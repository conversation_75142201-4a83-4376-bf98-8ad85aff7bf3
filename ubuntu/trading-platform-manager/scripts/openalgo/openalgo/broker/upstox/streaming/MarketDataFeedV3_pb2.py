# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: MarketDataFeedV3.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16MarketDataFeedV3.proto\x12,com.upstox.marketdatafeederv3udapi.rpc.proto\"9\n\x04LTPC\x12\x0b\n\x03ltp\x18\x01 \x01(\x01\x12\x0b\n\x03ltt\x18\x02 \x01(\x03\x12\x0b\n\x03ltq\x18\x03 \x01(\x03\x12\n\n\x02\x63p\x18\x04 \x01(\x01\"W\n\x0bMarketLevel\x12H\n\x0b\x62idAskQuote\x18\x01 \x03(\x0b\x32\x33.com.upstox.marketdatafeederv3udapi.rpc.proto.Quote\"N\n\nMarketOHLC\x12@\n\x04ohlc\x18\x01 \x03(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.OHLC\"?\n\x05Quote\x12\x0c\n\x04\x62idQ\x18\x01 \x01(\x03\x12\x0c\n\x04\x62idP\x18\x02 \x01(\x01\x12\x0c\n\x04\x61skQ\x18\x03 \x01(\x03\x12\x0c\n\x04\x61skP\x18\x04 \x01(\x01\"V\n\x0cOptionGreeks\x12\r\n\x05\x64\x65lta\x18\x01 \x01(\x01\x12\r\n\x05theta\x18\x02 \x01(\x01\x12\r\n\x05gamma\x18\x03 \x01(\x01\x12\x0c\n\x04vega\x18\x04 \x01(\x01\x12\x0b\n\x03rho\x18\x05 \x01(\x01\"i\n\x04OHLC\x12\x10\n\x08interval\x18\x01 \x01(\t\x12\x0c\n\x04open\x18\x02 \x01(\x01\x12\x0c\n\x04high\x18\x03 \x01(\x01\x12\x0b\n\x03low\x18\x04 \x01(\x01\x12\r\n\x05\x63lose\x18\x05 \x01(\x01\x12\x0b\n\x03vol\x18\x06 \x01(\x03\x12\n\n\x02ts\x18\x07 \x01(\x03\"\x8e\x03\n\x0eMarketFullFeed\x12@\n\x04ltpc\x18\x01 \x01(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.LTPC\x12N\n\x0bmarketLevel\x18\x02 \x01(\x0b\x32\x39.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketLevel\x12P\n\x0coptionGreeks\x18\x03 \x01(\x0b\x32:.com.upstox.marketdatafeederv3udapi.rpc.proto.OptionGreeks\x12L\n\nmarketOHLC\x18\x04 \x01(\x0b\x32\x38.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketOHLC\x12\x0b\n\x03\x61tp\x18\x05 \x01(\x01\x12\x0b\n\x03vtt\x18\x06 \x01(\x03\x12\n\n\x02oi\x18\x07 \x01(\x01\x12\n\n\x02iv\x18\x08 \x01(\x01\x12\x0b\n\x03tbq\x18\t \x01(\x01\x12\x0b\n\x03tsq\x18\n \x01(\x01\"\x9f\x01\n\rIndexFullFeed\x12@\n\x04ltpc\x18\x01 \x01(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.LTPC\x12L\n\nmarketOHLC\x18\x02 \x01(\x0b\x32\x38.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketOHLC\"\xbd\x01\n\x08\x46ullFeed\x12P\n\x08marketFF\x18\x01 \x01(\x0b\x32<.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketFullFeedH\x00\x12N\n\x07indexFF\x18\x02 \x01(\x0b\x32;.com.upstox.marketdatafeederv3udapi.rpc.proto.IndexFullFeedH\x00\x42\x0f\n\rFullFeedUnion\"\x98\x02\n\x14\x46irstLevelWithGreeks\x12@\n\x04ltpc\x18\x01 \x01(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.LTPC\x12G\n\nfirstDepth\x18\x02 \x01(\x0b\x32\x33.com.upstox.marketdatafeederv3udapi.rpc.proto.Quote\x12P\n\x0coptionGreeks\x18\x03 \x01(\x0b\x32:.com.upstox.marketdatafeederv3udapi.rpc.proto.OptionGreeks\x12\x0b\n\x03vtt\x18\x04 \x01(\x03\x12\n\n\x02oi\x18\x05 \x01(\x01\x12\n\n\x02iv\x18\x06 \x01(\x01\"\xd7\x02\n\x04\x46\x65\x65\x64\x12\x42\n\x04ltpc\x18\x01 \x01(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.LTPCH\x00\x12J\n\x08\x66ullFeed\x18\x02 \x01(\x0b\x32\x36.com.upstox.marketdatafeederv3udapi.rpc.proto.FullFeedH\x00\x12\x62\n\x14\x66irstLevelWithGreeks\x18\x03 \x01(\x0b\x32\x42.com.upstox.marketdatafeederv3udapi.rpc.proto.FirstLevelWithGreeksH\x00\x12N\n\x0brequestMode\x18\x04 \x01(\x0e\x32\x39.com.upstox.marketdatafeederv3udapi.rpc.proto.RequestModeB\x0b\n\tFeedUnion\"\xe2\x01\n\nMarketInfo\x12\x62\n\rsegmentStatus\x18\x01 \x03(\x0b\x32K.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketInfo.SegmentStatusEntry\x1ap\n\x12SegmentStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12I\n\x05value\x18\x02 \x01(\x0e\x32:.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketStatus:\x02\x38\x01\"\xe9\x02\n\x0c\x46\x65\x65\x64Response\x12@\n\x04type\x18\x01 \x01(\x0e\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.Type\x12T\n\x05\x66\x65\x65\x64s\x18\x02 \x03(\x0b\x32\x45.com.upstox.marketdatafeederv3udapi.rpc.proto.FeedResponse.FeedsEntry\x12\x11\n\tcurrentTs\x18\x03 \x01(\x03\x12L\n\nmarketInfo\x18\x04 \x01(\x0b\x32\x38.com.upstox.marketdatafeederv3udapi.rpc.proto.MarketInfo\x1a`\n\nFeedsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x41\n\x05value\x18\x02 \x01(\x0b\x32\x32.com.upstox.marketdatafeederv3udapi.rpc.proto.Feed:\x02\x38\x01*8\n\x04Type\x12\x10\n\x0cinitial_feed\x10\x00\x12\r\n\tlive_feed\x10\x01\x12\x0f\n\x0bmarket_info\x10\x02*E\n\x0bRequestMode\x12\x08\n\x04ltpc\x10\x00\x12\x0b\n\x07\x66ull_d5\x10\x01\x12\x11\n\roption_greeks\x10\x02\x12\x0c\n\x08\x66ull_d30\x10\x03*{\n\x0cMarketStatus\x12\x12\n\x0ePRE_OPEN_START\x10\x00\x12\x10\n\x0cPRE_OPEN_END\x10\x01\x12\x0f\n\x0bNORMAL_OPEN\x10\x02\x12\x10\n\x0cNORMAL_CLOSE\x10\x03\x12\x11\n\rCLOSING_START\x10\x04\x12\x0f\n\x0b\x43LOSING_END\x10\x05\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'MarketDataFeedV3_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MARKETINFO_SEGMENTSTATUSENTRY._options = None
  _MARKETINFO_SEGMENTSTATUSENTRY._serialized_options = b'8\001'
  _FEEDRESPONSE_FEEDSENTRY._options = None
  _FEEDRESPONSE_FEEDSENTRY._serialized_options = b'8\001'
  _TYPE._serialized_start=2537
  _TYPE._serialized_end=2593
  _REQUESTMODE._serialized_start=2595
  _REQUESTMODE._serialized_end=2664
  _MARKETSTATUS._serialized_start=2666
  _MARKETSTATUS._serialized_end=2789
  _LTPC._serialized_start=72
  _LTPC._serialized_end=129
  _MARKETLEVEL._serialized_start=131
  _MARKETLEVEL._serialized_end=218
  _MARKETOHLC._serialized_start=220
  _MARKETOHLC._serialized_end=298
  _QUOTE._serialized_start=300
  _QUOTE._serialized_end=363
  _OPTIONGREEKS._serialized_start=365
  _OPTIONGREEKS._serialized_end=451
  _OHLC._serialized_start=453
  _OHLC._serialized_end=558
  _MARKETFULLFEED._serialized_start=561
  _MARKETFULLFEED._serialized_end=959
  _INDEXFULLFEED._serialized_start=962
  _INDEXFULLFEED._serialized_end=1121
  _FULLFEED._serialized_start=1124
  _FULLFEED._serialized_end=1313
  _FIRSTLEVELWITHGREEKS._serialized_start=1316
  _FIRSTLEVELWITHGREEKS._serialized_end=1596
  _FEED._serialized_start=1599
  _FEED._serialized_end=1942
  _MARKETINFO._serialized_start=1945
  _MARKETINFO._serialized_end=2171
  _MARKETINFO_SEGMENTSTATUSENTRY._serialized_start=2059
  _MARKETINFO_SEGMENTSTATUSENTRY._serialized_end=2171
  _FEEDRESPONSE._serialized_start=2174
  _FEEDRESPONSE._serialized_end=2535
  _FEEDRESPONSE_FEEDSENTRY._serialized_start=2439
  _FEEDRESPONSE_FEEDSENTRY._serialized_end=2535
# @@protoc_insertion_point(module_scope)
