<style>
/* Custom styles that extend daisyUI */

/* Card styling */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid hsl(var(--b2));
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* Badge styling with gradients */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.badge-function {
    background: linear-gradient(135deg, hsl(var(--p)) 0%, hsl(var(--pf)) 100%);
    color: hsl(var(--pc));
    border: none;
}

.badge.badge-strategy {
    background: linear-gradient(135deg, hsl(var(--s)) 0%, hsl(var(--sf)) 100%);
    color: hsl(var(--sc));
    border: none;
}

.badge.badge-buy {
    background: linear-gradient(135deg, hsl(var(--su)) 0%, hsl(142, 76%, 36%) 100%);
    color: hsl(var(--suc));
    border: none;
}

.badge.badge-sell {
    background: linear-gradient(135deg, hsl(var(--er)) 0%, hsl(0, 74%, 42%) 100%);
    color: hsl(var(--erc));
    border: none;
}

.badge.badge-exchange {
    background: linear-gradient(135deg, hsl(var(--a)) 0%, hsl(var(--af)) 100%);
    color: hsl(var(--ac));
    border: none;
}

/* Stats styling */
.stat {
    background: hsl(var(--b2));
    padding: 1rem;
    border-radius: var(--rounded-box, 1rem);
    transition: all 0.2s ease;
}

.stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stat-title {
    color: hsl(var(--bc) / 0.7);
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-value {
    color: hsl(var(--bc));
    font-size: 1.25rem;
    line-height: 1.75rem;
}

/* JSON content styling */
.json-content {
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    background: hsl(var(--b1));
    border-radius: 0.5rem;
}

.json-content pre {
    margin: 0;
    padding: 1rem;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    color: hsl(var(--bc));
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Custom scrollbar */
.json-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.json-content::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;
}

.json-content::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.5);
}

/* Collapse styling */
.collapse {
    transition: all 0.3s ease;
}

.collapse-title {
    font-weight: 600;
    color: hsl(var(--bc));
}

.collapse-content {
    background: hsl(var(--b2));
    border-radius: 0 0 var(--rounded-box, 1rem) var(--rounded-box, 1rem);
}

/* Icon styling */
.api-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.375rem;
}

/* Animation for collapse content */
.collapse-content {
    transition: padding 0.3s ease-in-out;
}

.peer:checked ~ .collapse-content {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
    .badge {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1rem;
    }
}
</style>
