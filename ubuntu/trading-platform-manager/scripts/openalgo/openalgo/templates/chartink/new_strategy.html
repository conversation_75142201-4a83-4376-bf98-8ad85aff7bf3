{% extends "base.html" %}

{% block title %}New Chartink Strategy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">New Chartink Strategy</h1>
        <a href="{{ url_for('chartink_bp.index') }}" class="btn">
            Back to Strategies
        </a>
    </div>

    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <form method="POST" class="space-y-6" id="strategyForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Strategy Name</span>
                        <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="join w-full">
                        <div class="join-item flex items-center px-4 bg-base-200 border border-r-0 rounded-l-lg">
                            chartink_
                        </div>
                        <input type="text" name="name" class="input input-bordered join-item rounded-l-none w-full" required 
                               placeholder="Enter strategy name"
                               pattern="[A-Za-z0-9\s\-_]+"
                               title="Only letters, numbers, spaces, hyphens and underscores allowed">
                    </div>
                    <label class="label">
                        <span class="label-text-alt">Strategy name will be prefixed with 'chartink_'</span>
                    </label>
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Strategy Type</span>
                        <span class="label-text-alt text-error">*</span>
                    </label>
                    <select name="type" class="select select-bordered" id="strategyType" required>
                        <option value="intraday">Intraday</option>
                        <option value="positional">Positional</option>
                    </select>
                    <label class="label">
                        <span class="label-text-alt">Intraday strategies have time-based controls</span>
                    </label>
                </div>

                <div id="intradaySettings" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Start Time</span>
                                <span class="label-text-alt text-error">*</span>
                            </label>
                            <input type="time" name="start_time" class="input input-bordered"
                                   value="09:15">
                            <label class="label">
                                <span class="label-text-alt">When to start taking trades</span>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">End Time</span>
                                <span class="label-text-alt text-error">*</span>
                            </label>
                            <input type="time" name="end_time" class="input input-bordered"
                                   value="15:00">
                            <label class="label">
                                <span class="label-text-alt">Last trade entry time</span>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Square Off Time</span>
                                <span class="label-text-alt text-error">*</span>
                            </label>
                            <input type="time" name="squareoff_time" class="input input-bordered"
                                   value="15:15">
                            <label class="label">
                                <span class="label-text-alt">Auto square off time</span>
                            </label>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <div>
                            <h3 class="font-bold">Time Settings</h3>
                            <ul class="list-disc list-inside text-sm mt-2">
                                <li>Orders will be placed between start and end time</li>
                                <li>No new positions will be taken after end time</li>
                                <li>All open positions will be squared off at square off time</li>
                                <li>Square off time must be after end time</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-actions justify-end">
                    <button type="reset" class="btn">Reset</button>
                    <button type="submit" class="btn btn-primary">Create Strategy</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const strategyType = document.getElementById('strategyType');
    const intradaySettings = document.getElementById('intradaySettings');
    const timeInputs = intradaySettings.querySelectorAll('input[type="time"]');
    const form = document.getElementById('strategyForm');

    function toggleIntradaySettings() {
        if (strategyType.value === 'intraday') {
            intradaySettings.classList.remove('hidden');
            timeInputs.forEach(input => input.required = true);
        } else {
            intradaySettings.classList.add('hidden');
            timeInputs.forEach(input => {
                input.required = false;
                input.value = '';
            });
        }
    }

    strategyType.addEventListener('change', toggleIntradaySettings);
    toggleIntradaySettings();

    // Form validation
    form.addEventListener('submit', function(e) {
        if (strategyType.value === 'intraday') {
            const start = timeInputs[0].value;
            const end = timeInputs[1].value;
            const squareoff = timeInputs[2].value;
            
            if (start >= end) {
                e.preventDefault();
                showAlert('error', 'Start time must be before end time');
                return;
            }

            if (end >= squareoff) {
                e.preventDefault();
                showAlert('error', 'End time must be before square off time');
                return;
            }
        }
    });
});

function showAlert(type, message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-' + type + ' fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-auto';
    alert.innerHTML = `<span>${message}</span>`;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 3000);
}
</script>
{% endblock %}
{% endblock %}
