{% extends "layout.html" %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-lg mx-auto">
            <div class="card shadow-2xl bg-base-100">
                <div class="card-body">
                    <h2 class="card-title text-2xl mb-4">Reset Password</h2>

                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                    </svg>
                                    <span>{{ message }}</span>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% if not email_sent %}
                    <!-- Step 1: Email Form -->
                    <form id="emailForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="email">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Email Address</span>
                            </label>
                            <input type="email" 
                                   name="email" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Enter your email">
                            <label class="label">
                                <span class="label-text-alt">Enter the email address associated with your account</span>
                            </label>
                        </div>
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Continue</button>
                        </div>
                    </form>
                    {% elif not totp_verified %}
                    <!-- Step 2: TOTP Verification -->
                    <form id="totpForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="totp">
                        <input type="hidden" name="email" value="{{ email }}">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">TOTP Code</span>
                            </label>
                            <input type="text" 
                                   name="totp_code" 
                                   required 
                                   pattern="[0-9]{6}"
                                   maxlength="6"
                                   class="input input-bordered" 
                                   placeholder="Enter 6-digit code">
                            <label class="label">
                                <span class="label-text-alt">Enter the 6-digit code from your authenticator app</span>
                            </label>
                        </div>
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Verify Code</button>
                        </div>
                    </form>
                    {% else %}
                    <!-- Step 3: New Password -->
                    <form id="passwordForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="password">
                        <input type="hidden" name="email" value="{{ email }}">
                        <input type="hidden" name="token" value="{{ token }}">
                        
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">New Password</span>
                            </label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Enter new password">
                            <label class="label">
                                <span class="label-text-alt">Password must be at least 8 characters long</span>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Confirm New Password</span>
                            </label>
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Confirm new password">
                        </div>

                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Reset Password</button>
                        </div>
                    </form>
                    {% endif %}

                    <div class="divider">OR</div>

                    <div class="text-center">
                        <a href="{{ url_for('auth.login') }}" class="link link-hover">Back to Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long!');
                return;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
            }
        });
    }

    // Auto-focus on input fields
    const emailInput = document.querySelector('input[type="email"]');
    const totpInput = document.querySelector('input[name="totp_code"]');
    const passwordInput = document.getElementById('password');

    if (emailInput) emailInput.focus();
    if (totpInput) totpInput.focus();
    if (passwordInput) passwordInput.focus();
});
</script>
{% endblock %}
