{% extends "layout.html" %}

{% block title %}Setup TOTP Authentication{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-lg mx-auto">
            <div class="card shadow-2xl bg-base-100">
                <div class="card-body">
                    <h2 class="card-title text-2xl mb-4">Setup TOTP Authentication</h2>
                    
                    <div class="alert alert-info mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-bold">Important!</h3>
                            <div class="text-sm">Scan this QR code with your authenticator app. You'll need it to reset your password if you forget it.</div>
                        </div>
                    </div>

                    <!-- QR Code -->
                    <div class="flex justify-center mb-6">
                        <div class="p-4 bg-white rounded-lg">
                            <img src="data:image/png;base64,{{ qr_code }}" alt="TOTP QR Code" class="w-48 h-48">
                        </div>
                    </div>

                    <!-- Manual Entry Section -->
                    <div class="bg-base-200 p-4 rounded-lg mb-6">
                        <h3 class="font-semibold mb-2">Manual Entry</h3>
                        <p class="text-sm mb-2">If you can't scan the QR code, enter this secret key manually in your authenticator app:</p>
                        <div class="flex items-center gap-2">
                            <code class="flex-1 bg-base-300 p-2 rounded select-all">{{ secret }}</code>
                            <button onclick="copyToClipboard('{{ secret }}')" class="btn btn-square btn-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="space-y-2 mb-6">
                        <p class="font-medium">Setup Instructions:</p>
                        <ol class="list-decimal list-inside space-y-1 text-sm">
                            <li>Install an authenticator app (Google Authenticator, Authy, etc.)</li>
                            <li>Scan the QR code or enter the secret key manually</li>
                            <li>Store your backup codes in a safe place</li>
                            <li>You'll need the TOTP code to reset your password if you forget it</li>
                        </ol>
                    </div>

                    <!-- Continue Button -->
                    <div class="form-control mt-4">
                        <a href="{{ url_for('auth.login') }}" class="btn btn-primary">
                            Continue to Login
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Show toast or notification
        alert('Secret copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
}
</script>
{% endblock %}
