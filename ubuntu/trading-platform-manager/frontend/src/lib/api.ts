const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: 'user' | 'admin' | 'super_admin';
  is_active: boolean;
  created_at: string;
  instance_limit: number;
  parent_admin_id?: number;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
  expires_in: number;
}

export interface ApiError {
  detail: string;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    // Try to get token from localStorage on initialization
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          detail: `HTTP ${response.status}: ${response.statusText}`
        }));
        throw new Error(errorData.detail || 'An error occurred');
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    this.setToken(response.access_token);
    
    // Store user data
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(response.user));
    }
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.request('/api/auth/logout', {
        method: 'POST',
      });
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/api/auth/me');
  }

  async verifyToken(): Promise<{ valid: boolean; user: User }> {
    return this.request<{ valid: boolean; user: User }>('/api/auth/verify-token');
  }

  // Platform configuration
  async getConfig(): Promise<any> {
    return this.request('/api/config');
  }

  // Health check
  async healthCheck(): Promise<{ status: string; platform: string }> {
    return this.request('/health');
  }

  // User Management (Hierarchical)
  async createUser(userData: any): Promise<any> {
    return this.request('/api/user-management/create-user', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getMyUsers(): Promise<any[]> {
    return this.request('/api/user-management/my-users');
  }

  async updateUser(userId: number, userData: any): Promise<any> {
    return this.request(`/api/user-management/update-user/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async resetUserPassword(userId: number, newPassword: string): Promise<any> {
    return this.request('/api/user-management/reset-password', {
      method: 'POST',
      body: JSON.stringify({ user_id: userId, new_password: newPassword }),
    });
  }

  async deleteUser(userId: number): Promise<any> {
    return this.request(`/api/user-management/delete-user/${userId}`, {
      method: 'DELETE',
    });
  }

  // Broker Instances (Hierarchical)
  async getUserBrokerInstances(): Promise<any[]> {
    return this.request('/api/user/broker-instances');
  }

  async getAdminBrokerInstances(): Promise<any[]> {
    return this.request('/api/admin/broker-instances');
  }

  async createBrokerInstance(instanceData: any): Promise<any> {
    const endpoint = this.user?.role === 'admin'
      ? '/api/admin/broker-instances'
      : '/api/user/broker-instances';
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(instanceData),
    });
  }

  // Strategies (Hierarchical)
  async getUserStrategies(): Promise<any[]> {
    return this.request('/api/user/strategies');
  }

  async getAdminStrategies(): Promise<any[]> {
    return this.request('/api/admin/strategies');
  }

  async createStrategy(strategyData: any): Promise<any> {
    const endpoint = this.user?.role === 'admin'
      ? '/api/admin/strategies'
      : '/api/user/strategies';
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(strategyData),
    });
  }

  async updateStrategy(strategyId: number, strategyData: any): Promise<any> {
    const endpoint = this.user?.role === 'admin'
      ? `/api/admin/strategies/${strategyId}`
      : `/api/user/strategies/${strategyId}`;
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(strategyData),
    });
  }

  async deleteStrategy(strategyId: number): Promise<any> {
    const endpoint = this.user?.role === 'admin'
      ? `/api/admin/strategies/${strategyId}`
      : `/api/user/strategies/${strategyId}`;
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }

  // Revenue and Analytics
  async getRevenueData(): Promise<any> {
    return this.request('/api/revenue/analytics');
  }

  async getReferralData(): Promise<any> {
    return this.request('/api/revenue/referrals');
  }
}

export const apiClient = new ApiClient();
export default apiClient;
