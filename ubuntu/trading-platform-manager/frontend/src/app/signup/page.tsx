'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, UserPlus } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

export default function SignupPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [referralCode, setReferralCode] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    referral_code: ''
  });
  const [generatedCredentials, setGeneratedCredentials] = useState<{
    username: string;
    password: string;
  } | null>(null);

  useEffect(() => {
    // Get referral code from URL
    const refCode = searchParams.get('ref');
    if (refCode) {
      setReferralCode(refCode);
      setFormData(prev => ({ ...prev, referral_code: refCode }));
    }
  }, [searchParams]);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.full_name) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const response = await api.publicSignup({
        email: formData.email,
        full_name: formData.full_name,
        referral_code: formData.referral_code || null
      });

      // Extract username from response and generate password
      const username = response.username;
      const password = `${username}123!`;

      setGeneratedCredentials({ username, password });
      setSuccess(true);
      toast.success('Account created successfully!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to create account';
      toast.error(errorMessage);
      console.error('Signup error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginRedirect = () => {
    router.push('/user/login');
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-2xl">Account Created Successfully!</CardTitle>
            <CardDescription>
              Your trading account has been set up and is ready to use.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Please save your login credentials securely.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-3 p-4 bg-muted rounded-lg">
              <div>
                <Label className="text-sm font-medium">Username</Label>
                <div className="font-mono text-lg font-bold">{generatedCredentials?.username}</div>
              </div>
              <div>
                <Label className="text-sm font-medium">Password</Label>
                <div className="font-mono text-lg font-bold">{generatedCredentials?.password}</div>
              </div>
            </div>

            <div className="text-sm text-muted-foreground space-y-2">
              <p>• Your account has been assigned to an admin who will manage your resources</p>
              <p>• You can create up to 5 broker instances and 10 strategies</p>
              <p>• Change your password after first login for security</p>
            </div>

            <Button onClick={handleLoginRedirect} className="w-full" size="lg">
              Continue to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <UserPlus className="h-6 w-6 text-primary" />
          </div>
          <CardTitle className="text-2xl">Create Your Trading Account</CardTitle>
          <CardDescription>
            Join our platform and start your automated trading journey
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignup} className="space-y-4">
            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="full_name">Full Name *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder="John Doe"
                required
              />
            </div>

            <div>
              <Label htmlFor="referral_code">Referral Code (Optional)</Label>
              <Input
                id="referral_code"
                value={formData.referral_code}
                onChange={(e) => setFormData({ ...formData, referral_code: e.target.value })}
                placeholder="Enter referral code if you have one"
                disabled={!!referralCode} // Disable if came from referral link
              />
              {referralCode && (
                <p className="text-sm text-green-600 mt-1">
                  ✓ Using referral code from link
                </p>
              )}
              {!formData.referral_code && (
                <p className="text-sm text-muted-foreground mt-1">
                  No referral code? You'll be assigned to our default admin.
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" size="lg" disabled={loading}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{' '}
              <Button variant="link" className="p-0 h-auto" onClick={() => router.push('/user/login')}>
                Sign in here
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
