'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DollarSign, Users, TrendingUp, Calendar, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface RevenueDetail {
  user_id: number;
  username: string;
  full_name: string;
  total_revenue: number;
  monthly_revenue: number;
  recent_transactions: Transaction[];
}

interface Transaction {
  id: number;
  amount: number;
  admin_share: number;
  transaction_type: string;
  date: string;
  billing_month: number;
  billing_year: number;
}

interface RevenueData {
  total_revenue: number;
  monthly_revenue: number;
  total_users: number;
  user_details: RevenueDetail[];
}

export default function AdminRevenuePage() {
  const [revenueData, setRevenueData] = useState<RevenueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(30);

  useEffect(() => {
    loadRevenueData();
  }, []);

  const loadRevenueData = async () => {
    try {
      setLoading(true);
      const data = await api.request('/api/user-management/revenue-details');
      setRevenueData(data);
    } catch (error) {
      console.error('Error loading revenue data:', error);
      toast.error('Failed to load revenue data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading revenue data...</p>
        </div>
      </div>
    );
  }

  if (!revenueData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-muted-foreground">No revenue data available</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout allowedRoles={['admin']}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Revenue Dashboard</h1>
          <p className="text-muted-foreground">Track your earnings from managed users</p>
        </div>

      {/* Revenue Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(revenueData.total_revenue)}</div>
            <p className="text-xs text-muted-foreground">All time earnings</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(revenueData.monthly_revenue)}</div>
            <p className="text-xs text-muted-foreground">Current month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{revenueData.total_users}</div>
            <p className="text-xs text-muted-foreground">Managed users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Revenue/User</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(revenueData.total_users > 0 ? revenueData.total_revenue / revenueData.total_users : 0)}
            </div>
            <p className="text-xs text-muted-foreground">Per user</p>
          </CardContent>
        </Card>
      </div>

      {/* User Revenue Details */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue by User</CardTitle>
          <CardDescription>
            Detailed breakdown of revenue from each managed user
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Total Revenue</TableHead>
                <TableHead>Monthly Revenue</TableHead>
                <TableHead>Recent Activity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {revenueData.user_details.map((user) => (
                <TableRow key={user.user_id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.full_name}</div>
                      <div className="text-sm text-muted-foreground">{user.username}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{formatCurrency(user.total_revenue)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{formatCurrency(user.monthly_revenue)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {user.recent_transactions.slice(0, 3).map((transaction) => (
                        <div key={transaction.id} className="text-xs">
                          <Badge variant="outline" className="mr-2">
                            {transaction.transaction_type}
                          </Badge>
                          {formatCurrency(transaction.admin_share)}
                          <span className="text-muted-foreground ml-2">
                            {formatDate(transaction.date)}
                          </span>
                        </div>
                      ))}
                      {user.recent_transactions.length === 0 && (
                        <div className="text-xs text-muted-foreground">No transactions yet</div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>
            Latest revenue transactions from all users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Total Amount</TableHead>
                <TableHead>Your Share</TableHead>
                <TableHead>Period</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {revenueData.user_details
                .flatMap(user => 
                  user.recent_transactions.map(transaction => ({
                    ...transaction,
                    user_name: user.full_name,
                    username: user.username
                  }))
                )
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .slice(0, 10)
                .map((transaction) => (
                  <TableRow key={`${transaction.id}-${transaction.user_name}`}>
                    <TableCell>
                      <div className="text-sm">{formatDate(transaction.date)}</div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{transaction.user_name}</div>
                        <div className="text-sm text-muted-foreground">{transaction.username}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{transaction.transaction_type}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{formatCurrency(transaction.amount)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium text-green-600">
                        {formatCurrency(transaction.admin_share)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {transaction.billing_month}/{transaction.billing_year}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      </div>
    </DashboardLayout>
  );
}
