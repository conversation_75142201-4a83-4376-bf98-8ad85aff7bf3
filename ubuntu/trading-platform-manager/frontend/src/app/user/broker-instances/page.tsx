'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Plus, Play, Square, RotateCcw, Trash2, ExternalLink } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface BrokerInstance {
  id: number;
  name: string;
  domain: string;
  subdomain: string;
  broker: string;
  status: string;
  port: number;
  ssl_enabled: boolean;
  created_at: string;
  last_started: string;
  last_stopped: string;
}

export default function UserBrokerInstancesPage() {
  const [instances, setInstances] = useState<BrokerInstance[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadInstances();
  }, []);

  const loadInstances = async () => {
    try {
      setLoading(true);
      const data = await api.getUserBrokerInstances();
      setInstances(data);
    } catch (error) {
      toast.error('Failed to load broker instances');
      console.error('Error loading instances:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return 'default';
      case 'stopped': return 'secondary';
      case 'error': return 'destructive';
      case 'creating': return 'outline';
      case 'installing': return 'outline';
      default: return 'secondary';
    }
  };

  const handleInstanceAction = async (instanceId: number, action: string) => {
    try {
      // This would call the instance action API
      toast.success(`Instance ${action} initiated`);
      loadInstances();
    } catch (error) {
      toast.error(`Failed to ${action} instance`);
      console.error(`Error ${action} instance:`, error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading broker instances...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Broker Instances</h1>
          <p className="text-muted-foreground">Manage your trading broker instances</p>
        </div>
        
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Instance
        </Button>
      </div>

      {/* Instance Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Instances</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{instances.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Running</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {instances.filter(i => i.status === 'running').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stopped</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {instances.filter(i => i.status === 'stopped').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {instances.filter(i => i.status === 'error').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Instances Table */}
      <Card>
        <CardHeader>
          <CardTitle>Broker Instances</CardTitle>
          <CardDescription>Your active trading broker instances</CardDescription>
        </CardHeader>
        <CardContent>
          {instances.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No broker instances found</p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Instance
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Instance</TableHead>
                  <TableHead>Broker</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Domain</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {instances.map((instance) => (
                  <TableRow key={instance.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{instance.name}</div>
                        <div className="text-sm text-muted-foreground">{instance.subdomain}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{instance.broker}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusColor(instance.status)}>
                        {instance.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{instance.domain}</span>
                        {instance.status === 'running' && (
                          <Button variant="ghost" size="sm">
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(instance.created_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {instance.status === 'stopped' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleInstanceAction(instance.id, 'start')}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                        {instance.status === 'running' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleInstanceAction(instance.id, 'stop')}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleInstanceAction(instance.id, 'restart')}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleInstanceAction(instance.id, 'delete')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
