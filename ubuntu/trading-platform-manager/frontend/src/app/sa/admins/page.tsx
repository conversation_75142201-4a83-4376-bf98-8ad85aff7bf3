'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Plus, Edit, Trash2, Key, Users, TrendingUp, Server, ChevronDown, Shield, Settings } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface Admin {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  broker_instance_limit: number;
  strategy_limit: number;
  used_broker_instances: number;
  used_strategies: number;
  revenue_share_percentage: number;
  total_revenue_earned: number;
  referral_code: string;
  created_at: string;
  managed_users_count: number;
  total_user_instances: number;
  total_user_strategies: number;
  // Permissions
  can_create_users: boolean;
  can_edit_users: boolean;
  can_delete_users: boolean;
  can_view_users: boolean;
  can_manage_strategies: boolean;
  can_manage_instances: boolean;
  can_reset_passwords: boolean;
}

export default function SuperAdminAdminsPage() {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const [selectedAdmins, setSelectedAdmins] = useState<number[]>([]);
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    broker_instance_limit: 100,
    strategy_limit: 100,
    revenue_share_percentage: 15
  });
  const [permissions, setPermissions] = useState({
    can_create_users: true,
    can_edit_users: true,
    can_delete_users: false,
    can_view_users: true,
    can_manage_strategies: true,
    can_manage_instances: true,
    can_reset_passwords: true
  });
  const [newPassword, setNewPassword] = useState('');

  useEffect(() => {
    loadAdmins();
  }, []);

  const loadAdmins = async () => {
    try {
      setLoading(true);
      const data = await api.getMyUsers();
      // Filter only admins
      setAdmins(data.filter((user: any) => user.role === 'admin'));
    } catch (error) {
      toast.error('Failed to load admins');
      console.error('Error loading admins:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAdmin = async () => {
    try {
      await api.createUser({
        ...formData,
        role: 'admin',
        permissions
      });
      toast.success('Admin created successfully');
      setShowCreateDialog(false);
      setFormData({
        email: '',
        full_name: '',
        broker_instance_limit: 100,
        strategy_limit: 100,
        revenue_share_percentage: 15
      });
      setPermissions({
        can_create_users: true,
        can_edit_users: true,
        can_delete_users: false,
        can_view_users: true,
        can_manage_strategies: true,
        can_manage_instances: true,
        can_reset_passwords: true
      });
      loadAdmins();
    } catch (error) {
      toast.error('Failed to create admin');
      console.error('Error creating admin:', error);
    }
  };

  const handleEditAdmin = async () => {
    if (!selectedAdmin) return;
    
    try {
      await api.updateUser(selectedAdmin.id, formData);
      toast.success('Admin updated successfully');
      setShowEditDialog(false);
      setSelectedAdmin(null);
      loadAdmins();
    } catch (error) {
      toast.error('Failed to update admin');
      console.error('Error updating admin:', error);
    }
  };

  const handleUpdatePermissions = async () => {
    if (!selectedAdmin) return;
    
    try {
      await api.updateUser(selectedAdmin.id, { permissions });
      toast.success('Permissions updated successfully');
      setShowPermissionsDialog(false);
      setSelectedAdmin(null);
      loadAdmins();
    } catch (error) {
      toast.error('Failed to update permissions');
      console.error('Error updating permissions:', error);
    }
  };

  const handleResetPassword = async () => {
    if (!selectedAdmin || !newPassword) return;
    
    try {
      await api.resetUserPassword(selectedAdmin.id, newPassword);
      toast.success('Password reset successfully');
      setShowPasswordDialog(false);
      setSelectedAdmin(null);
      setNewPassword('');
    } catch (error) {
      toast.error('Failed to reset password');
      console.error('Error resetting password:', error);
    }
  };

  const handleDeleteAdmin = async (adminId: number) => {
    if (!confirm('Are you sure you want to delete this admin? This will affect all their managed users.')) return;
    
    try {
      await api.deleteUser(adminId);
      toast.success('Admin deleted successfully');
      loadAdmins();
    } catch (error) {
      toast.error('Failed to delete admin');
      console.error('Error deleting admin:', error);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedAdmins.length === 0) {
      toast.error('Please select admins first');
      return;
    }

    try {
      // This would call the bulk action API
      toast.success(`Bulk ${action} initiated for ${selectedAdmins.length} admins`);
      setSelectedAdmins([]);
      loadAdmins();
    } catch (error) {
      toast.error(`Failed to perform bulk ${action}`);
      console.error(`Error performing bulk ${action}:`, error);
    }
  };

  const toggleAdminSelection = (adminId: number) => {
    setSelectedAdmins(prev => 
      prev.includes(adminId) 
        ? prev.filter(id => id !== adminId)
        : [...prev, adminId]
    );
  };

  const toggleSelectAll = () => {
    setSelectedAdmins(
      selectedAdmins.length === admins.length 
        ? [] 
        : admins.map(a => a.id)
    );
  };

  const openEditDialog = (admin: Admin) => {
    setSelectedAdmin(admin);
    setFormData({
      email: admin.email,
      full_name: admin.full_name,
      broker_instance_limit: admin.broker_instance_limit,
      strategy_limit: admin.strategy_limit,
      revenue_share_percentage: admin.revenue_share_percentage
    });
    setShowEditDialog(true);
  };

  const openPermissionsDialog = (admin: Admin) => {
    setSelectedAdmin(admin);
    setPermissions({
      can_create_users: admin.can_create_users,
      can_edit_users: admin.can_edit_users,
      can_delete_users: admin.can_delete_users,
      can_view_users: admin.can_view_users,
      can_manage_strategies: admin.can_manage_strategies,
      can_manage_instances: admin.can_manage_instances,
      can_reset_passwords: admin.can_reset_passwords
    });
    setShowPermissionsDialog(true);
  };

  const openPasswordDialog = (admin: Admin) => {
    setSelectedAdmin(admin);
    setNewPassword('');
    setShowPasswordDialog(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading admins...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Management</h1>
          <p className="text-muted-foreground">Manage system administrators and their permissions</p>
        </div>
        
        <div className="flex items-center gap-2">
          {selectedAdmins.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Bulk Actions ({selectedAdmins.length})
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                  Activate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                  Deactivate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('reset-permissions')}>
                  Reset Permissions
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('delete')}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Admin
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Admin</DialogTitle>
                <DialogDescription>
                  Create a new administrator with specified quotas and permissions
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="full_name">Full Name</Label>
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                      placeholder="John Doe"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="broker_instance_limit">Broker Instance Limit</Label>
                    <Input
                      id="broker_instance_limit"
                      type="number"
                      value={formData.broker_instance_limit}
                      onChange={(e) => setFormData({...formData, broker_instance_limit: parseInt(e.target.value)})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="strategy_limit">Strategy Limit</Label>
                    <Input
                      id="strategy_limit"
                      type="number"
                      value={formData.strategy_limit}
                      onChange={(e) => setFormData({...formData, strategy_limit: parseInt(e.target.value)})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="revenue_share">Revenue Share (%)</Label>
                    <Input
                      id="revenue_share"
                      type="number"
                      step="0.1"
                      value={formData.revenue_share_percentage}
                      onChange={(e) => setFormData({...formData, revenue_share_percentage: parseFloat(e.target.value)})}
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-base font-medium">Permissions</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    {Object.entries(permissions).map(([key, value]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Checkbox
                          id={key}
                          checked={value}
                          onCheckedChange={(checked) => 
                            setPermissions(prev => ({...prev, [key]: checked}))
                          }
                        />
                        <Label htmlFor={key} className="text-sm">
                          {key.replace(/_/g, ' ').replace(/^can /, '').replace(/\b\w/g, l => l.toUpperCase())}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Button onClick={handleCreateAdmin} className="w-full">
                  Create Admin
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Admin Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Admins</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{admins.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Admins</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{admins.filter(a => a.is_active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Managed Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{admins.reduce((sum, a) => sum + (a.managed_users_count || 0), 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${admins.reduce((sum, a) => sum + a.total_revenue_earned, 0).toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Admins Table */}
      <Card>
        <CardHeader>
          <CardTitle>System Administrators</CardTitle>
          <CardDescription>Manage administrators and their permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedAdmins.length === admins.length}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Quotas</TableHead>
                <TableHead>Managed Users</TableHead>
                <TableHead>Revenue Share</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {admins.map((admin) => (
                <TableRow key={admin.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedAdmins.includes(admin.id)}
                      onCheckedChange={() => toggleAdminSelection(admin.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{admin.full_name}</div>
                      <div className="text-sm text-muted-foreground">{admin.username}</div>
                      <div className="text-sm text-muted-foreground">{admin.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={admin.is_active ? "default" : "secondary"}>
                      {admin.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>Instances: {admin.broker_instance_limit}</div>
                      <div>Strategies: {admin.strategy_limit}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>Users: {admin.managed_users_count || 0}</div>
                      <div>Instances: {admin.total_user_instances || 0}</div>
                      <div>Strategies: {admin.total_user_strategies || 0}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{admin.revenue_share_percentage}%</div>
                      <div className="text-muted-foreground">${admin.total_revenue_earned.toFixed(2)}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(admin)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openPermissionsDialog(admin)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openPasswordDialog(admin)}
                      >
                        <Key className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteAdmin(admin.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Admin Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Admin</DialogTitle>
            <DialogDescription>
              Update admin information and quotas
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_email">Email</Label>
              <Input
                id="edit_email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="edit_full_name">Full Name</Label>
              <Input
                id="edit_full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({...formData, full_name: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit_broker_instance_limit">Broker Instance Limit</Label>
                <Input
                  id="edit_broker_instance_limit"
                  type="number"
                  value={formData.broker_instance_limit}
                  onChange={(e) => setFormData({...formData, broker_instance_limit: parseInt(e.target.value)})}
                />
              </div>
              <div>
                <Label htmlFor="edit_strategy_limit">Strategy Limit</Label>
                <Input
                  id="edit_strategy_limit"
                  type="number"
                  value={formData.strategy_limit}
                  onChange={(e) => setFormData({...formData, strategy_limit: parseInt(e.target.value)})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="edit_revenue_share">Revenue Share (%)</Label>
              <Input
                id="edit_revenue_share"
                type="number"
                step="0.1"
                value={formData.revenue_share_percentage}
                onChange={(e) => setFormData({...formData, revenue_share_percentage: parseFloat(e.target.value)})}
              />
            </div>
            <Button onClick={handleEditAdmin} className="w-full">
              Update Admin
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Permissions Dialog */}
      <Dialog open={showPermissionsDialog} onOpenChange={setShowPermissionsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage Permissions</DialogTitle>
            <DialogDescription>
              Configure permissions for {selectedAdmin?.full_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {Object.entries(permissions).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2">
                  <Checkbox
                    id={`perm_${key}`}
                    checked={value}
                    onCheckedChange={(checked) => 
                      setPermissions(prev => ({...prev, [key]: checked}))
                    }
                  />
                  <Label htmlFor={`perm_${key}`} className="text-sm">
                    {key.replace(/_/g, ' ').replace(/^can /, '').replace(/\b\w/g, l => l.toUpperCase())}
                  </Label>
                </div>
              ))}
            </div>
            <Button onClick={handleUpdatePermissions} className="w-full">
              Update Permissions
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Set a new password for {selectedAdmin?.full_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new_password">New Password</Label>
              <Input
                id="new_password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter new password"
              />
            </div>
            <Button onClick={handleResetPassword} className="w-full">
              Reset Password
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
