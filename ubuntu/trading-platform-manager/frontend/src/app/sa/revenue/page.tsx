'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DollarSign, Users, TrendingUp, Calendar, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface AdminRevenue {
  admin_id: number;
  admin_username: string;
  admin_full_name: string;
  total_revenue: number;
  monthly_revenue: number;
  admin_share: number;
  super_admin_share: number;
  user_count: number;
  recent_transactions: Transaction[];
}

interface Transaction {
  id: number;
  amount: number;
  admin_share: number;
  super_admin_share: number;
  transaction_type: string;
  date: string;
  billing_month: number;
  billing_year: number;
  user_name: string;
  username: string;
}

interface SuperAdminRevenueData {
  total_platform_revenue: number;
  total_super_admin_share: number;
  monthly_platform_revenue: number;
  monthly_super_admin_share: number;
  total_admins: number;
  admin_details: AdminRevenue[];
  recent_transactions: Transaction[];
}

export default function SuperAdminRevenuePage() {
  const [revenueData, setRevenueData] = useState<SuperAdminRevenueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(30);

  useEffect(() => {
    loadRevenueData();
  }, []);

  const loadRevenueData = async () => {
    try {
      setLoading(true);
      const data = await api.request('/api/user-management/super-admin-revenue');
      setRevenueData(data);
    } catch (error: any) {
      console.error('Error loading revenue data:', error);
      const errorMessage = error.message || 'Failed to load revenue data';
      if (errorMessage.includes('permission') || errorMessage.includes('403')) {
        toast.error('❌ Permission Denied: Only super admin can access this data');
      } else if (errorMessage.includes('Not authenticated')) {
        toast.error('❌ Please login as super admin to access revenue data');
      } else {
        toast.error(`❌ ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    });
  };

  // Filter and search logic
  const filteredAdmins = revenueData?.admin_details.filter(admin => {
    const matchesSearch = admin.admin_full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         admin.admin_username.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  }) || [];

  const filteredTransactions = revenueData?.recent_transactions.filter(transaction => {
    const matchesSearch = transaction.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.username.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || transaction.transaction_type === filterType;
    return matchesSearch && matchesFilter;
  }) || [];

  // Pagination
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTransactions = filteredTransactions.slice(startIndex, startIndex + itemsPerPage);

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['super_admin']}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading revenue data...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!revenueData) {
    return (
      <DashboardLayout allowedRoles={['super_admin']}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <p className="text-muted-foreground">No revenue data available</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout allowedRoles={['super_admin']}>
      <div className="space-y-6 h-full overflow-hidden">
        <div>
          <h1 className="text-3xl font-bold">Super Admin Revenue Dashboard</h1>
          <p className="text-muted-foreground">Monitor platform revenue and admin performance</p>
        </div>

        {/* Revenue Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Platform Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(revenueData.total_platform_revenue)}</div>
              <p className="text-xs text-muted-foreground">All time earnings</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Your Share (80%)</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(revenueData.total_super_admin_share)}</div>
              <p className="text-xs text-muted-foreground">Your total earnings</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(revenueData.monthly_platform_revenue)}</div>
              <p className="text-xs text-muted-foreground">Current month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Admins</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{revenueData.total_admins}</div>
              <p className="text-xs text-muted-foreground">Revenue generating</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search admins or users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Transactions</SelectItem>
                  <SelectItem value="broker_instance_monthly">Broker Instances</SelectItem>
                  <SelectItem value="strategy_monthly">Strategies</SelectItem>
                  <SelectItem value="subscription_fee">Subscriptions</SelectItem>
                  <SelectItem value="usage_charge">Usage Charges</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Admin Revenue Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Admin Performance</CardTitle>
            <CardDescription>
              Revenue breakdown by admin (Admins get 20%, you get 80%)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-auto max-h-96">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Admin</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Total Revenue</TableHead>
                    <TableHead>Admin Share (20%)</TableHead>
                    <TableHead>Your Share (80%)</TableHead>
                    <TableHead>Monthly</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAdmins.map((admin) => (
                    <TableRow key={admin.admin_id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{admin.admin_full_name}</div>
                          <div className="text-sm text-muted-foreground">{admin.admin_username}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{admin.user_count} users</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{formatCurrency(admin.total_revenue)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-blue-600">{formatCurrency(admin.admin_share)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-green-600">{formatCurrency(admin.super_admin_share)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{formatCurrency(admin.monthly_revenue)}</div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions with Pagination */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>
              Latest revenue transactions across all admins (Page {currentPage} of {totalPages})
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-auto max-h-96">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Admin Share (20%)</TableHead>
                    <TableHead>Your Share (80%)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div className="text-sm">{formatDate(transaction.date)}</div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{transaction.user_name}</div>
                          <div className="text-sm text-muted-foreground">{transaction.username}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{transaction.transaction_type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{formatCurrency(transaction.amount)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-blue-600">
                          {formatCurrency(transaction.admin_share)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-green-600">
                          {formatCurrency(transaction.super_admin_share)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination Controls */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredTransactions.length)} of {filteredTransactions.length} transactions
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <div className="text-sm">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
