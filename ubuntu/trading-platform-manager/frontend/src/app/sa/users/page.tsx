'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Plus, Edit, Trash2, Key, <PERSON>, TrendingUp, Server, ChevronDown, Shield } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { DashboardLayout } from '@/components/layout/dashboard-layout';

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  broker_instance_limit: number;
  strategy_limit: number;
  used_broker_instances: number;
  used_strategies: number;
  revenue_share_percentage: number;
  total_revenue_earned: number;
  referral_code: string;
  created_at: string;
  parent_admin_id?: number;
  parent_admin_name?: string;
}

function SuperAdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [availableAdmins, setAvailableAdmins] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    role: 'admin',
    parent_admin_id: '',
    broker_instance_limit: 100,
    strategy_limit: 100,
    revenue_share_percentage: 0
  });
  const [newPassword, setNewPassword] = useState('');

  useEffect(() => {
    loadUsers();
    loadAvailableAdmins();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const data = await api.getMyUsers();
      setUsers(data);
    } catch (error) {
      toast.error('Failed to load users');
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableAdmins = async () => {
    try {
      const data = await api.getAvailableAdmins();
      setAvailableAdmins(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error loading available admins:', error);
      setAvailableAdmins([]);
    }
  };

  const handleCreateUser = async () => {
    try {
      // Validate admin selection for users
      if (formData.role === 'user' && !formData.parent_admin_id) {
        toast.error('Please select an admin for the user');
        return;
      }

      const userData = {
        ...formData,
        parent_admin_id: formData.role === 'user' ? parseInt(formData.parent_admin_id) : null
      };

      await api.createUser(userData);
      toast.success('User created successfully');
      setShowCreateDialog(false);
      setFormData({
        email: '',
        full_name: '',
        role: 'admin',
        parent_admin_id: '',
        broker_instance_limit: 100,
        strategy_limit: 100,
        revenue_share_percentage: 0
      });
      loadUsers();
    } catch (error) {
      toast.error('Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleEditUser = async () => {
    if (!selectedUser) return;

    try {
      const updateData = {
        ...formData,
        parent_admin_id: formData.role === 'user' && formData.parent_admin_id
          ? parseInt(formData.parent_admin_id)
          : null
      };

      await api.updateUser(selectedUser.id, updateData);
      toast.success('User updated successfully');
      setShowEditDialog(false);
      setSelectedUser(null);
      loadUsers();
    } catch (error) {
      toast.error('Failed to update user');
      console.error('Error updating user:', error);
    }
  };

  const handleResetPassword = async () => {
    if (!selectedUser || !newPassword) return;
    
    try {
      await api.resetUserPassword(selectedUser.id, newPassword);
      toast.success('Password reset successfully');
      setShowPasswordDialog(false);
      setSelectedUser(null);
      setNewPassword('');
    } catch (error) {
      toast.error('Failed to reset password');
      console.error('Error resetting password:', error);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return;
    
    try {
      await api.deleteUser(userId);
      toast.success('User deleted successfully');
      loadUsers();
    } catch (error) {
      toast.error('Failed to delete user');
      console.error('Error deleting user:', error);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users first');
      return;
    }

    try {
      // This would call the bulk action API
      toast.success(`Bulk ${action} initiated for ${selectedUsers.length} users`);
      setSelectedUsers([]);
      loadUsers();
    } catch (error) {
      toast.error(`Failed to perform bulk ${action}`);
      console.error(`Error performing bulk ${action}:`, error);
    }
  };

  const toggleUserSelection = (userId: number) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const toggleSelectAll = () => {
    setSelectedUsers(
      selectedUsers.length === filteredUsers.length 
        ? [] 
        : filteredUsers.map(u => u.id)
    );
  };

  const openEditDialog = (user: User) => {
    setSelectedUser(user);
    setFormData({
      email: user.email,
      full_name: user.full_name,
      role: user.role,
      parent_admin_id: user.parent_admin_id?.toString() || '',
      broker_instance_limit: user.broker_instance_limit,
      strategy_limit: user.strategy_limit,
      revenue_share_percentage: user.revenue_share_percentage
    });
    setShowEditDialog(true);
  };

  const openPasswordDialog = (user: User) => {
    setSelectedUser(user);
    setNewPassword('');
    setShowPasswordDialog(true);
  };

  const filteredUsers = roleFilter === 'all' 
    ? users 
    : users.filter(u => u.role === roleFilter);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading users...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System User Management</h1>
          <p className="text-muted-foreground">Manage all users and admins in the system</p>
        </div>
        
        <div className="flex items-center gap-2">
          {selectedUsers.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Bulk Actions ({selectedUsers.length})
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                  Activate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                  Deactivate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('delete')}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create User/Admin
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New User/Admin</DialogTitle>
                <DialogDescription>
                  Create a new admin or user with specified quotas and permissions
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="full_name">Full Name</Label>
                  <Input
                    id="full_name"
                    value={formData.full_name}
                    onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData({...formData, role: value, parent_admin_id: ''})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formData.role === 'user' && (
                  <div>
                    <Label htmlFor="parent_admin">Select Admin *</Label>
                    <Select value={formData.parent_admin_id} onValueChange={(value) => setFormData({...formData, parent_admin_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an admin to manage this user" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableAdmins.map((admin) => (
                          <SelectItem key={admin.id} value={admin.id.toString()}>
                            {admin.username} - {admin.full_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      Super Admin must assign users to an admin. Users cannot be created directly.
                    </p>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="broker_instance_limit">Broker Instance Limit</Label>
                    <Input
                      id="broker_instance_limit"
                      type="number"
                      value={formData.broker_instance_limit}
                      onChange={(e) => setFormData({...formData, broker_instance_limit: parseInt(e.target.value)})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="strategy_limit">Strategy Limit</Label>
                    <Input
                      id="strategy_limit"
                      type="number"
                      value={formData.strategy_limit}
                      onChange={(e) => setFormData({...formData, strategy_limit: parseInt(e.target.value)})}
                    />
                  </div>
                </div>
                {formData.role === 'admin' && (
                  <div>
                    <Label htmlFor="revenue_share">Revenue Share from Users (%)</Label>
                    <Input
                      id="revenue_share"
                      type="number"
                      step="0.1"
                      value={formData.revenue_share_percentage}
                      onChange={(e) => setFormData({...formData, revenue_share_percentage: parseFloat(e.target.value)})}
                      placeholder="10"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Percentage of revenue this admin earns from their users
                    </p>
                  </div>
                )}
                <Button onClick={handleCreateUser} className="w-full">
                  Create {formData.role === 'admin' ? 'Admin' : 'User'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span className="text-sm font-medium">Role:</span>
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admins</SelectItem>
                <SelectItem value="user">Users</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* User Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredUsers.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admins</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.role === 'admin').length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredUsers.filter(u => u.is_active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${filteredUsers.reduce((sum, u) => sum + u.total_revenue_earned, 0).toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>System Users & Admins</CardTitle>
          <CardDescription>Manage all users and admins in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedUsers.length === filteredUsers.length}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Quotas</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Revenue Share</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={() => toggleUserSelection(user.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.full_name}</div>
                      <div className="text-sm text-muted-foreground">{user.username}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                      {user.role === 'admin' ? 'Admin' : 'User'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.is_active ? "default" : "secondary"}>
                      {user.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>Instances: {user.broker_instance_limit}</div>
                      <div>Strategies: {user.strategy_limit}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>Instances: {user.used_broker_instances}/{user.broker_instance_limit}</div>
                      <div>Strategies: {user.used_strategies}/{user.strategy_limit}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {user.role === 'admin' ? (
                        <>
                          <div>{user.revenue_share_percentage}%</div>
                          <div className="text-muted-foreground">${user.total_revenue_earned.toFixed(2)}</div>
                        </>
                      ) : (
                        <div className="text-muted-foreground">No revenue share</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openPasswordDialog(user)}
                      >
                        <Key className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and quotas
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_email">Email</Label>
              <Input
                id="edit_email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="edit_full_name">Full Name</Label>
              <Input
                id="edit_full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({...formData, full_name: e.target.value})}
              />
            </div>
            {formData.role === 'user' && (
              <div>
                <Label htmlFor="edit_parent_admin">Transfer to Admin</Label>
                <Select value={formData.parent_admin_id} onValueChange={(value) => setFormData({...formData, parent_admin_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select admin to manage this user" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableAdmins.map((admin) => (
                      <SelectItem key={admin.id} value={admin.id.toString()}>
                        {admin.username} - {admin.full_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground mt-1">
                  Change which admin manages this user. This will update referral tracking.
                </p>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit_broker_instance_limit">Broker Instance Limit</Label>
                <Input
                  id="edit_broker_instance_limit"
                  type="number"
                  value={formData.broker_instance_limit}
                  onChange={(e) => setFormData({...formData, broker_instance_limit: parseInt(e.target.value)})}
                />
              </div>
              <div>
                <Label htmlFor="edit_strategy_limit">Strategy Limit</Label>
                <Input
                  id="edit_strategy_limit"
                  type="number"
                  value={formData.strategy_limit}
                  onChange={(e) => setFormData({...formData, strategy_limit: parseInt(e.target.value)})}
                />
              </div>
            </div>
            {formData.role === 'admin' && (
              <div>
                <Label htmlFor="edit_revenue_share">Revenue Share from Users (%)</Label>
                <Input
                  id="edit_revenue_share"
                  type="number"
                  step="0.1"
                  value={formData.revenue_share_percentage}
                  onChange={(e) => setFormData({...formData, revenue_share_percentage: parseFloat(e.target.value)})}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Percentage of revenue this admin earns from their users
                </p>
              </div>
            )}
            <Button onClick={handleEditUser} className="w-full">
              Update User
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Set a new password for {selectedUser?.full_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new_password">New Password</Label>
              <Input
                id="new_password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter new password"
              />
            </div>
            <Button onClick={handleResetPassword} className="w-full">
              Reset Password
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function SuperAdminUsersPageWithLayout() {
  return (
    <DashboardLayout allowedRoles={['super_admin']}>
      <SuperAdminUsersPage />
    </DashboardLayout>
  );
}
