'use client';

import { useState } from 'react';
import Link from 'next/link';
import { User, Lock, LogIn, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface LoginFormProps {
  role: 'user' | 'admin' | 'super_admin';
  className?: string;
}

const roleConfig = {
  user: {
    title: 'User Login',
    description: 'Access your trading dashboard',
    icon: User,
    demoCredentials: { username: 'US1000', password: 'User123!' },
    otherRoles: [
      { href: '/admin/login', label: 'Admin', icon: 'shield' },
      { href: '/sa/login', label: 'Super Admin', icon: 'crown' },
    ],
  },
  admin: {
    title: 'Admin Login',
    description: 'Manage users and system',
    icon: User, // Will be replaced with Shield icon
    demoCredentials: { username: 'AD1000', password: 'Admin123!' },
    otherRoles: [
      { href: '/user/login', label: 'User', icon: 'user' },
      { href: '/sa/login', label: 'Super Admin', icon: 'crown' },
    ],
  },
  super_admin: {
    title: 'Super Admin Login',
    description: 'Full system administration',
    icon: User, // Will be replaced with Crown icon
    demoCredentials: { username: 'SA1000', password: 'SuperAdmin123!' },
    otherRoles: [
      { href: '/user/login', label: 'User', icon: 'user' },
      { href: '/admin/login', label: 'Admin', icon: 'shield' },
    ],
  },
};

export function LoginForm({ role, className }: LoginFormProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const { login, isLoading } = useAuth();
  const config = roleConfig[role];
  const IconComponent = config.icon;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }

    const result = await login({ username, password });
    
    if (result.success) {
      setSuccess('Login successful! Redirecting...');
    } else {
      setError(result.error || 'Login failed. Please try again.');
    }
  };

  const fillDemoCredentials = () => {
    setUsername(config.demoCredentials.username);
    setPassword(config.demoCredentials.password);
  };

  return (
    <div className={cn("max-w-md w-full mx-auto p-8", className)}>
      <Card className="shadow-lg">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <IconComponent className="w-8 h-8 text-primary dark:text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold">{config.title}</CardTitle>
          <CardDescription>{config.description}</CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="username" className="block text-sm font-medium mb-2">
                  Username
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-muted-foreground dark:text-muted-foreground" />
                  </div>
                  <Input
                    id="username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Enter your username"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="password" className="block text-sm font-medium mb-2">
                  Password
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-muted-foreground dark:text-muted-foreground" />
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <Label htmlFor="remember-me" className="ml-2 block text-sm">
                  Remember me
                </Label>
              </div>
              <div className="text-sm">
                <Link href="#" className="font-medium text-primary hover:text-primary/80">
                  Forgot password?
                </Link>
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              <LogIn className="w-4 h-4 mr-2" />
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </form>

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 text-destructive rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                <span>{success}</span>
              </div>
            </div>
          )}

          {/* Demo Credentials */}
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="text-sm font-medium mb-2 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              Demo Credentials
            </h4>
            <div className="text-xs text-muted-foreground space-y-1">
              <div><strong>Username:</strong> {config.demoCredentials.username}</div>
              <div><strong>Password:</strong> {config.demoCredentials.password}</div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={fillDemoCredentials}
              className="mt-2 w-full"
            >
              Use Demo Credentials
            </Button>
          </div>

          {/* Other Login Types */}
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground mb-4">Login as different role:</p>
            <div className="grid grid-cols-2 gap-3">
              {config.otherRoles.map((otherRole) => (
                <Link
                  key={otherRole.href}
                  href={otherRole.href}
                  className="flex items-center justify-center py-2 px-3 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors text-sm"
                >
                  {otherRole.icon === 'shield' && <User className="w-4 h-4 mr-2" />}
                  {otherRole.icon === 'crown' && <User className="w-4 h-4 mr-2" />}
                  {otherRole.icon === 'user' && <User className="w-4 h-4 mr-2" />}
                  {otherRole.label}
                </Link>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
