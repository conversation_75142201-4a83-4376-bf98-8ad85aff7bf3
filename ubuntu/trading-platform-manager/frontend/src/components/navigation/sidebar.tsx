'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Menu, 
  X, 
  LayoutDashboard, 
  Settings, 
  LogOut,
  User,
  Shield,
  Crown,
  TrendingUp,
  Users,
  Server,
  BarChart3,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: string[];
}

const getNavigationItems = (role: string): NavItem[] => {
  const baseItems: NavItem[] = [
    {
      href: `/${role}/dashboard`,
      label: 'Dashboard',
      icon: LayoutDashboard,
    },
  ];

  switch (role) {
    case 'user':
      return [
        ...baseItems,
        {
          href: '/user/broker-instances',
          label: 'Broker Instances',
          icon: Server,
        },
        {
          href: '/user/strategies',
          label: 'Strategies',
          icon: TrendingUp,
        },
        {
          href: '/user/analytics',
          label: 'Analytics',
          icon: BarChart3,
        },
        {
          href: '/user/settings',
          label: 'Settings',
          icon: Settings,
        },
      ];
    case 'admin':
      return [
        ...baseItems,
        {
          href: '/admin/users',
          label: 'Users',
          icon: Users,
        },
        {
          href: '/admin/strategies',
          label: 'Strategies',
          icon: TrendingUp,
        },
        {
          href: '/admin/broker-instances',
          label: 'Broker Instances',
          icon: Server,
        },
        {
          href: '/admin/analytics',
          label: 'Analytics',
          icon: BarChart3,
        },
        {
          href: '/admin/settings',
          label: 'Settings',
          icon: Settings,
        },
      ];
    case 'super_admin':
      return [
        ...baseItems,
        {
          href: '/sa/admins',
          label: 'Admins',
          icon: Shield,
        },
        {
          href: '/sa/users',
          label: 'Users',
          icon: Users,
        },
        {
          href: '/sa/broker-instances',
          label: 'Broker Instance Management',
          icon: Server,
        },
        {
          href: '/sa/system',
          label: 'System Management',
          icon: Settings,
        },
        {
          href: '/sa/strategies',
          label: 'Strategies',
          icon: TrendingUp,
        },
        {
          href: '/sa/analytics',
          label: 'Analytics',
          icon: BarChart3,
        },
      ];
    default:
      return baseItems;
  }
};

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'user':
      return User;
    case 'admin':
      return Shield;
    case 'super_admin':
      return Crown;
    default:
      return User;
  }
};

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'user':
      return 'User';
    case 'admin':
      return 'Admin';
    case 'super_admin':
      return 'Super Admin';
    default:
      return 'User';
  }
};

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  if (!user) return null;

  const navigationItems = getNavigationItems(user.role);
  const RoleIcon = getRoleIcon(user.role);
  const roleLabel = getRoleLabel(user.role);

  const NavContent = () => (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex h-16 items-center justify-between border-b px-4">
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground flex-shrink-0">
            <RoleIcon className="h-4 w-4" />
          </div>
          {!isCollapsed && (
            <div className="flex flex-col min-w-0">
              <span className="text-sm font-semibold truncate">{user.full_name}</span>
              <span className="text-xs text-muted-foreground">{roleLabel}</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-1 flex-shrink-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:flex h-8 w-8"
          >
            <Menu className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileOpen(false)}
            className="lg:hidden h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;
          
          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                "flex items-center rounded-lg text-sm font-medium transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                isCollapsed
                  ? "justify-center p-2 mx-2"
                  : "gap-3 px-3 py-2"
              )}
              title={isCollapsed ? item.label : undefined}
            >
              <Icon className="h-4 w-4 shrink-0" />
              {!isCollapsed && <span>{item.label}</span>}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="border-t p-4 space-y-2">
        <div className={cn(
          "flex",
          isCollapsed ? "justify-center" : "justify-between items-center"
        )}>
          {!isCollapsed && <span className="text-sm text-muted-foreground">Theme</span>}
          <ThemeToggle />
        </div>
        <Button
          variant="ghost"
          onClick={logout}
          className={cn(
            isCollapsed
              ? "w-full p-2 justify-center"
              : "w-full justify-start gap-3"
          )}
          title={isCollapsed ? "Logout" : undefined}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>Logout</span>}
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Mobile toggle button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => setIsMobileOpen(true)}
        className="fixed left-4 top-4 z-50 lg:hidden"
      >
        <Menu className="h-4 w-4" />
      </Button>

      {/* Desktop sidebar */}
      <aside
        className={cn(
          "hidden lg:flex flex-col h-screen border-r bg-background transition-all duration-300 flex-shrink-0",
          isCollapsed ? "w-16" : "w-64",
          className
        )}
      >
        <NavContent />
      </aside>

      {/* Mobile sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 border-r bg-background transition-transform duration-300 lg:hidden flex flex-col",
          isMobileOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <NavContent />
      </aside>
    </>
  );
}
