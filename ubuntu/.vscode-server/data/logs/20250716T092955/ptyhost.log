2025-07-16 09:29:59.221 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata:true' -O - http://***************/metadata/instance?api-version=2019-03-11\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata:true' http://***************/metadata/instance?api-version=2019-03-11\n\tfi\nfi\nexit 0"]
2025-07-16 09:29:59.249 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/latest/meta-data/instance-id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/latest/meta-data/instance-id\n\tfi\nfi\nexit 0"]
2025-07-16 09:29:59.259 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/metadata/v1/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/metadata/v1/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:29:59.273 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata-Flavor:Google' -O - http://metadata.google.internal/computeMetadata/v1/instance/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata-Flavor:Google' http://metadata.google.internal/computeMetadata/v1/instance/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:21.944 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata:true' -O - http://***************/metadata/instance?api-version=2019-03-11\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata:true' http://***************/metadata/instance?api-version=2019-03-11\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:21.956 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/latest/meta-data/instance-id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/latest/meta-data/instance-id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:21.963 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/metadata/v1/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/metadata/v1/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:21.971 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata-Flavor:Google' -O - http://metadata.google.internal/computeMetadata/v1/instance/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata-Flavor:Google' http://metadata.google.internal/computeMetadata/v1/instance/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:30.726 [info] Revived process, old id 49050a103d44b8fd85390fe38e83a6d6-9 -> new id 19
2025-07-16 09:55:30.726 [info] Revived process, old id 49050a103d44b8fd85390fe38e83a6d6-10 -> new id 20
2025-07-16 09:55:30.726 [info] Revived process, old id 49050a103d44b8fd85390fe38e83a6d6-14 -> new id 21
2025-07-16 09:55:30.834 [info] Expanding terminal instance, old id 49050a103d44b8fd85390fe38e83a6d6-5 -> new id undefined
2025-07-16 09:55:30.836 [warning] Couldn't get layout info, a terminal was probably disconnected Could not find pty 5 on pty host
2025-07-16 09:55:30.836 [info] Expanding terminal instance, old id 49050a103d44b8fd85390fe38e83a6d6-9 -> new id 19
2025-07-16 09:55:30.836 [info] Expanding terminal instance, old id 49050a103d44b8fd85390fe38e83a6d6-10 -> new id 20
2025-07-16 09:55:30.836 [info] Expanding terminal instance, old id 49050a103d44b8fd85390fe38e83a6d6-14 -> new id 21
2025-07-16 09:55:31.528 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata:true' -O - http://***************/metadata/instance?api-version=2019-03-11\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata:true' http://***************/metadata/instance?api-version=2019-03-11\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:31.535 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/latest/meta-data/instance-id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/latest/meta-data/instance-id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:31.556 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q  -O - http://***************/metadata/v1/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s  http://***************/metadata/v1/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:31.582 [warning] Shell integration cannot be enabled for executable "/bin/sh" and args ["-c","wget --version > /dev/null\nif [ $? -eq 0 ]\nthen\n\twget --no-config --connect-timeout=7 --tries=1 --dns-timeout=7 -q --header='Metadata-Flavor:Google' -O - http://metadata.google.internal/computeMetadata/v1/instance/id\nelse\n\tcurl --version > /dev/null\n\tif [ $? -eq 0 ]\n\tthen\n\t\tcurl --disable --connect-timeout 7 -s --header='Metadata-Flavor:Google' http://metadata.google.internal/computeMetadata/v1/instance/id\n\tfi\nfi\nexit 0"]
2025-07-16 09:55:31.984 [warning] Persistent process "19": Process had no disconnect runners but was an orphan
2025-07-16 09:55:31.984 [warning] Persistent process "21": Process had no disconnect runners but was an orphan
2025-07-16 09:55:31.984 [warning] Persistent process "20": Process had no disconnect runners but was an orphan
2025-07-16 09:55:31.984 [info] Persistent process reconnection "19"
2025-07-16 09:55:31.984 [info] Persistent process reconnection "21"
2025-07-16 09:55:31.984 [info] Persistent process reconnection "20"
2025-07-16 09:55:32.149 [info] Persistent process "19": Replaying 10884 chars and 1 size events
2025-07-16 09:55:32.194 [info] Persistent process "21": Replaying 847 chars and 1 size events
2025-07-16 09:55:32.286 [info] Persistent process "20": Replaying 4550 chars and 1 size events
