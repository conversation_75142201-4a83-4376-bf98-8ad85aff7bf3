2025-07-16 09:55:28.929 [info] Extension host with pid 209385 started
2025-07-16 09:55:28.930 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/49050a103d44b8fd85390fe38e83a6d6/vscode.lock': Lock acquired.
2025-07-16 09:55:29.228 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-16 09:55:29.229 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:javascript'
2025-07-16 09:55:29.230 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-16 09:55:30.134 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-16 09:55:30.136 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-16 09:55:33.281 [info] Eager extensions activated
2025-07-16 09:55:33.281 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-16 09:55:33.289 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-16 09:55:33.289 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-16 09:55:38.111 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-07-16 09:55:49.853 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5CAnita%20Bhanushali%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at t8e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:523:10721)
