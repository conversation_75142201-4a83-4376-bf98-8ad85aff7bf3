2025-07-16 09:55:39.548 [info] [main] Log level: Info
2025-07-16 09:55:39.548 [info] [main] Validating found git in: "git"
2025-07-16 09:55:39.548 [info] [main] Using git "2.43.0" from "git"
2025-07-16 09:55:39.548 [info] [Model][doInitialScan] Initial repository scan started
2025-07-16 09:55:39.548 [info] > git rev-parse --show-toplevel [5353ms]
2025-07-16 09:55:39.548 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.548 [info] > git rev-parse --show-toplevel [134ms]
2025-07-16 09:55:39.548 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.548 [info] > git rev-parse --show-toplevel [114ms]
2025-07-16 09:55:39.548 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.643 [info] > git rev-parse --show-toplevel [77ms]
2025-07-16 09:55:39.643 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.730 [info] > git rev-parse --show-toplevel [71ms]
2025-07-16 09:55:39.730 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.748 [info] > git rev-parse --show-toplevel [1ms]
2025-07-16 09:55:39.749 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.766 [info] > git rev-parse --show-toplevel [2ms]
2025-07-16 09:55:39.766 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.782 [info] > git rev-parse --show-toplevel [6ms]
2025-07-16 09:55:39.782 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.808 [info] > git rev-parse --show-toplevel [14ms]
2025-07-16 09:55:39.808 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.829 [info] > git rev-parse --show-toplevel [6ms]
2025-07-16 09:55:39.829 [info] fatal: not a git repository (or any of the parent directories): .git
2025-07-16 09:55:39.845 [info] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
