[["/home/<USER>/trading-platform-manager/test_complete_system.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/test_complete_system.py"}}], ["/home/<USER>/trading-platform-manager/manual/1-angel-3_commands.txt", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/manual/1-angel-3_commands.txt"}}], ["/home/<USER>/trading-platform-manager/manual/1-angel-3_install.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/manual/1-angel-3_install.sh"}}], ["/home/<USER>/trading-platform-manager/manual/1-angel-1_commands.txt", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/manual/1-angel-1_commands.txt"}}], ["/home/<USER>/tpi-install/COMPLETE-SOLUTION.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/tpi-install/COMPLETE-SOLUTION.md"}}], ["/home/<USER>/tpi-install/commands.txt", {"value": {"rootPath": "/home", "relPath": "ubuntu/tpi-install/commands.txt"}}], ["/home/<USER>/trading-platform-manager/frontend/spa_server.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/spa_server.py"}}], ["/home/<USER>/trading-platform-manager/frontend/admin-dashboard.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/admin-dashboard.html"}}], ["/home/<USER>/trading-platform-manager/scripts/final-uninstall.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/final-uninstall.sh"}}], ["/home/<USER>/trading-platform-manager/scripts/fix-socket-permissions.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/fix-socket-permissions.sh"}}], ["/home/<USER>/trading-platform-manager/scripts/check-wildcard-ssl.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/check-wildcard-ssl.sh"}}], ["/home/<USER>/openalgo-install/install.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/openalgo-install/install.sh"}}], ["/home/<USER>/openalgo-install/api-create-instance.php", {"value": {"rootPath": "/home", "relPath": "ubuntu/openalgo-install/api-create-instance.php"}}], ["/home/<USER>/openalgo-install/api-create-instance.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/openalgo-install/api-create-instance.js"}}], ["/home/<USER>/openalgo-install/create-instance.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/openalgo-install/create-instance.sh"}}], ["/home/<USER>/trading-platform-manager/scripts/setup-us1001.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/setup-us1001.js"}}], ["/home/<USER>/trading-platform-manager/frontend/super-user-dashboard.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/super-user-dashboard.html"}}], ["/home/<USER>/openalgo-install/install-shared-venv.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/openalgo-install/install-shared-venv.sh"}}], ["/home/<USER>/tpi-install/install.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/install.sh"}}], ["/home/<USER>/tpi-install/final-uninstall.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/final-uninstall.sh"}}], ["/home/<USER>/tpi-install/fix-all-instances.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/fix-all-instances.sh"}}], ["/home/<USER>/tpi-install/fix-nginx-403.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/fix-nginx-403.sh"}}], ["/home/<USER>/tpi-install/install-final.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/install-final.sh"}}], ["/home/<USER>/tpi-install/setup-wildcard-ssl.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/setup-wildcard-ssl.sh"}}], ["/home/<USER>/tpi-install/final.sh", {"value": {"rootPath": "/home", "relPath": "sa-admin-backend/tpi-install/final.sh"}}], ["/home/<USER>/trading-platform-manager/test-create-us1001.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/test-create-us1001.js"}}], ["/home/<USER>/.sudo_as_admin_successful", {"value": {"rootPath": "/home", "relPath": "ubuntu/.sudo_as_admin_successful"}}], ["/home/<USER>/.profile", {"value": {"rootPath": "/home", "relPath": "ubuntu/.profile"}}], ["/home/<USER>/.bashrc", {"value": {"rootPath": "/home", "relPath": "ubuntu/.bashrc"}}], ["/home/<USER>/.bash_logout", {"value": {"rootPath": "/home", "relPath": "ubuntu/.bash_logout"}}], ["/home/<USER>/trading-platform-manager/backend/strategies-api.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/strategies-api.js"}}], ["/home/<USER>/trading-platform-manager/backend/super-instance-manager.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/super-instance-manager.js"}}], ["/home/<USER>/trading-platform-manager/backend/test-robust-system.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/test-robust-system.js"}}], ["/home/<USER>/trading-platform-manager/fix-theme-system.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/fix-theme-system.js"}}], ["/home/<USER>/trading-platform-manager/package.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/package.json"}}], ["/home/<USER>/trading-platform-manager/create-us1001.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/create-us1001.js"}}], ["/home/<USER>/trading-platform-manager/demo_branding.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/demo_branding.py"}}], ["/home/<USER>/trading-platform-manager/backend/config.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/config.py"}}], ["/home/<USER>/trading-platform-manager/backend/routers/strategies.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/routers/strategies.py"}}], ["/home/<USER>/trading-platform-manager/backend/utils/auth.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/utils/auth.py"}}], ["/home/<USER>/trading-platform-manager/backend/instance-manager.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/backend/instance-manager.js"}}], ["/home/<USER>/trading-platform-manager/frontend/database.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/database.js"}}], ["/home/<USER>/trading-platform-manager/frontend/api.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/api.js"}}], ["/home/<USER>/trading-platform-manager/frontend/src/components/strategies.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/src/components/strategies.js"}}], ["/home/<USER>/trading-platform-manager/frontend/src/components/navigation.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/src/components/navigation.js"}}], ["/home/<USER>/trading-platform-manager/frontend/src/components/instances.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/src/components/instances.js"}}], ["/home/<USER>/trading-platform-manager/frontend/strategies.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/strategies.html"}}], ["/home/<USER>/trading-platform-manager/frontend/index.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/index.html"}}], ["/home/<USER>/trading-platform-manager/frontend/server.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/server.js"}}], ["/home/<USER>/trading-platform-manager/frontend/user-analytics.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-analytics.html"}}], ["/home/<USER>/trading-platform-manager/frontend/user-instances.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-instances.html"}}], ["/home/<USER>/trading-platform-manager/frontend/user-signup.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-signup.html"}}], ["/home/<USER>/trading-platform-manager/frontend/user-strategies.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-strategies.html"}}], ["/home/<USER>/trading-platform-manager/frontend/working-theme-test.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/working-theme-test.html"}}], ["/home/<USER>/trading-platform-manager/frontend/user-dashboard.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-dashboard.html"}}], ["/home/<USER>/trading-platform-manager/frontend/user-login.html", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/user-login.html"}}], ["/home/<USER>/trading-platform-manager/react-frontend/package.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/react-frontend/package.json"}}], ["/home/<USER>/trading-platform-manager/frontend/src/app/page.tsx", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/src/app/page.tsx"}}], ["/home/<USER>/trading-platform-manager/frontend/package-lock.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/package-lock.json"}}], ["/home/<USER>/trading-platform-manager/frontend/src/app/globals.css", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/frontend/src/app/globals.css"}}], ["/home/<USER>/trading-platform-manager/scripts/create-instance.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/create-instance.sh"}}], ["/home/<USER>/.bash_history", {"value": {"rootPath": "/home", "relPath": "ubuntu/.bash_history"}}], ["/home/<USER>/cmd", {"value": {"rootPath": "/home", "relPath": "ubuntu/cmd"}}], ["/home/<USER>/trading-platform-manager/scripts/instance-api.js", {"value": {"rootPath": "/home", "relPath": "ubuntu/trading-platform-manager/scripts/instance-api.js"}}]]