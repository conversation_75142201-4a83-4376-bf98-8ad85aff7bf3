*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[09:29:55] 




[09:29:55] Extension host agent started.
[09:29:55] [<unknown>][eda5a9a0][ExtensionHostConnection] New connection established.
[09:29:55] [<unknown>][9b5250b6][ManagementConnection] New connection established.
[09:29:55] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.496.2
[09:29:55] [<unknown>][eda5a9a0][ExtensionHostConnection] <206009> Launched Extension Host Process.
[09:30:16] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home)
New EH opened, aborting shutdown
[09:34:55] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[09:35:28] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[09:35:28] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[09:35:28] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
[09:55:14] [<unknown>][9b5250b6][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[09:55:15] [<unknown>][eda5a9a0][ExtensionHostConnection] <206009> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[09:55:15] Last EH closed, waiting before shutting down
[09:55:19] [<unknown>][9ba111a2][ManagementConnection] New connection established.
[09:55:19] [<unknown>][14c68ffc][ExtensionHostConnection] New connection established.
[09:55:19] [<unknown>][14c68ffc][ExtensionHostConnection] <209233> Launched Extension Host Process.
[09:55:24] [<unknown>][9ba111a2][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[09:55:24] [<unknown>][14c68ffc][ExtensionHostConnection] <209233> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[09:55:24] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[09:55:24] Last EH closed, waiting before shutting down
[09:55:27] [<unknown>][cbb73042][ExtensionHostConnection] New connection established.
[09:55:27] [<unknown>][cbb73042][ExtensionHostConnection] <209385> Launched Extension Host Process.
[09:55:27] [<unknown>][b10a430e][ManagementConnection] New connection established.
[09:55:59] [File Watcher ('parcel')] Inotify limit reached (ENOSPC) (path: /home)
