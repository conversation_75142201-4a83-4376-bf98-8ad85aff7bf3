                        <p class="text-3xl font-bold text-gray-900 dark:text-white">156</p>
                        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">+12 this week</p>
                    </div>
                </div>

                <!-- Active Instances Card -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                            <i data-lucide="server" class="h-6 w-6 text-green-600 dark:text-green-400"></i>
                        </div>
                        <span class="text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">Active</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Active Instances</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">89</p>
                        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">98.2% uptime</p>
                    </div>
                </div>

                <!-- System Health Card -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                            <i data-lucide="activity" class="h-6 w-6 text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <span class="text-xs font-medium text-purple-600 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">Healthy</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">System Health</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">95%</p>
                        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">All systems operational</p>
                    </div>
                </div>

                <!-- Revenue Card -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                            <i data-lucide="dollar-sign" class="h-6 w-6 text-orange-600 dark:text-orange-400"></i>
                        </div>
                        <span class="text-xs font-medium text-orange-600 bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded-full">Monthly</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Monthly Revenue</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">$12.4K</p>
                        <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">+8.2% from last month</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-4">
                <h2 class="text-base font-semibold mb-2 text-gray-900 dark:text-white">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button id="admin-create-user-btn" class="bg-green-600 text-white hover:bg-green-700 rounded-lg p-4 flex items-center space-x-3 transition-colors">
                        <i data-lucide="user-plus" class="h-5 w-5"></i>
                        <span class="font-medium">Create User</span>
                    </button>
                    <button id="admin-manage-instances-btn" class="bg-blue-600 text-white hover:bg-blue-700 rounded-lg p-4 flex items-center space-x-3 transition-colors">
                        <i data-lucide="server" class="h-5 w-5"></i>
                        <span class="font-medium">Manage Instances</span>
                    </button>
                    <button id="admin-view-analytics-btn" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg p-4 flex items-center space-x-3 transition-colors">
                        <i data-lucide="bar-chart-3" class="h-5 w-5"></i>
                        <span class="font-medium">View Analytics</span>
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mb-4">
                <h2 class="text-base font-semibold mb-2 text-gray-900 dark:text-white">Recent Activity</h2>
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">New user US0156 registered</span>
                            <span class="text-xs text-gray-500 dark:text-gray-500 ml-auto">2 min ago</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Instance zerodha-89 deployed successfully</span>
                            <span class="text-xs text-gray-500 dark:text-gray-500 ml-auto">5 min ago</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">System backup completed</span>
                            <span class="text-xs text-gray-500 dark:text-gray-500 ml-auto">1 hour ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Initialize Lucide icons
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing icons...');
            lucide.createIcons();
            console.log('Icons initialized');
        });

        // Mobile menu functionality
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        const mobileMenuClose = document.getElementById('mobile-menu-close');
        const themeToggle = document.getElementById('theme-toggle');
        const logoutBtn = document.getElementById('logout-btn');
        const userNameElement = document.getElementById('sidebar-user-name');

        // Load user data
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        console.log('Current user data:', currentUser);
        
        if (currentUser.name) {
            userNameElement.textContent = currentUser.name;
        } else if (currentUser.username) {
            userNameElement.textContent = currentUser.username;
        } else {
            userNameElement.textContent = 'Demo Admin';
        }
        
        // Also update the welcome message
        const welcomeHeader = document.querySelector('h1');
        if (welcomeHeader && currentUser.name) {
            welcomeHeader.textContent = `Welcome Back, ${currentUser.name}!`;
        } else if (welcomeHeader && currentUser.username) {
            welcomeHeader.textContent = `Welcome Back, ${currentUser.username}!`;
        }

        // Mobile menu toggle
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function() {
                sidebar.classList.remove('-translate-x-full');
                sidebarOverlay.classList.remove('hidden');
            });
        }

        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            });
        }

        // Dark mode functionality
        if (localStorage.getItem('darkMode') === 'true' || 
            (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
        
        if (themeToggle) {
            themeToggle.addEventListener('click', function() {
                console.log('Theme toggle clicked');
                document.documentElement.classList.toggle('dark');
                const isDark = document.documentElement.classList.contains('dark');
                localStorage.setItem('darkMode', isDark);
                lucide.createIcons(); // Refresh icons
            });
        } else {
            console.error('Theme toggle button not found');
        }

        // Button functionality
        const adminCreateUserBtn = document.getElementById('admin-create-user-btn');
        const adminManageInstancesBtn = document.getElementById('admin-manage-instances-btn');
        const adminViewAnalyticsBtn = document.getElementById('admin-view-analytics-btn');
        
        if (adminCreateUserBtn) {
            adminCreateUserBtn.addEventListener('click', function() {
                alert('Create User functionality coming soon!');
            });
        }

        if (adminManageInstancesBtn) {
            adminManageInstancesBtn.addEventListener('click', function() {
                alert('Manage Instances functionality coming soon!');
            });
        }

        if (adminViewAnalyticsBtn) {
            adminViewAnalyticsBtn.addEventListener('click', function() {
                alert('View Analytics functionality coming soon!');
            });
        }

        // Logout functionality
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to logout?')) {
                    localStorage.removeItem('currentUser');
                    window.location.href = '/admin/login';
                }
            });
        }
    </script>
</body>
</html>
EOF

ls -la sa-login.html
ls -la sa-dashboard.html
echo 'Creating SA dashboard...'
touch sa-dashboard.html
ps aux | grep node
netstat -tlnp | grep 3005
pkill -f "node server.js"
npm install sqlite3 bcryptjs jsonwebtoken
pkill -f "node server.js"
fuser -k 3005/tcp
cd ..
# Create a directory for installation
mkdir -p ~/openalgo-install
cd ~/openalgo-install
# Download the installation script
wget https://raw.githubusercontent.com/marketcalls/openalgo/main/install/install.sh
# Make the script executable
chmod +x install.sh
# Execute the installation script
sudo ./install.sh
chmod +x uninstall.sh
sudo ./uninstall.sh
./uninstall.sh
chmod +x test-uninstall.sh
./test-uninstall.sh
chmod +x install-shared-venv.sh
./install-shared-venv.sh 
chmod +x uninstall-shared-venv.sh
sudo ./install-shared-venv.sh 
chmod +x test-shared-venv.sh
./test-shared-venv.sh
ls -la *.sh
ls -la *.md
sudo ./install-shared-venv.sh 
chmod +x create-instance.sh test-create-instance.sh api-create-instance.js
chmod +x create-instance.sh instance-api.js test-us1001.js
chmod +x /home/<USER>/trading-platform-manager/scripts/create-instance.sh /home/<USER>/trading-platform-manager/scripts/instance-api.js /home/<USER>/trading-platform-manager/scripts/test-us1001.js
sqlite3 /home/<USER>/trading-platform-manager/frontend/trading_platform.db "SELECT username, name, instance_quota, instances_used FROM users WHERE username='us1001';"
chmod +x setup-us1001.js
chmod +x instance-manager.js test-robust-system.js
ls -la /home/<USER>/trading-platform-manager/backend/
chmod +x instance-manager.js test-robust-system.js
find /home/<USER>/trading-platform-manager -name "instance-manager.js" -type f
chmod +x /home/<USER>/trading-platform-manager/backend/instance-manager.js /home/<USER>/trading-platform-manager/backend/test-robust-system.js
sqlite3 /home/<USER>/trading-platform-manager/frontend/trading_platform.db "SELECT username, name, instance_quota, instances_used, status FROM users WHERE username='us1001';"
node create-us1001.js
cd /home/<USER>/trading-platform-manager && node create-us1001.js
npm install sqlite3 bcrypt express axios
node create-us1001.js
node backend/test-robust-system.js
sqlite3 /home/<USER>/trading-platform-manager/frontend/trading_platform.db "ALTER TABLE instance_logs ADD COLUMN details TEXT;"
node test-create-us1001.js
sqlite3 /home/<USER>/trading-platform-manager/frontend/trading_platform.db "SELECT id, instance_id, action, status, message, performed_by, created_at FROM instance_logs ORDER BY created_at DESC LIMIT 10;"
sqlite3 /home/<USER>/trading-platform-manager/frontend/trading_platform.db "SELECT instance_id, user_username, broker, domain, status, created_at FROM instances WHERE user_username='us1001';"
chmod +x super-instance-manager.js test-super-system.js
chmod +x /home/<USER>/trading-platform-manager/backend/super-instance-manager.js /home/<USER>/trading-platform-manager/test-super-system.js
chmod +x start-us1001-test.sh stop-us1001-test.sh
./start-us1001-test.sh
ps aux | grep -E "(super-instance-manager|instance-manager)" | grep -v grep
curl -s http://localhost:3010/api/health
curl -s http://localhost:3009/api/health
./stop-us1001-test.sh
./start-us1001-test.sh
ps aux | grep -E "(uvicorn|fastapi|main.py)" | grep -v grep
htop
npm run
npm start
cd backend && source venv/bin/activate && python main.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
cd frontend && python3 -m http.server 8080
source venv/bin/activate && python main.py
cd backend && source venv/bin/activate && python main.py
source venv/bin/activate && uvicorn main:app --host 0.0.0.0 --port 8001 --reload
cd /home/<USER>/trading-platform-manager/frontend && python3 -m http.server 3005
venv/bin/activate
source venv/bin/activate
python main.py
ls
cd..
cd /
ls
cd home
ls
cd ubuntu
ls
cd trading-platform-manager/
ls
cd frontend
run npm
start npm
npm start
chmod +x fix-theme-system.js
node fix-theme-system.js
chmod +x strategies-api.js
chmod +x backend/strategies-api.js
curl -H "X-API-Key: strategies-api-2024" http://localhost:8000/api/strategies/
cd backend && source venv/bin/activate && python -c "from routers import strategies; print('Strategies router imported successfully')"
python -c "from routers import strategies; print('✅ Strategies router imported successfully')"
lsof -i :8000
curl -H "X-API-Key: strategies-api-2024" http://localhost:8001/api/strategies/
curl -X POST -H "X-API-Key: strategies-api-2024" -H "Content-Type: application/json" -d '{"name": "NIFTY Scalping", "type": "scalping", "symbol": "NIFTY", "capital": 50000, "risk_level": "medium", "description": "Quick scalping strategy for NIFTY index"}' http://localhost:8001/api/strategies/
curl -H "X-API-Key: strategies-api-2024" http://localhost:8001/api/strategies/statistics/summary
curl -H "X-API-Key: strategies-api-2024" http://localhost:8001/api/strategies/quota/4
source venv/bin/activate && python -c "
from database import engine, User
from sqlalchemy import text
try:
    with engine.connect() as conn:
        conn.execute(text('ALTER TABLE users ADD COLUMN strategy_limit INTEGER DEFAULT 10'))
        conn.commit()
        print('✅ Added strategy_limit column')
except Exception as e:
    print(f'Column might already exist or error: {e}')
"
source venv/bin/activate && python -c "
from database import SessionLocal, User
db = SessionLocal()
try:
    user = db.query(User).filter(User.username == 'us1001').first()
    if user:
        user.strategy_limit = 10
        db.commit()
        print('✅ Updated user strategy limit')
    else:
        print('❌ User not found')
except Exception as e:
    print(f'Error: {e}')
finally:
    db.close()
"
source venv/bin/activate && rm -f platform.db && python database.py
curl -H "X-API-Key: strategies-api-2024" http://localhost:8001/api/strategies/
lsof -i :3005
kill -9 149684
cd /home/<USER>/trading-platform-manager && npx create-react-app react-frontend
cd /home/<USER>/trading-platform-manager && mkdir -p react-frontend/src/components react-frontend/src/pages react-frontend/public
echo 'Terminal capability test'
ls
cd trading-platform-manager/
ls
cd frontend
ls
rsync -avz --progress --exclude=node_modules ubuntu@***********:~/trading-platform-manager/frontend/ ./frontend-copy
rsync -avz --progress --exclude=node_modules ubuntu@*************:~/trading-platform-manager/frontend/ ./frontend-copy
zip -r frontend.zip frontend -x "frontend/node_modules/*"
sudo apt install zip
zip -r frontend.zip frontend -x "frontend/node_modules/*"
cd ..
zip -r frontend.zip frontend -x "frontend/node_modules/*"
zip -r backend.zip backend -x "backend/venv/*"
echo 'Terminal capability test'
echo 'Terminal capability test'
cd ..
npx create-next-app@latest frontend --ts --tailwind --app --eslint --src-dir
cd home/ubuntu/trading-platform-manager/frontend
cd ./frontend
ls
lsof -i :8000
kill -9 8000
python main.py
python --version
python3 --version
.\venv\Scripts\Activate.ps1
.\venv\Scripts\activated
source venv/bin/activate
python main.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
cd ../backend && python main.py
cd ../backend && python3 main.py
npm run dev
cd ../backend && source venv/bin/activate && python main.py
source venv/bin/activate && python main.py
npx shadcn-ui@latest add button
source venv/bin/activate && python main.py
uvicorn main:app --reload --host 0.0.0.0 --port 8000
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
source venv/bin/activate && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
python create_default_accounts.py
source venv/bin/activate && python create_default_accounts.py
source venv/bin/activate && python main.py
npm run dev
cd frontend && npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge lucide-react
cd ../backend && source venv/bin/activate && python -c "
from database import get_db, User
from sqlalchemy.orm import Session

# Get database session
db = next(get_db())

# Check existing users
users = db.query(User).all()
print('Existing users:')
for user in users:
    print(f'- {user.username} ({user.role}) - Active: {user.is_active}')

if not users:
    print('No users found in database')
"
source venv/bin/activate && python create_demo_users.py
npm uninstall tailwindcss @tailwindcss/postcss
cd ../frontend && npm install -D tailwindcss@^3.4.0 postcss autoprefixer
npx tailwindcss init -p
npm install -D tailwindcss-animate
cd backend && source venv/bin/activate && python database.py
cd backend && source venv/bin/activate && python create_demo_users.py
cd ../backend && source venv/bin/activate && python create_demo_users.py
rm -f platform.db && source venv/bin/activate && python database.py
source venv/bin/activate && python create_demo_users.py
sed -i 's/db\.query(Instance)/db.query(BrokerInstance)/g' routers/instances.py
sed -i 's/Instance\./BrokerInstance./g' routers/instances.py
sed -i 's/InstanceLog/BrokerInstanceLog/g' routers/instances.py
sed -i 's/BrokerBrokerInstance/BrokerInstance/g' routers/instances.py
sed -i 's/Instance\./BrokerInstance./g' services/instance_manager.py
sed -i 's/db\.query(Instance)/db.query(BrokerInstance)/g' services/instance_manager.py
sed -i 's/InstanceLog/BrokerInstanceLog/g' services/instance_manager.py
sed -i 's/= Instance(/= BrokerInstance(/g' services/instance_manager.py
sed -i 's/Instance\./BrokerInstance./g' services/manual_instance_manager.py
sed -i 's/= Instance(/= BrokerInstance(/g' services/manual_instance_manager.py
sed -i 's/InstanceLog/BrokerInstanceLog/g' services/manual_instance_manager.py
grep -r "from database import.*Instance" . --include="*.py"
source venv/bin/activate && python -c "from routers import user_management; print('user_management imported successfully')"
source venv/bin/activate && python -c "import traceback; 
try:
    from routers import auth, instances, admin, settings as settings_router, strategies, profile, user_management
    print('All routers imported successfully')
except Exception as e:
    traceback.print_exc()"
sed -i 's/BrokerBrokerInstance/BrokerInstance/g' services/instance_manager.py
sed -i 's/BrokerBrokerInstance/BrokerInstance/g' services/manual_instance_manager.py
sed -i 's/-> Instance:/-> BrokerInstance:/g' services/instance_manager.py
sed -i 's/-> Instance:/-> BrokerInstance:/g' services/manual_instance_manager.py
sed -i 's/instance: Instance/instance: BrokerInstance/g' services/instance_manager.py
sed -i 's/instance: Instance/instance: BrokerInstance/g' services/manual_instance_manager.py
sed -i 's/Tuple\[Instance,/Tuple[BrokerInstance,/g' services/manual_instance_manager.py
sed -i 's/Instance\./BrokerInstance./g' routers/admin.py
sed -i 's/db\.query(Instance)/db.query(BrokerInstance)/g' routers/admin.py
sed -i 's/InstanceLog/BrokerInstanceLog/g' routers/admin.py
sed -i 's/BrokerBrokerInstance/BrokerInstance/g' routers/admin.py
npm install @radix-ui/react-checkbox @radix-ui/react-dropdown-menu sonner
npm install sonner
cd ../frontend && npm install sonner
npm install sonner
npm install @radix-ui/react-dialog @radix-ui/react-select
npm install @radix-ui/react-checkbox @radix-ui/react-dropdown-menu
cd backend && python create_default_accounts.py
python create_default_accounts.py
cd /home/<USER>/trading-platform-manager/backend && python create_default_accounts.py
npm install class-variance-authority
npm install @radix-ui/react-checkbox @radix-ui/react-dropdown-menu
python add_sample_revenue.py
python update_admin_permissions.py
python test_quota_system.py
pip install pytz
python add_sample_revenue.py
curl -X GET "http://localhost:8000/api/user-management/revenue-details" -H "Authorization: Bearer YOUR_TOKEN"
ps aux | grep uvicorn
pip3 install uvicorn
lsof -i :8000
curl -X GET "http://localhost:8000/api/health"
curl -X GET "http://localhost:8000/api/user-management/revenue-details"
kill 166691
source venv/bin/activate && python main.py
lsof -i :8000
curl -X GET "http://localhost:8000/api/user-management/revenue-details"
curl -X GET "http://localhost:8000/api/user-management/super-admin-revenue"
python create_default_accounts.py
ls -la *.db
rm -f platform.db && source venv/bin/activate && python database.py
htop
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
ls
git clone https://github.com/marketcalls/openalgo.git
ls
source bin/activated/venv
source bin/activate/venv
source venv/bin/activate
python main.py
npm start
npm run
npm start dev
npm run dev
echo 'Terminal capability test'
echo 'Terminal capability test'
