{"name": "@img/sharp-linux-s390x", "dist-tags": {"latest": "0.34.3", "next": "0.34.3-rc.1"}, "versions": {"0.33.0-alpha.10": {"name": "@img/sharp-linux-s390x", "version": "0.33.0-alpha.10", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "0.0.3"}, "dist": {"shasum": "cef46028d676d0f3ae3f5900c72b745cc71af744", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-XWCLwNNFhzcJ8PbIl7CdxJpcJGRRf7msRz3I3opXphExRmOjeIaF0TkImwC+xuooRKvwlk31454ox6QkI98//w==", "signatures": [{"sig": "MEQCIAxapBfL65Yjom/syC04vxkYo9vSfQILeex2/99tzCZqAiB1/i8A6dPiQNxfG+NDd5DPQNRS5caPhRktxKEmuC5AAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297371}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.0-alpha.11": {"name": "@img/sharp-linux-s390x", "version": "0.33.0-alpha.11", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "0.0.3"}, "dist": {"shasum": "edb63f8f1b6e33cf92d1415236d4a6823128dcde", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-gpBYj3BByrVmNlTLx5ZPN50WtbM/5pliB9cPQcQpmWNQKg2zejC1M2MBevufT3HQiIAHhO6DA3fNScHQGRpbFA==", "signatures": [{"sig": "MEQCID0BfK9R1FpEWJsSeWRQJVXB8iJL1800wRUvBrun8NaPAiATg84aKilbhJSwcHIeR/IIJN1HnRgQZa9eXV3kPyQLfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297371}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.0-rc.2": {"name": "@img/sharp-linux-s390x", "version": "0.33.0-rc.2", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "5901170987858d7b62f69e17d4678200a4acfe4d", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.0-rc.2.tgz", "fileCount": 4, "integrity": "sha512-L7rUZIPB7CYX3XWJzQNE7r8jSJ4TpmlmcWEQQGNaqxsi80H6TIqLEQxb54VTZs/MgWXnk0hI3UZicJEfo/bQ+Q==", "signatures": [{"sig": "MEQCIDGMEvRtzFrzkDPQYueZcf5FytTbZfg1dNGekMPx4SeAAiBZzV5rx7aSZcZYCvKZrQH7Jymsplgibr4AAfsiCpYjvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "4b6d49f87364c60ae015825746f365385f1c3b54", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.0.tgz", "fileCount": 4, "integrity": "sha512-TiVJbx38J2rNVfA309ffSOB+3/7wOsZYQEOlKqOUdWD/nqkjNGrX+YQGz7nzcf5oy2lC+d37+w183iNXRZNngQ==", "signatures": [{"sig": "MEYCIQC6fULEYUZXNYWoY98XhlkkvjbenN7wwOgJCSFysJeVWwIhALXlwDEES2ipMis8krtXLawLKJVbohxys+DPzX+r0pq2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305698}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.1-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.1-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "1fd642ddd3b13f72d65e7351e9d51cdd0db39037", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.1-rc.0.tgz", "fileCount": 4, "integrity": "sha512-DqU5xXK656cGC5EQmMkYNDPx1Cp/zUfIuAWLgsFWWtbFLLhwdpFfBGnTqKo547AwoEpCWbcepgGulpeH27pQuA==", "signatures": [{"sig": "MEUCIBLPKzVH/TciawbdY71u0r2u5klQdjcQyzyzcDGYw5CJAiEA28CU0KgrsRlaRvhO4064Kc/OwYLN0w/FQO76c0xwBNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.1-rc.2": {"name": "@img/sharp-linux-s390x", "version": "0.33.1-rc.2", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "2125eed7a347e2a9cdd8dec82d87993664e7658c", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.1-rc.2.tgz", "fileCount": 4, "integrity": "sha512-MVbwhpxdVCLzxMWViwMQNFJvcc+50e9c+H8aIUaHoP1x5PrJU9cimMe61Slg7xvVnkXeU0rMPSfwxLiLpkrBag==", "signatures": [{"sig": "MEYCIQC91eBXZoYOaup7Sjw5JkuvQOGljamiDfI66QR6+tiY1AIhAJD/50TCv7M90hjgWyAl1mSs3IF7akBKxXXJIk8CG/kA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.1-rc.3": {"name": "@img/sharp-linux-s390x", "version": "0.33.1-rc.3", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "264f6225cbfacd96c583b683b06e3e77007ce55d", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.1-rc.3.tgz", "fileCount": 4, "integrity": "sha512-H2fv0kpX5zF7Nn0WkplQ0AZVvDmCI6r7Bdn81HSloPSUOs1Z55Ga7Sv1fNhjgxdO3d7eRcw+Rxypp5/kxU6prQ==", "signatures": [{"sig": "MEUCIFi9gLNVn+ZjuGvI6bJRCOE0D6IuoLDoWsL/b41qFdetAiEAkK9I3//eAxhOoJZE60/jOggfhOUIz2u2AoAklWD28pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.1": {"name": "@img/sharp-linux-s390x", "version": "0.33.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.0"}, "dist": {"shasum": "c554567bb211cb1e916562752e70ef65df0cdcb7", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.1.tgz", "fileCount": 4, "integrity": "sha512-tRGrb2pHnFUXpOAj84orYNxHADBDIr0J7rrjwQrTNMQMWA4zy3StKmMvwsI7u3dEZcgwuMMooIIGWEWOjnmG8A==", "signatures": [{"sig": "MEYCIQD/CNBjsN35uHELDxAL7nsuIyxF0Rgk8yVa+zMpzW5DEwIhAM51diqTd8qzt14zlE67ylNWKWnayDSrcYvt4eyp/4pg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305698}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.2-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.1"}, "dist": {"shasum": "3bc0539ea1e394c1dcb683cf63390417a242cc67", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-y5E9Wp+cqznM07MA1eFxlXWh2zTIb5MOg1R+ShZ4N7tKn0zRYG8/tr8ybf/MT0fZSDzcAaylRmvA2Gmi7JOTTg==", "signatures": [{"sig": "MEUCIALR0DGRYJhMZuh6aXfZ/zKOpzQdxlw03GdVJOEU4ufGAiEAuDfT7i7PtDH/9kt120DqijLQfGvAHqxr0p2PV+egHkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.2-rc.1": {"name": "@img/sharp-linux-s390x", "version": "0.33.2-rc.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.1"}, "dist": {"shasum": "edbc53adef271e46ecb0d259a070c088d1aadaa8", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.2-rc.1.tgz", "fileCount": 4, "integrity": "sha512-zdCeUYClF/B0WdsnN07IsFrsxWwy8HgDDkYiCmme1vSvwVRKy6H0T4VG7IBt8B4HMp1laOA940t4fQ0tPavwsA==", "signatures": [{"sig": "MEUCIB7e2X6u9GDsUCMymyG3aVFpnu3Rf6jSsF2aAsSvmIYyAiEAsBb5Yzhqc6i2AiJ4/G1CmP37PhdTGam/tg0hzWQEoSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305703}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.2": {"name": "@img/sharp-linux-s390x", "version": "0.33.2", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.1"}, "dist": {"shasum": "9c171f49211f96fba84410b3e237b301286fa00f", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.2.tgz", "fileCount": 4, "integrity": "sha512-MBoInDXDppMfhSzbMmOQtGfloVAflS2rP1qPcUIiITMi36Mm5YR7r0ASND99razjQUpHTzjrU1flO76hKvP5RA==", "signatures": [{"sig": "MEUCID0pDwgX7K9MBGM0n25wjbCCPuyLaJn8ZSU0vRgfkVKZAiEA1qP6RlAtfmd9toFiBDP+Hr1C/FW0eGvzSM+bIDvdh10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305698}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.3-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.2"}, "dist": {"shasum": "2bd6193bde5ef10c250936904ff96074ff8cb107", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-jrL7i3i8SYUj+vSbneeS2voAIoiB613Ow5kIMEIkFkB80zQCGpHGEvp57t7T9rPDbzfmfR+LVUUMZGsGMimTJw==", "signatures": [{"sig": "MEQCIFwWJzKPBqv464yorqM5qAgO9WjFPeLN/K2I3bS6een9AiBrjBk+JgSIpnA6TyDYhar4D5QZt4PfOQSiZ60ubWCgjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305679}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.3": {"name": "@img/sharp-linux-s390x", "version": "0.33.3", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.2"}, "dist": {"shasum": "8719341d3931a297df1a956c02ee003736fa8fac", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.3.tgz", "fileCount": 4, "integrity": "sha512-vFk441DKRFepjhTEH20oBlFrHcLjPfI8B0pMIxGm3+yilKyYeHEVvrZhYFdqIseSclIqbQ3SnZMwEMWonY5XFA==", "signatures": [{"sig": "MEYCIQC7PQK+0SIF1p6kvyq6jvFnlmUMf1In57w3K9XKHY1o+QIhAL08pIrAWOTPitiRNaecdK72R32fdGzfeLoZ2Hx2Huqg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305674}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.28"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.4-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.4-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.2"}, "dist": {"shasum": "d762b02b20367adcd39554acdbd1dc28c60c06ab", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.4-rc.0.tgz", "fileCount": 4, "integrity": "sha512-eEFEke/x4+Vb9MRRIC9Fz12VWUcs9EXWXhaiMoeCPSlXYBIMm0GWd70wjcwMKod2HsyoZ96N2THAKNOOsQsoIg==", "signatures": [{"sig": "MEYCIQDOjKfcc+GjABNTbTbqrlH9adO6hVzG4t+3ZuG0EXS4KAIhAPnJ32RGfpJRFPSH2N2Yqg6gyc6r8FPNxDr6uf+3eG4c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305271}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.31"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.4": {"name": "@img/sharp-linux-s390x", "version": "0.33.4", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.2"}, "dist": {"shasum": "119e8081e2c6741b5ac908fe02244e4c559e525f", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.4.tgz", "fileCount": 4, "integrity": "sha512-h3RAL3siQoyzSoH36tUeS0PDmb5wINKGYzcLB5C6DIiAn2F3udeFAum+gj8IbA/82+8RGCTn7XW8WTFnqag4tQ==", "signatures": [{"sig": "MEUCIQCtm+2Rv8rkNn/UUaVfrg/5t1UALmaldvAyEUQ8ou5efwIgbBN5GtcMKGwgClt+DFgv9IvtUFAIHJAdPIKvDgsY+E4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305266}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.31"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.5-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.33.5-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.4"}, "dist": {"shasum": "1c25f66ae267fda9369e347dca10c8ba7ef2662b", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.5-rc.0.tgz", "fileCount": 4, "integrity": "sha512-wJ8j622O1oyKw5FJOOnTklSNBijReKuL6yLjrZW8WqUCkoOVSMflf6moFDCeSlJQhWzbtdiYF2iSbwKppTdYjw==", "signatures": [{"sig": "MEUCIQDafeGl2ktQCc/hKD9UQPKIsQNAjl7kRL9XtiUuVholywIgO0d83R+R3wiGVe7YrvLTXfHCopht2kKNXz0BrJBhgFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 309381}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.5-rc.1": {"name": "@img/sharp-linux-s390x", "version": "0.33.5-rc.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.4"}, "dist": {"shasum": "6aaaa8d3b9bac1fad4a5247a9fa7afafd6b061bf", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.5-rc.1.tgz", "fileCount": 4, "integrity": "sha512-urpBhUxHNgNs/TlhZFxVAu9heGJeKEhlA6apmnHkOjHUhcDbjtcGqKsYr82tlgQA/u2xaQKQ1l2niByQIyzzSw==", "signatures": [{"sig": "MEYCIQCdK7TgumYdoz42zM5BvLL5t9MIxLOUjE24PY3QrnnWMQIhALPE0Ymsa9zNGWbFYv+lfcgg0PxG8lPptdX9cpvcrhOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 309381}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.33.5": {"name": "@img/sharp-linux-s390x", "version": "0.33.5", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.0.4"}, "dist": {"shasum": "f5c077926b48e97e4a04d004dfaf175972059667", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.5.tgz", "fileCount": 4, "integrity": "sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==", "signatures": [{"sig": "MEYCIQCEKk3QHeM9GSVErJGCSEqkDK/9zMMXH6cuXPpPbx7QNQIhAIqqlC10ljhVh8JVz56CEc/XLRXYp3OqHCddK7L0/LEK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 309376}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.0-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.34.0-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0-rc4"}, "dist": {"shasum": "9726c014002970e1f60b7672d57005c6eb68d46f", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.0-rc.0.tgz", "fileCount": 4, "integrity": "sha512-zjcm7vPxPqIfKE5XbSvwMYc7ZYA7GZmCW2+o/B5i5DhoomLrx4IlBjt7+L4WykMdQkrYkDaFmTRORoD6/Veh6g==", "signatures": [{"sig": "MEQCIH3kwPK3kbZt3gTCzm2CnG4Yo9xMy/OFU2YUM8AjdcTRAiADAEV8WTOKU0ZtbCy1K0wZ2g86l1+bZ2/5SOL8C8tk+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 317649}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.0-rc.1": {"name": "@img/sharp-linux-s390x", "version": "0.34.0-rc.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0-rc5"}, "dist": {"shasum": "00d383adcf2f29f27f8d90f7dc11480dbde0e178", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-09Yb3XW2tWEQOdpIFKbhZv6LJxdHDS4prPS3lx3/Rn9KZYSjZ8b0Pz83vje6HOzgWyuHv6zRYoPPn4u8a7Sqsw==", "signatures": [{"sig": "MEQCIDnZQcFIwRFplGckWGXUukqQ/gm9eD1YhxS9l2sdLkemAiB288SFeeCSCaeNcq2QUuDqZ5VtdmWczr6S7qAYv/SXRQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 317673}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.0": {"name": "@img/sharp-linux-s390x", "version": "0.34.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}, "dist": {"shasum": "618f5f8d419c2341bc938975210739f092c35989", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.0.tgz", "fileCount": 4, "integrity": "sha512-04jdT+VCZIqj0RoTEpWXh0lErZC9prhkxEZWrQdGt1MZ368SlvXpKkXCD4Ww5ISc3LexBmfnAW/+ErUmD9sRPQ==", "signatures": [{"sig": "MEUCIQC5T3/tAxs7idTO4jTGGETjhCWmVgp7vdgky/2LgeL2DAIgTjfcMSjLTy95vygAkRHBsm+3j3OqqY7FI5z2WMkuqBM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299168}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.1": {"name": "@img/sharp-linux-s390x", "version": "0.34.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}, "dist": {"shasum": "8ac58d9a49dcb08215e76c8d450717979b7815c3", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.1.tgz", "fileCount": 4, "integrity": "sha512-7s0KX2tI9mZI2buRipKIw2X1ufdTeaRgwmRabt5bi9chYfhur+/C1OXg3TKg/eag1W+6CCWLVmSauV1owmRPxA==", "signatures": [{"sig": "MEUCIQCPwmdy1C9d5Fut+y0GM1G0VRMjMxRjWqj2TexUOzI6zwIgcHFjFwzI4arFpUd+XIQEEHDnrHD6T7UXcW5zZCcxplQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299168}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.2-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.34.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}, "dist": {"shasum": "ba223b928d73693e77d51f9c6e36e9affb468e35", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-8vZ7C6HFne1ST3ipJCXe2+g2ViN1uMZN1Y9g5l8chCUycgvHjNft1+4bhj/UG1lt8bzbv6sL9j/vwUNzU4u2QA==", "signatures": [{"sig": "MEQCIDOofWPeAMsrIdQBbr6nBqCF9iCK7HSTgtkd908B4VPxAiAxrfA9HwzjoFewDVC3J52GGMf308hhcgYzZQkmq0ysDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299173}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.2": {"name": "@img/sharp-linux-s390x", "version": "0.34.2", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.1.0"}, "dist": {"shasum": "82132d158abff57bd90b53574f2865f72f94e6c8", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.2.tgz", "fileCount": 4, "integrity": "sha512-EGZ1xwhBI7dNISwxjChqBGELCWMGDvmxZXKjQRuqMrakhO8QoMgqCrdjnAqJq/CScxfRn+Bb7suXBElKQpPDiw==", "signatures": [{"sig": "MEQCIATLAlSg9uZtJtZbqOiU7Bi/spLkm4KCk/mY1GIksxi7AiAHL2MW3iIQg9wAyDMiot/vIbC4jRTInx39T/d4G/YNeQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299168}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.3-rc.0": {"name": "@img/sharp-linux-s390x", "version": "0.34.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.2.0-rc.2"}, "dist": {"shasum": "82dc107df3d12018932822ee8991374d5421dfd8", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-vhf+iLd+DigFUJnM8OlEkgqqvGHUpmYJNJTnOIbwpTNTROYS6fO1D3wowyGzieznqQMyUTBJEyqqYjpl2+dQug==", "signatures": [{"sig": "MEQCIA2ySSM/R0Z1+ism9VQjAirsZSQ2tTAUx2n53xhW+GQSAiB6GJTy2bD9gUNeR+dh0WViQBIDhCyPhmcjKxxieB75xg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 258210}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.3-rc.1": {"name": "@img/sharp-linux-s390x", "version": "0.34.3-rc.1", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.2.0"}, "dist": {"shasum": "b7c0569bacdaa0d56ff86bfbe44530e8186a13d6", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.3-rc.1.tgz", "fileCount": 4, "integrity": "sha512-7yWWdbN8IfjR5PGPn2eOKHRxsH0uTBPzjwEwZFU20Idg5KfgiZo3btYVjNw55aonzt3CLltOcTWp0S2F1HsG6w==", "signatures": [{"sig": "MEUCIAPzAeDQuiWhVQSamQZHNfhGR8PQNDeQRSUKmeDrJ8m0AiEA2FSn+SSjZ+QGsETNoH8JVdsmUXTyBgn0ABSDERh0zhM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 258213}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}, "0.34.3": {"name": "@img/sharp-linux-s390x", "version": "0.34.3", "optionalDependencies": {"@img/sharp-libvips-linux-s390x": "1.2.0"}, "dist": {"integrity": "sha512-3gahT+A6c4cdc2edhsLHmIOXMb17ltffJlxR0aC2VPZfwKoTGZec6u5GrFgdR7ciJSsHT27BD3TIuGcuRT0KmQ==", "shasum": "48e27ab969efe97d270e39297654c0e0c9b42919", "tarball": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.3.tgz", "fileCount": 4, "unpackedSize": 258208, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDL0WbT9pDNV3FY57eWYfAZZ25qbKDXfN2tT2Fk50O06AiA9MpB8jAJ1lR54tKEp+EsOOPRH1KObMXtSRzasIz91Cg=="}]}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["s390x"]}}, "modified": "2025-07-10T07:57:29.388Z"}