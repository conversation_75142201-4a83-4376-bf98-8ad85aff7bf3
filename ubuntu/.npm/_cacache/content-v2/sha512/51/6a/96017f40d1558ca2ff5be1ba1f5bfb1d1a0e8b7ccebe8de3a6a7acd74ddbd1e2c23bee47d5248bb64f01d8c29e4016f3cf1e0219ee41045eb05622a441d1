{"name": "tinyglobby", "dist-tags": {"latest": "0.2.14"}, "versions": {"0.1.0": {"name": "tinyglobby", "version": "0.1.0", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "ebb1ca007848d924bccd5dec34be30cce264f3a7", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.0.tgz", "fileCount": 9, "integrity": "sha512-8d+AJ9Q+O4dPb8XhBmJklsIyRXJoO3X8KTaBXXOwWZrNb+GxPVdqljmqvfYhmcKRNcbQ6M4cRvnF4LGOynwsiw==", "signatures": [{"sig": "MEUCIQCsQEU/ZBSBrKj2jR6LlGh765Np+/kA5TzlwRipZ6+4LgIgKxPgY360h/sB0otUDcFbfuBeEtpMQCBGosrh07ciTco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 14678}, "engines": {"node": ">=12.0.0"}}, "0.1.1": {"name": "tinyglobby", "version": "0.1.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "a9b2a1ab4cd086888f45d423660ce9cc2719966f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.1.tgz", "fileCount": 9, "integrity": "sha512-DTTozc6Ce8ave4bttVQBUf7l4POgqgljzlVBuiJ8hNHuVESjYiZtVEpyRFoq0ripdtOxjlOU9oKy1oSPxsYfFQ==", "signatures": [{"sig": "MEYCIQDZ/94JvoA0/rPZ1uFZ0+0HQ4qqRMwD58HYjRL8BWeDLwIhAKExIcUQORYCWlzQIiTNgBSRpBlwVTNnxZSV8rGfjY3S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 15919}, "engines": {"node": ">=12.0.0"}}, "0.1.2": {"name": "tinyglobby", "version": "0.1.2", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "ebb67fce47f865ab74f2dec887347c81a50c591e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.1.2.tgz", "fileCount": 9, "integrity": "sha512-dCTuCKyovvOdzcdtU8ovtfY3w4OWcRO/h31kim9UV47GFwEqTCCgILZNiF6Kx4+ItjyxX+7lUF2LwGIJjBF9jQ==", "signatures": [{"sig": "MEUCIQDdVESL7u7tma9AwfqutgdNjJDP5L1dpCmdT114pECXUwIgdEmmsgMYrbbbFr9R9PA0BJSaZvxaNDU43FSDQrjtRTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16909}, "engines": {"node": ">=12.0.0"}}, "0.2.0": {"name": "tinyglobby", "version": "0.2.0", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "a9d9ef839f87a505936a31882b1e8564c86a972f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.0.tgz", "fileCount": 9, "integrity": "sha512-+clyYQfAnNlt5a1x7CCQ6RLuTIztDfDAl6mAANvqRUlz6sVy5znCzJOhais8G6oyUyoeeaorLopO3HptVP8niA==", "signatures": [{"sig": "MEYCIQDUQgHCBlItWao0RbDKzTQ+ws76CfLwmmQKqw87Umlp1QIhAMtKKGO9EkuYvt3xJ/+gISXgs++QjXBbJMNmhKMdy8Df", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23629}, "engines": {"node": ">=12.0.0"}}, "0.2.1": {"name": "tinyglobby", "version": "0.2.1", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "d6147f5c5c918120634ebb689a3de745465908da", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.1.tgz", "fileCount": 9, "integrity": "sha512-9P+ZLlLTDTjUxMw9aPgWJfBgYiUmW4Ki5XYoaFFwNPgXt4uRDdQOiatse8s/K64DjHNWE94FBe59lJ0oAeCWuQ==", "signatures": [{"sig": "MEUCIFE9iKEKlcyYI65sL8PJYklaaw/95Ha4RGoYO8czB2BWAiEAueWHcJI1M0TyyKNLSZQzmJ5nJqOf+EdD9YbYelvMLhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24403}, "engines": {"node": ">=12.0.0"}}, "0.2.2": {"name": "tinyglobby", "version": "0.2.2", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.2.2", "typescript": "^5.5.3", "@types/node": "^20.14.11", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.0"}, "dist": {"shasum": "c3b21f177d41a3c86b122cfbd3dd618870f3689f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.2.tgz", "fileCount": 9, "integrity": "sha512-mZ2sDMaySvi1PkTp4lTo1In2zjU+cY8OvZsfwrDrx3YGRbXPX1/cbPwCR9zkm3O/Fz9Jo0F1HNgIQ1b8BepqyQ==", "signatures": [{"sig": "MEYCIQDgjtUaf6DFBnfq5NleIrYR6mUiiOt3E8H6vuYGrsi38wIhAIjU/p+Qob4l/HkZpiZlu9NY5rhY2dgLcnHOJZdPqPfz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 26384}, "engines": {"node": ">=12.0.0"}}, "0.2.3": {"name": "tinyglobby", "version": "0.2.3", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "841ef6d6c039eaca6cc2c0d4850df82f4c0acf0e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.3.tgz", "fileCount": 9, "integrity": "sha512-JvE0yT/RJulHtCPZG0tII18fufosZzNzRn2UFpcZNRScpN7ayko6tKIxa+dCSyPYig5gxz3mM4ReOlSvHNwzSA==", "signatures": [{"sig": "MEUCIQD9yGCVyhKJDvPfubRB/npvjlhlVAUBvvT6YhZyE3RFUgIgLh49NtvW7kyeR27cSEqOLp9MJKnh8j55RFAKTRMWflk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37976}, "engines": {"node": ">=12.0.0"}, "deprecated": "Versions 0.2.3 and 0.2.4 contain a critical bug. Please upgrade to 0.2.5 or higher"}, "0.2.4": {"name": "tinyglobby", "version": "0.2.4", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "a720586c21ed8fba5f485bd5dfbb0d84e3641179", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.4.tgz", "fileCount": 9, "integrity": "sha512-h2O9NpZ4xnq0HIcsS1+5d9CzKSThHSmAO4kMGFMD9rTzUfZWjvMVVj8qq+wpO4l7+xb3YcjtXr5z6N0W0EDPvw==", "signatures": [{"sig": "MEUCICaQQI3OXjmnM/BPtWJysVbowjPOwNhuqCp3uClbCW8zAiEAy/1F8QjKo1SKJjg1ksoKKmSodjhauPJ+x+S393cJPLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 38286}, "engines": {"node": ">=12.0.0"}, "deprecated": "Versions 0.2.3 and 0.2.4 contain a critical bug. Please upgrade to 0.2.5 or higher"}, "0.2.5": {"name": "tinyglobby", "version": "0.2.5", "dependencies": {"fdir": "^6.2.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.2.4", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "8cdd1df1b155bf2a3c4d5ea2581489f967a38318", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.5.tgz", "fileCount": 9, "integrity": "sha512-Dlqgt6h0QkoHttG53/WGADNh9QhcjCAIZMTERAVhdpmIBEejSuLI9ZmGKWzB7tweBjlk30+s/ofi4SLmBeTYhw==", "signatures": [{"sig": "MEYCIQCfI1f/DSfgJC20LRDJachSs3n8zqQes0m9ifsNId1QNgIhAJ29Gc2YUuUkrkFoXgWyHCJc75HPKS2qfxqT3ufHcn7O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 38274}, "engines": {"node": ">=12.0.0"}}, "0.2.6": {"name": "tinyglobby", "version": "0.2.6", "dependencies": {"fdir": "^6.3.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.2.4", "fs-fixture": "^2.4.0", "typescript": "^5.5.4", "@types/node": "^22.5.4", "@biomejs/biome": "^1.8.3", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "950baf1462d0c0b443bc3d754d0d39c2e589aaae", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.6.tgz", "fileCount": 9, "integrity": "sha512-NbBoFBpqfcgd1tCiO8Lkfdk+xrA7mlLR9zgvZcZWQQwU63XAfUePyd6wZBaU93Hqw347lHnwFzttAkemHzzz4g==", "signatures": [{"sig": "MEUCIQDRT8Z00WUYvS400yal7FRc/0HB/lwXtEoQzNWT+4WgfgIgWgqt1vIyitkfQ4AQEwuAbyWaVXtCVWzbY5mlE4oRV/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39458}, "engines": {"node": ">=12.0.0"}}, "0.2.7": {"name": "tinyglobby", "version": "0.2.7", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "36f583c166ac2d91f47c799d6e2e256fdcfb3a56", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.7.tgz", "fileCount": 7, "integrity": "sha512-qFWYeNxBQxrOTRHvGjlRdBamy8JFqu6c0bwRru9leE+q8J72tLtlT0L3v+2T7fbLXN7FGzDNBhXkWiJqHUHD9g==", "signatures": [{"sig": "MEUCIQCBKgHMLc9GnINUaFINoWHfzoctIeRGGiNnDgd0qgLgOwIgSVPGvpqiIS9ecmqlWGERpOwR6NA5XPe0sPOWm7Luiic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23531}, "engines": {"node": ">=12.0.0"}}, "0.2.8": {"name": "tinyglobby", "version": "0.2.8", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "dfa666c75551828580bfe900b86ebf3952bd4642", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.8.tgz", "fileCount": 7, "integrity": "sha512-AMLZywN0vbhiZi2neFEaj9VIIxC+PjDMsp0nAK6tpR86LJavZgHqGz0S/FOONwBygC+mu7R0/TyAQw0gx0Mu9Q==", "signatures": [{"sig": "MEQCIG0ETR6ZMbfQMn84DdZsIY6OmDPU1TVxT826jzGd9boTAiBhDoxKilIeeGROdYf3n/q/GfrLeI/toBZkTKR/MT+i/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23643}, "engines": {"node": ">=12.0.0"}}, "0.2.9": {"name": "tinyglobby", "version": "0.2.9", "dependencies": {"fdir": "^6.4.0", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.0", "fs-fixture": "^2.4.0", "typescript": "^5.6.2", "@types/node": "^22.7.4", "@biomejs/biome": "^1.9.2", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "6baddd1b0fe416403efb0dd40442c7d7c03c1c66", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.9.tgz", "fileCount": 7, "integrity": "sha512-8or1+BGEdk1Zkkw2ii16qSS7uVrQJPre5A9o/XkWPATkk23FZh/15BKFxPnlTy6vkljZxLqYCzzBMj30ZrSvjw==", "signatures": [{"sig": "MEQCIGFLIJGQyecptXzxY5xMmGo9VmLgVTI7bVid+X6pa9B4AiBJ3NvivHLFz8CPx21CC4wRfYjJn3fQMwIdYGKlioe29Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23683}, "engines": {"node": ">=12.0.0"}}, "0.2.10": {"name": "tinyglobby", "version": "0.2.10", "dependencies": {"fdir": "^6.4.2", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.4", "fs-fixture": "^2.5.0", "typescript": "^5.6.3", "@types/node": "^22.7.9", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.1"}, "dist": {"shasum": "e712cf2dc9b95a1f5c5bbd159720e15833977a0f", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.10.tgz", "fileCount": 7, "integrity": "sha512-Zc+8eJlFMvgatPZTl6A9L/yht8QqdmUNtURHaKZLmKBE12hNPSrqNkUp2cs3M/UKmNVVAMFQYSjYIVHDjW5zew==", "signatures": [{"sig": "MEUCIQDAo8iX7Ppg76qMa4EK7T87KEZ0JUGFjgkDVo/Tvg1RugIgL+ejkOtwGaJHSblO3VaKtHCFp5zrjOjxd49Qcs4qxJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24406}, "engines": {"node": ">=12.0.0"}}, "0.2.11": {"name": "tinyglobby", "version": "0.2.11", "dependencies": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.6", "fs-fixture": "^2.7.0", "typescript": "^5.7.3", "@types/node": "^22.13.4", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.2"}, "dist": {"shasum": "9182cff655a0e272aad850d1a84c5e8e0f700426", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.11.tgz", "fileCount": 7, "integrity": "sha512-32TmKeeKUahv0Go8WmQgiEp9Y21NuxjwjqiRC1nrUB51YacfSwuB44xgXD+HdIppmMRgjQNPdrHyA6vIybYZ+g==", "signatures": [{"sig": "MEUCIQDgPVO6PgBMj2Y3pe+VffHlAOnt1bk+Losz5VOg3FcYSAIgcqCNOqNSPlN6w0GYfH10UEFaJNuwjfZhAJaYZeupVTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30739}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "0.2.12": {"name": "tinyglobby", "version": "0.2.12", "dependencies": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.3.6", "fs-fixture": "^2.7.0", "typescript": "^5.7.3", "@types/node": "^22.13.4", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^3.0.2"}, "dist": {"shasum": "ac941a42e0c5773bd0b5d08f32de82e74a1a61b5", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz", "fileCount": 7, "integrity": "sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==", "signatures": [{"sig": "MEUCIGJLsFelwogkDd3dXMsWAE+oWm0X3iVuYHMxSnl6NsF3AiEAg7/khcs++cI+4ho5DgCWLAPoLFJzpuTAVGVgKXT0P4g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 31060}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "0.2.13": {"name": "tinyglobby", "version": "0.2.13", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "devDependencies": {"tsup": "^8.4.0", "fs-fixture": "^2.7.1", "typescript": "^5.8.3", "@types/node": "^22.14.1", "@biomejs/biome": "^1.9.4", "@types/picomatch": "^4.0.0"}, "dist": {"shasum": "a0e46515ce6cbcd65331537e57484af5a7b2ff7e", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.13.tgz", "fileCount": 7, "integrity": "sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==", "signatures": [{"sig": "MEQCIEcWOwnZOUyi8rr8gfrWHPoTrBM52myZjCzefAhi1Ns/AiB1Gr34f+ppDrcMUsGRXRLGb0v0V8DTzVE8EETlTaaIyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 31264}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "0.2.14": {"name": "tinyglobby", "version": "0.2.14", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.21", "@types/picomatch": "^4.0.0", "fs-fixture": "^2.7.1", "tsdown": "^0.12.3", "typescript": "^5.8.3"}, "dist": {"integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "shasum": "5280b0cf3f972b050e74ae88406c0a6a58f4079d", "tarball": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz", "fileCount": 7, "unpackedSize": 31120, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyglobby@0.2.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCZpOR2Ndtzv/PgDNuWql51hjonKXY/551SDuQOyR+RpwIgdOhSpkPjekoiw70WoHzlF1pp6/yOxVVMaPgF/e6nTQo="}]}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}}, "modified": "2025-05-25T16:19:06.334Z"}