{"name": "@img/sharp-wasm32", "dist-tags": {"latest": "0.34.3", "next": "0.34.3-rc.1"}, "versions": {"0.33.0-alpha.11": {"name": "@img/sharp-wasm32", "version": "0.33.0-alpha.11", "dependencies": {"@emnapi/runtime": "^0.43.1"}, "dist": {"shasum": "7a0485f3883712dcd48c87ad12afd0ea629fb8cc", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.0-alpha.11.tgz", "fileCount": 7, "integrity": "sha512-txfFSnyzLdfydpbK9tlO+Hd497bkzFbBhHQWGQq7ZJ2N2CjKOmsu0SpZAOOEVonNAB1tGxjAvrXKv8rB0+WRsQ==", "signatures": [{"sig": "MEYCIQDZ/xBqGcAHhIpCxDqIAiWPLaMQ1nD08XXBuzBtz9WPFAIhAIBmsdxavMyTOPl39fXfQ2SQ8PYbGTZhnnXkGhtRyAiC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10782916}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.0-rc.2": {"name": "@img/sharp-wasm32", "version": "0.33.0-rc.2", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "789b300943bf671569bc93801355bed1b0c01df0", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.0-rc.2.tgz", "fileCount": 7, "integrity": "sha512-2MJ9b3dwzMXfmz+j04+l6zrGui0YMOO/7uiR//53AaDc1ycOEd4FL0pfINBmqrOYKHjfzO08PMA7ZhEaJJT8JQ==", "signatures": [{"sig": "MEUCIA7tgebqIfu/U1lMjkMTKesn1veQTXU6XmeQk+p8zyOXAiEAtORxfuDthSoByZaxDE2WOPOXb5JOfWvQm4lkrlzGbnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791049}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.0": {"name": "@img/sharp-wasm32", "version": "0.33.0", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "5b085509f04256c43f201552fce5144fe0bd66b9", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.0.tgz", "fileCount": 7, "integrity": "sha512-Hn4js32gUX9qkISlemZBUPuMs0k/xNJebUNl/L6djnU07B/HAA2KaxRVb3HvbU5fL242hLOcp0+tR+M8dvJUFw==", "signatures": [{"sig": "MEUCIBwAhF5S5O8Wtm5PrCtNLRtjTwFZKdYmayNottotSkhrAiEAkDjZF+WvRfcsi4RFfqfLzfMRCamCjz4JNY5pwX/+ITM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791044}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.1-rc.0": {"name": "@img/sharp-wasm32", "version": "0.33.1-rc.0", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "d553d6cb4540401508021976c5fcce781d0db1d2", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.1-rc.0.tgz", "fileCount": 7, "integrity": "sha512-u6mjoMN415svRWBzNdawmxG5sTymyCYQVgGEH8RRazex22lsuyirAc4onpdybkWypu35OKwQoitEpimYSJd+cg==", "signatures": [{"sig": "MEQCIBVnNNXCLUo34xe4/2+5Bz6H9kKSf7+iZgDDeReGtUrvAiA54ZKuRo2si2+IVa9Q+gmjq140oyydALawEpeCl764rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791049}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.1-rc.2": {"name": "@img/sharp-wasm32", "version": "0.33.1-rc.2", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "435579984e19f0b18205d4c7286032100851de6b", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.1-rc.2.tgz", "fileCount": 7, "integrity": "sha512-XKJtiJozV1PZBqillWYPxC4c9f502blUXVIoUuzwENpClBzLzhJrpewfgdtLcTHDK2Nu5c2IG+kcqrp2x7Qkwg==", "signatures": [{"sig": "MEQCIB2uK8U9S3ng6w22OsYhlQiaTFGlP6ox5JzniHubntMhAiB7epLlvP5LVUQXAXIqFaAjgJBjqMeZQ55hERsOW3KehQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791217}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.1-rc.3": {"name": "@img/sharp-wasm32", "version": "0.33.1-rc.3", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "90f536ddb6da9ad243c0695d62225e97cbbf2f80", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.1-rc.3.tgz", "fileCount": 7, "integrity": "sha512-aLruzO0wv3kX3DJOwUKA2G5n9tlT67r45QBHjMjMsQ0XVT+GT/YnvGfuasMw5GaifjIHo9tFIKCdvUJdt3ahBg==", "signatures": [{"sig": "MEQCIEtAxknxaVwNGVlZMNm6Un8a6SIM1wsKen213cmhnV2cAiApkLJp/yXhqfGxUZzSvtMtJoTcP5ameOVMnBbYTDIQNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791217}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.1": {"name": "@img/sharp-wasm32", "version": "0.33.1", "dependencies": {"@emnapi/runtime": "^0.44.0"}, "dist": {"shasum": "aa6f33a8535e6bd4a66c59aeb569499db9d30043", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.1.tgz", "fileCount": 7, "integrity": "sha512-vWI/sA+0p+92DLkpAMb5T6I8dg4z2vzCUnp8yvxHlwBpzN8CIcO3xlSXrLltSvK6iMsVMNswAv+ub77rsf25lA==", "signatures": [{"sig": "MEUCIGpaKNSArVFZGRnIEgPnbqL06KwpYbMHX1lrZUSnSlsKAiEA4vw8w+uUL7uP9iPT1y4IpW5SWCFduSeT4c0IoTo/6m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10791212}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.2-rc.0": {"name": "@img/sharp-wasm32", "version": "0.33.2-rc.0", "dependencies": {"@emnapi/runtime": "^0.45.0"}, "dist": {"shasum": "9e285362f6fe80b3a02ad2cffec90b6e56e7ae19", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.2-rc.0.tgz", "fileCount": 7, "integrity": "sha512-F1XpBSbRBdE+FQk5KDHrS+rV50cAlaDDiP/UfVxLTdSF1K1Ben1sktDaZtT3CDIOgT32KRdXyfbE/v7apBq2ww==", "signatures": [{"sig": "MEUCIQDIkuA+AgJGZcGA/gbhXzcnmtMyuMcUiV5IbsYE7EZ6SAIgDI76SMRfCmmmXbKAbapFgyFb1AmpD0llWmlyq9Jgk5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10815975}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.2-rc.1": {"name": "@img/sharp-wasm32", "version": "0.33.2-rc.1", "dependencies": {"@emnapi/runtime": "^0.45.0"}, "dist": {"shasum": "79a649657f68d5e77411eae77ef8a0c991d48811", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.2-rc.1.tgz", "fileCount": 7, "integrity": "sha512-TLK1mr4TAyRbIES+/4bznoFLORIkHmPa6oC3iENr1XgVNLWwqMFAiBG10w6bjPqid89Kbt0nmFg5fAcJfi1t/w==", "signatures": [{"sig": "MEYCIQCOk1h4YCr0gqg6D6PeRKLo/YRRYJbwoK8Ib5QFyQ6c2QIhAK+qCmj9qh6iMTZ7pJWAMIFKRRYqiuBTJ9zn3HIanqJl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10815975}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.2": {"name": "@img/sharp-wasm32", "version": "0.33.2", "dependencies": {"@emnapi/runtime": "^0.45.0"}, "dist": {"shasum": "38d7c740a22de83a60ad1e6bcfce17462b0d4230", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.2.tgz", "fileCount": 7, "integrity": "sha512-fLbTaESVKuQcpm8ffgBD7jLb/CQLcATju/jxtTXR1XCLwbOQt+OL5zPHSDMmp2JZIeq82e18yE0Vv7zh6+6BfQ==", "signatures": [{"sig": "MEUCIQCeB2Ju4ax9wXhTJWewjxJDEIWWGgfxNhJixI2cYLseJQIgJPuG6ELHSCR3af5p3jo96vT0oc0sGtuKiZVdIy1fyko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10815970}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.3-rc.0": {"name": "@img/sharp-wasm32", "version": "0.33.3-rc.0", "dependencies": {"@emnapi/runtime": "^1.1.0"}, "dist": {"shasum": "9618634b46d305a376939683f936c06b96d3c05b", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.3-rc.0.tgz", "fileCount": 7, "integrity": "sha512-9eSgiFs9QHLJS9uBPpqhtW15CskgWSSOGei3HjHhSCvQHmtcVAB+bR1pbnQfsiGZCGoQHCQ7YqFuQhgoUKzICQ==", "signatures": [{"sig": "MEUCIQDSUbwZ6o9oZoIXaIREI3HmEKKhYxOarZ0cKUMuIYyMWQIgL6IWDcdn6Gh+PkgX8TTTdsr//xU2bgn+y9Y3dktbaKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10753210}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.3": {"name": "@img/sharp-wasm32", "version": "0.33.3", "dependencies": {"@emnapi/runtime": "^1.1.0"}, "dist": {"shasum": "340006047a77df0744db84477768bbca6327b4b4", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.3.tgz", "fileCount": 7, "integrity": "sha512-68zivsdJ0koE96stdUfM+gmyaK/NcoSZK5dV5CAjES0FUXS9lchYt8LAB5rTbM7nlWtxaU/2GON0HVN6/ZYJAQ==", "signatures": [{"sig": "MEYCIQDJG3je0mKnTbO1uhym3Rhd3aMr/lATIkXFrif10VHg2QIhAKRvm0kk+Zvgu0j0ZDMjU+WApdldmXcly9eMHfuabgY8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10753205}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.4-rc.0": {"name": "@img/sharp-wasm32", "version": "0.33.4-rc.0", "dependencies": {"@emnapi/runtime": "^1.1.1"}, "dist": {"shasum": "69307e7fae74cdf06ea155d9b2da022eaa197672", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.4-rc.0.tgz", "fileCount": 7, "integrity": "sha512-zx3Z4gRh3V7uFblId/jMBXcW+6N2D9msfBOj46P5d8XioMmJX5Hv+MUxJ9Oyxw/wG3Iv5CNcODG8qxA9nMjgcA==", "signatures": [{"sig": "MEUCIE5UVdl+cM+LerqG4mArhoHFRyE+jA9Y71oSKMMxxP1KAiEA3DXROHBLw4k5fZUHDrBkaEPuFonxnLjY66ogh6lfaCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10753554}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.4": {"name": "@img/sharp-wasm32", "version": "0.33.4", "dependencies": {"@emnapi/runtime": "^1.1.1"}, "dist": {"shasum": "88e3f18d7e7cd8cfe1af98e9963db4d7b6491435", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.4.tgz", "fileCount": 7, "integrity": "sha512-Bmmauh4sXUsUqkleQahpdNXKvo+wa1V9KhT2pDA4VJGKwnKMJXiSTGphn0gnJrlooda0QxCtXc6RX1XAU6hMnQ==", "signatures": [{"sig": "MEYCIQC0d1EO+nV0lkrkFHDhH4iUvcK7INFUY28mVbKhCOLIZwIhANv8V8BAA1vXz9nMLd5pN8Dv5fqm7DmkLVLlt5Wh3v1A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10753549}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.5-rc.0": {"name": "@img/sharp-wasm32", "version": "0.33.5-rc.0", "dependencies": {"@emnapi/runtime": "^1.2.0"}, "dist": {"shasum": "66954c74f8ad9138adf71ebd38b8fcbc10062aed", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.5-rc.0.tgz", "fileCount": 7, "integrity": "sha512-obeczKre3s0Uv4dW/s6Ej+so2lM1PJPxqLX9Tzib1nOUYAYTHiT4tnNSkNVQL5ksPGpmlo8vo9ysuOQgFQYrVA==", "signatures": [{"sig": "MEQCIDHMRRml9CKLmiXE1n+CNP6+nZ5F1WuSjvJYH9WuyGTDAiBnLoBqcaIVcoENUsE8upmlsqUb96n/IFYygiiMwbxkEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10926954}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.5-rc.1": {"name": "@img/sharp-wasm32", "version": "0.33.5-rc.1", "dependencies": {"@emnapi/runtime": "^1.2.0"}, "dist": {"shasum": "cc1ad02c5b2de37096b5214ed47830874b499bf9", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.5-rc.1.tgz", "fileCount": 7, "integrity": "sha512-XPuODcozSBAw93fWjhZQ8wHiIbvTkjCG3JO9uFJuu2MbMv2nvRMUdW6cj486gPMHgnQZmUQHsLdw1HMu+lx8IA==", "signatures": [{"sig": "MEQCICZJHExQ0ZenBpEj9HedczRtjPdFhcHs2o/JGnj7BKe2AiAUtf2GLTxG6TWfqnnm+MqVjdjgPb8//BQSvPUeYJXf1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10926954}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.33.5": {"name": "@img/sharp-wasm32", "version": "0.33.5", "dependencies": {"@emnapi/runtime": "^1.2.0"}, "dist": {"shasum": "6f44f3283069d935bb5ca5813153572f3e6f61a1", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.5.tgz", "fileCount": 7, "integrity": "sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==", "signatures": [{"sig": "MEYCIQCQLKYQfHSUHUxDAPqc8z/xKmDMch9lmyrCAZHxtY3R0QIhAO5kHDJSKDzj5vNsUFbOoMFbap6VdS0Vxx0ZUzB/tT40", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10926949}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.0-rc.0": {"name": "@img/sharp-wasm32", "version": "0.34.0-rc.0", "dependencies": {"@emnapi/runtime": "^1.3.1"}, "dist": {"shasum": "db76f7a68431de5c50f810bffe81ec51c26690f5", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.0-rc.0.tgz", "fileCount": 6, "integrity": "sha512-8mUPtwQXmw5YbUJMZjIhtuF+MpX+8ib2aQ4f143y5EFi4yej3qzNw8yxWUaor3VxaZMJNwTgG23K75Hj0eymTw==", "signatures": [{"sig": "MEYCIQCR6iqG2coBDHH/Kvi2f7NI0XHrj5I2ShBqJhCz+BtVDwIhAPVOaUA+e/cHoiO2XfNVgjfhC+CkkPWp9SJoYZR9qJiV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10865547}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.0-rc.1": {"name": "@img/sharp-wasm32", "version": "0.34.0-rc.1", "dependencies": {"@emnapi/runtime": "^1.4.0"}, "dist": {"shasum": "51f59ce3c8e92974d11a94966da06a7641f9aa66", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-QhLEfqPPRGRCPrtQpnFH1rjGvyWyGztbZ3ymzHH9dgHnZ8SvfsSBbfzu5+H9+dqmglh9AK0KxwMt3aObXLIHXw==", "signatures": [{"sig": "MEQCIAvEWGaIo6YoYi77bmduBjbfaNMHyP/6SXXOe1DD5+ixAiB8BJfX/qqwvTPBczV+M/IIAIVMDvzR+Nl5gUk2SzAPyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10856386}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.0": {"name": "@img/sharp-wasm32", "version": "0.34.0", "dependencies": {"@emnapi/runtime": "^1.4.0"}, "dist": {"shasum": "5a1be80a90892b60defd0eeb21b7c9dc77ca5c47", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.0.tgz", "fileCount": 6, "integrity": "sha512-oI6xsOqLHhRA3LSZb07KW3dMAmo1PpyAxwdHkuiC5+N8HzodpqXusOtzBEXKeFG8Za5ycry0xLYsu7hG5aUxoQ==", "signatures": [{"sig": "MEQCIGIP9386zKKOzrvG/3U7sSQfQj6uHmHEup0jvfgT8VwdAiAkbRKu9OAqRLknXJSDjAeQ1ekyAjSnsfPsA5Mb79vJaw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10853442}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.1": {"name": "@img/sharp-wasm32", "version": "0.34.1", "dependencies": {"@emnapi/runtime": "^1.4.0"}, "dist": {"shasum": "f7dfd66b6c231269042d3d8750c90f28b9ddcba1", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.1.tgz", "fileCount": 6, "integrity": "sha512-YDybQnYrLQfEpzGOQe7OKcyLUCML4YOXl428gOOzBgN6Gw0rv8dpsJ7PqTHxBnXnwXr8S1mYFSLSa727tpz0xg==", "signatures": [{"sig": "MEUCIQCvTvYhgCXouTpz1sFf6ZznV/pEJ1Yu5h5/8kZpSF0mrAIgIAXkZggaD6n9BBy48Ky9eTCrp/ZSzBNtp1n3ZlYtMRE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10853442}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.2-rc.0": {"name": "@img/sharp-wasm32", "version": "0.34.2-rc.0", "dependencies": {"@emnapi/runtime": "^1.4.3"}, "dist": {"shasum": "3b798e22b2fccd99dff5d2e3fd01736558e8a6e7", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.2-rc.0.tgz", "fileCount": 6, "integrity": "sha512-j3yw42OREdcSo7cYqcFWirf9nNIDiWuIDHdVR1JgOv+R0NhV9xAVEhiuY633DNqmtTQlJkHFERIQ+aUGbCDlag==", "signatures": [{"sig": "MEQCIE7fv7rHvr2xAazTLb6K3JgpXmQAGZQCLihqDGb24N02AiBGZnA90ma76JwKNLvZUjgD6XSzhPG1+cEPqK02WscqbQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10853576}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.2": {"name": "@img/sharp-wasm32", "version": "0.34.2", "dependencies": {"@emnapi/runtime": "^1.4.3"}, "dist": {"shasum": "b1dd0bab547dccf517586eb1fa5852160bba3b82", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.2.tgz", "fileCount": 6, "integrity": "sha512-/VI4mdlJ9zkaq53MbIG6rZY+QRN3MLbR6usYlgITEzi4Rpx5S6LFKsycOQjkOGmqTNmkIdLjEvooFKwww6OpdQ==", "signatures": [{"sig": "MEQCIA+K8hfEWSY5cThHDqP1H1TR6Ov3jD2ednyFP52T2cbqAiB2zrxv7cBigNktCFKTw68yqPjcB5P0JLqkTSfcXAJNKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10853509}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.3-rc.0": {"name": "@img/sharp-wasm32", "version": "0.34.3-rc.0", "dependencies": {"@emnapi/runtime": "^1.4.3"}, "dist": {"shasum": "fc429b4684203a604744fd101feada7eab98b627", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.3-rc.0.tgz", "fileCount": 6, "integrity": "sha512-sMuk2sifPxhia2+zte4xQNn1u22JZ1gnNupXFgUU/uunDRq2z1psoM58DIh4ZMhcnAD7iqhyjBSfK3AVqjCM6Q==", "signatures": [{"sig": "MEYCIQCRaDHFWiHELhu5Xvg/jVAz1nyh2Nn1vCw6c2Wxz/36RAIhANTCinXx4tHB/AD4AGFQUZipLjqkaGuy3oaOHG8c8BeA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10550673}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.3-rc.1": {"name": "@img/sharp-wasm32", "version": "0.34.3-rc.1", "dependencies": {"@emnapi/runtime": "^1.4.4"}, "dist": {"shasum": "7bca56e660078b197d8527172c69819bee9fc8d3", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.3-rc.1.tgz", "fileCount": 6, "integrity": "sha512-R4vg7vpIMdx6MT5R8Wx99unxzzBT2f7Fd4/OPP+Yp0pGtLONrhA8eL7LF06OGNF3I6l9fiuQgtLbPdE0jcq0GQ==", "signatures": [{"sig": "MEYCIQDpLopSks6mtOJG9KADUruJVGLU/cgf9OgZ1b9L3qCVcQIhAKNDVTZAHw1vjUP+kRQ+9VCQ2mwpq00axyBqEXQrXygk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10630063}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}, "0.34.3": {"name": "@img/sharp-wasm32", "version": "0.34.3", "dependencies": {"@emnapi/runtime": "^1.4.4"}, "dist": {"integrity": "sha512-+CyRcpagHMGteySaWos8IbnXcHgfDn7pO2fiC2slJxvNq9gDipYBN42/RagzctVRKgxATmfqOSulgZv5e1RdMg==", "shasum": "c1dcabb834ec2f71308a810b399bb6e6e3b79619", "tarball": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.3.tgz", "fileCount": 6, "unpackedSize": 10630058, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB2h/Y39ZK7XUisd/qNRP3TUTcMWv6y6D3X0obNhTW5ZAiAqFGDtXK8YUcZO4lPO4MWfOcPjB5BinaVya8WVF0ZyaA=="}]}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "cpu": ["wasm32"]}}, "modified": "2025-07-10T07:57:40.389Z"}