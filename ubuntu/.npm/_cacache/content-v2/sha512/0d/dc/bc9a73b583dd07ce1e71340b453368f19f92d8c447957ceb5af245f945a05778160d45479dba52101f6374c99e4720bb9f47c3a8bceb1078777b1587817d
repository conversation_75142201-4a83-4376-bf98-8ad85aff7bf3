{"name": "@img/sharp-win32-ia32", "dist-tags": {"latest": "0.34.3", "next": "0.34.3-rc.1"}, "versions": {"0.33.0-alpha.9": {"name": "@img/sharp-win32-ia32", "version": "0.33.0-alpha.9", "dist": {"shasum": "d29785589112dce744aae19cb99c4eb6a07dbb0a", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.0-alpha.9.tgz", "fileCount": 9, "integrity": "sha512-mSlom1S7SvHUDEilKFQgZ0yIRUhfRGTUuZLX/LSs/XFysSn4w9T6huyG11GHBcbf7FYdOmB7XyAY3Q/bZZvV/Q==", "signatures": [{"sig": "MEUCIHGGxW+qkHO28K4H9w5J/wbUbeam5X1FXqnzCfO+TxY0AiEA/uP9FHjFx9+Cj4HJubn6F4oaUjsYevF/BOl9EnKDq8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20716526}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.0-alpha.10": {"name": "@img/sharp-win32-ia32", "version": "0.33.0-alpha.10", "dist": {"shasum": "032a1f4915ed9a029489e455abfb768f6f70b85b", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.0-alpha.10.tgz", "fileCount": 7, "integrity": "sha512-0L+gwkOTasx3dBsmhyU/znNpBnilcLOaKeBDeEu8kVUHqQUuDE/zMsn8+CU+YINn8SFuU2Nzr/+SGVbJVd7gCw==", "signatures": [{"sig": "MEUCIC/vqfaBcB3e3158U8w7KFTA0eYWj9EAL1f4+e0HRQU5AiEA1aJPbiiLFhfWz9MwinAahFPv/qjwCpBgozkwKB5k79M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18974706}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.0-alpha.11": {"name": "@img/sharp-win32-ia32", "version": "0.33.0-alpha.11", "dist": {"shasum": "356d31a80c04b568fb3ffac9a815bdc3a89ef75c", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.0-alpha.11.tgz", "fileCount": 7, "integrity": "sha512-qJsGJtMMfi3vGnXcdkaftoEwQkL9T3fqc+/+hZXIFegbdL48yWx3G8C417GjRdCA4MbpsCFbL2Oda4xvhlQCYg==", "signatures": [{"sig": "MEYCIQC3f5nih++fLATR9bunWw61GcqdknJAh+wjVzjaIG67IgIhAOE3590W30r0Hh6H6kYnfWOmUZaf+tptSQNT6u0p7tB4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18974706}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.0-rc.2": {"name": "@img/sharp-win32-ia32", "version": "0.33.0-rc.2", "dist": {"shasum": "8cadd13b112aa65664df91a7ecfd8ffd25af2a67", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.0-rc.2.tgz", "fileCount": 7, "integrity": "sha512-s5doPlHYiXoKjz7vBp+OLVWdHno82eUcqLpfoFqWxMUH/G+GgtzDdCFbX1u2crNetbkABzFeQuQTrbisVmXcyw==", "signatures": [{"sig": "MEYCIQDVp/jJCHUdtdva/YDX70fg07LeDpFzCuwpbOyQgBLbtQIhAMXA8sRZ60siJlSKfm5fL+65Ri7k+FSmCxbTkMICmwRu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980846}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.0", "dist": {"shasum": "a89bc502a70e5547cbff9451d06f9962425e7b39", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.0.tgz", "fileCount": 7, "integrity": "sha512-5HfcsCZi3l5nPRF2q3bllMVMDXBqEWI3Q8KQONfzl0TferFE5lnsIG0A1YrntMAGqvkzdW6y1Ci1A2uTvxhfzg==", "signatures": [{"sig": "MEYCIQCNrPbb1AcQ12u5nYnP2fBYaSZqltcBl4sqvXRSgMUaoAIhAJHwlNViGt0MEdeuzW5yghkq26Z678JFldaRN/ITA4UY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980841}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.1-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.1-rc.0", "dist": {"shasum": "cfd94fea8704ee5baa17f79da7b3b52375ab3e06", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.1-rc.0.tgz", "fileCount": 7, "integrity": "sha512-csVrBHYW+vvMxsu9uew429i7BvCnPryZ0SLHyGuDb2gpZMmoWuVZpl/ZwQplqaojlKrcfYoVNf9Jm9cRWER0QQ==", "signatures": [{"sig": "MEYCIQCMjFNFpCIbxRyR6RTSH+/IziaE96PmytuyVm3hB132sAIhAN0sxzKtYFZANYbR0o+4Bic86NZ9xgVxysuYVxFpBOlI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980846}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.1-rc.2": {"name": "@img/sharp-win32-ia32", "version": "0.33.1-rc.2", "dist": {"shasum": "e3fc8bfc37825a1adb69b58a48f16f402646447e", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.1-rc.2.tgz", "fileCount": 7, "integrity": "sha512-8Q/6XchmbHlUNKaJTWiEyTcbkZ7RwEuUGp7zYoBgejC+j5Q8LJA8jTBrw6qdg1vvGy1t3R8TOnb4LR2fHuUDDg==", "signatures": [{"sig": "MEUCIQCkNomj9ByQJcXS47lPbU8VcpAsfEpqGRprYbNRz3y+SAIgU2uCBOCAB1BTRXOV2jssUiuN8uMTsGaHj1332pWx79M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980846}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.1-rc.3": {"name": "@img/sharp-win32-ia32", "version": "0.33.1-rc.3", "dist": {"shasum": "f98296cb61e7fbba1d60007f077a5418a122b261", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.1-rc.3.tgz", "fileCount": 7, "integrity": "sha512-/D9BN/O6gkwHrlVL0ZgvdFLQhrs4MZe+OArBsj+GwBvLvRJFlD5R6pjcZ+dLbKghxdTK8UQ0AlmdZjtv5bHrCA==", "signatures": [{"sig": "MEUCIQDlCVGygDvWzcLObXzMv5yrXcELYW4asCBl3h6eFNMnZwIgPBadlNa3CVHHoH0qFVs2b/UQXSIBb27ylVaGkUAwA8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980846}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.1": {"name": "@img/sharp-win32-ia32", "version": "0.33.1", "dist": {"shasum": "8df522d79b3c08f59e65fbe773849daa353c4f12", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.1.tgz", "fileCount": 7, "integrity": "sha512-/xhYkylsKL05R+NXGJc9xr2Tuw6WIVl2lubFJaFYfW4/MQ4J+dgjIo/T4qjNRizrqs/szF/lC9a5+updmY9jaQ==", "signatures": [{"sig": "MEQCIHu9peBRLKi0WEbDl+rSSR1DZ954HBPxy6lmuFUyCjaDAiAn+b6CT/BFeyhcN0m2i9uyD9Op7B7ShY34jBAEtfqYvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18980841}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.2-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.2-rc.0", "dist": {"shasum": "f6e409c67beaa7280637e343a6df880f9eba5a81", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.2-rc.0.tgz", "fileCount": 7, "integrity": "sha512-tZXnUzYkY440Djk8CrRCK3j0EGNXnd0BlM8I2BwHyZTkG6GCrTa8lhIzLz2Tb3rVLvNngLiEjzEMVq/shyDxww==", "signatures": [{"sig": "MEYCIQCa3ArgOZaHspStaryp9p6U1+Sx47O2kg0G3S1r08BAfQIhAN5ZnfOb6TjWgj76xun0wNcsFdXmwTJKXEJRpi9DstSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19019758}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.2-rc.1": {"name": "@img/sharp-win32-ia32", "version": "0.33.2-rc.1", "dist": {"shasum": "4da90cf05f09d8281d89dca96696ee05a1c11125", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.2-rc.1.tgz", "fileCount": 7, "integrity": "sha512-rqaOTe88KNkZXrPiXSVU7HsjClzkg59U13FMxaX5IhM3uEHuLqYfSYf1B3+ZuRbLcABomR/ANwpAnhOVxwN03Q==", "signatures": [{"sig": "MEUCIQCEiinfQo6p3kX8Xvld5XtSxP7R8/6bxTFiCgunYKGHhwIgD0N8XC9G0SqbdjUyoQIDGE2DsdRp4HOWPevOxYvq2AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19019758}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.2": {"name": "@img/sharp-win32-ia32", "version": "0.33.2", "dist": {"shasum": "09456314e223f68e5417c283b45c399635c16202", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.2.tgz", "fileCount": 7, "integrity": "sha512-okBpql96hIGuZ4lN3+nsAjGeggxKm7hIRu9zyec0lnfB8E7Z6p95BuRZzDDXZOl2e8UmR4RhYt631i7mfmKU8g==", "signatures": [{"sig": "MEUCIEDMgSPEq+2K4ZJXVhch4r62E3y7nsDqoER4z0pwVOQCAiEAs4rkLE0BAUTrAR+YtU9D7rGIIpRZVpWKQgVPRPIHv8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19019753}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.3-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.3-rc.0", "dist": {"shasum": "284778b4ee1a865de13646222a167c8ff4746822", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.3-rc.0.tgz", "fileCount": 7, "integrity": "sha512-SIS+Bqg43WG0LGBzymapK/NiYitOCRDHtMoZmmVyj0R+AY5UtLPL2j2xF1qeRTYCF/npRADHcwayW+hzRbWznw==", "signatures": [{"sig": "MEQCIH2qYD/geSTHLiDRCT/ElsAv5rAWXFCX2Y5f5war7i0cAiBIiN0t+Wkff8dWTR+AlY5LG44WeO0KFvt7nr9RvSQ5zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19600879}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.3": {"name": "@img/sharp-win32-ia32", "version": "0.33.3", "dist": {"shasum": "0fdc49ab094ed0151ec8347afac7917aa5fc5145", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.3.tgz", "fileCount": 7, "integrity": "sha512-CyimAduT2whQD8ER4Ux7exKrtfoaUiVr7HG0zZvO0XTFn2idUWljjxv58GxNTkFb8/J9Ub9AqITGkJD6ZginxQ==", "signatures": [{"sig": "MEUCIQD6HCFSIYubqfetob7Utkdd1O1wjdAYBUcqAe9ubV2KJwIgNZ8LOovr0xBiCsU+k+zdPQeKGLENsBGnRlp4/tCjQcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19600874}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.4-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.4-rc.0", "dist": {"shasum": "660380d7b871ace274ca14f9c374b2985ee0f0c9", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.4-rc.0.tgz", "fileCount": 7, "integrity": "sha512-kgaLSZ0tYGXuskpJdXsaLIrYEKPmuRo02CCS49cvLsWP1Bu1h5GIgnmDjNnjiVmD6FwC4K/NTkPPVi5G8bhEQw==", "signatures": [{"sig": "MEQCIBTUfmPvUgFKEHWpCbfs6HoMoOmcrT0lwR84CQgDIU80AiAaVkzrR7FEqv2w2fu0Om1v+6AQhlQ14es74eIMyAUqSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19600879}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.4": {"name": "@img/sharp-win32-ia32", "version": "0.33.4", "dist": {"shasum": "b1c772dd2952e983980b1eb85808fa8129484d46", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.4.tgz", "fileCount": 7, "integrity": "sha512-99SJ91XzUhYHbx7uhK3+9Lf7+LjwMGQZMDlO/E/YVJ7Nc3lyDFZPGhjwiYdctoH2BOzW9+TnfqcaMKt0jHLdqw==", "signatures": [{"sig": "MEYCIQCLJOmKHCkl0dgMj3cMFxIVUdOJvu51FrFqvu5MbVvPugIhAJGokkoWfsSoRk+pxYZYbgfcmZXW9Kmm/qfZy7R66zu0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19600874}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.5-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.33.5-rc.0", "dist": {"shasum": "10c3e7d1155860d5cf280dbcb4298079c527bc44", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.5-rc.0.tgz", "fileCount": 7, "integrity": "sha512-4qcCCSytBwRcvxKIXC5HSzVC1arPKHa3p1Sk8qGP66m9yUXPKAn9M4uQ1SRSUFHqiRMrU1wpQvq07m1UJ2cBXg==", "signatures": [{"sig": "MEUCIQDft1KIxcTfpXnX/YoHilXB6KAc+9XfXCZc/mRvBAez8gIgX1hSHUtqZjJM0IYQi75RUep7U/ZYIMwukwvsvQm2zjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18936083}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.5-rc.1": {"name": "@img/sharp-win32-ia32", "version": "0.33.5-rc.1", "dist": {"shasum": "e38b45b8a44af5e7a55c50b9b1c6e22eccd83e85", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.5-rc.1.tgz", "fileCount": 7, "integrity": "sha512-UGxfDzqk/t4ldXmNUAqh3yO7OZLeTeSV8iO2v4oB/njV8ewvIZL6HeKQCg0CnNh86CeUmSArRyPCoWwBpSaaqw==", "signatures": [{"sig": "MEUCIQCuVyPoVRQisGC70TxaGT30aB2uT9w7f6XxutXI1UWjEwIgQx54DXAGcX1qWk5GPTPXjp5F3H/rc/jr/uKHeZs1/QM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18936083}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.33.5": {"name": "@img/sharp-win32-ia32", "version": "0.33.5", "dist": {"shasum": "1a0c839a40c5351e9885628c85f2e5dfd02b52a9", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.5.tgz", "fileCount": 7, "integrity": "sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==", "signatures": [{"sig": "MEUCIQCWKVH7MEP2hUfnpyBIw08uCyZauujneDdlHYgszSP/EAIgbOLIUthpX8vgIFagqeI8OGmU7s+wu6L9jj1lhFhitEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18936078}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.0-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.34.0-rc.0", "dist": {"shasum": "6df09824df32faff995cb3a46751e15db8676765", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.0-rc.0.tgz", "fileCount": 7, "integrity": "sha512-HvKmPPepuoJb7bzv8aRR13/WVN2YPhcgdb6R9w5z8i08Eijglb5gzB+z4/ajRvPjC6GCn121/XLtM59b5HzDQQ==", "signatures": [{"sig": "MEUCIAT14pfd9eq6ku6bIau74tlZw0VB+YLl5REYwcjItOtYAiEA2w2Z5BCgwGVIWiN6yipn5lo+RfmsEWdisYk1Y7ugbIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18884373}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.0-rc.1": {"name": "@img/sharp-win32-ia32", "version": "0.34.0-rc.1", "dist": {"shasum": "c0754c1f21de7e64e7d581a78ff94e4c43ce9953", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.0-rc.1.tgz", "fileCount": 7, "integrity": "sha512-7+vtiwfmQFVXDNkcneq2/WRLOCaqlms1pZJOWM6IPXHdUpKc0udhK2YhNAU4yeaT8q+ivqz1t2xCJb1hWYEIZg==", "signatures": [{"sig": "MEYCIQD86dyJzRhe8owecSpP9dmjC56TLQuPiEsl6GIL1yyF0AIhAKA89TfjU1Ku0NcLTZUyKlcMca+Dv3dRkZTCI28e5sZ6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18885909}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.0": {"name": "@img/sharp-win32-ia32", "version": "0.34.0", "dist": {"shasum": "f6858ed2772b9719e04294347bf01777ee35be0a", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.0.tgz", "fileCount": 7, "integrity": "sha512-ofcDYsjJJ1zya9s/GCnXjbFIhTw5/gRVr+SivAGPMXmAml/rLLyDu/HtWntvhiacnL4VYvtgMFw/B2Zz/kgoWQ==", "signatures": [{"sig": "MEQCIC735hkDaoiRZSISeSuM3rbE5iwwifl9RiteAwWlKvVBAiBS6e3hiE2P5b7Lny6nZJaFjB+1PKT1G5TMq1v6G6964w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18865424}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.1": {"name": "@img/sharp-win32-ia32", "version": "0.34.1", "dist": {"shasum": "4bc293705df76a5f0a02df66ca3dc12e88f61332", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.1.tgz", "fileCount": 7, "integrity": "sha512-WKf/NAZITnonBf3U1LfdjoMgNO5JYRSlhovhRhMxXVdvWYveM4kM3L8m35onYIdh75cOMCo1BexgVQcCDzyoWw==", "signatures": [{"sig": "MEUCIBmpWPJphJV/vOUKjuZwMjqS875pSZCeMkWnCjHvzvs2AiEArPhtR1gjG19bv9mlQmh1yMjuSNyBvi0cfvCCVcysTNY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18825488}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.2-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.34.2-rc.0", "dist": {"shasum": "b1a31c93f8af024b8fd06333a68816343072e571", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.2-rc.0.tgz", "fileCount": 7, "integrity": "sha512-qKHx1TjCLnrou4X2/bDImifgACa4xxvbmRJsYvyN4XTx56q3RwB6bBii45s+EPRkGrZnagehen/EL38vFPwuqA==", "signatures": [{"sig": "MEUCIDYspT16glvdze5nYZoYQZ0ZDqsBoSYMB1GdsPuRfQ1EAiEA1zNdABEaysO0haXeFDqoVc7ak5uQlrL+Lyk8heJH+vM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18825493}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.2": {"name": "@img/sharp-win32-ia32", "version": "0.34.2", "dist": {"shasum": "8fc30b6655bc6ff8910344a2020d334aa6361672", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.2.tgz", "fileCount": 7, "integrity": "sha512-QLjGGvAbj0X/FXl8n1WbtQ6iVBpWU7JO94u/P2M4a8CFYsvQi4GW2mRy/JqkRx0qpBzaOdKJKw8uc930EX2AHw==", "signatures": [{"sig": "MEUCIQCP03gnGtDOIfgH5i5a4zQa3iewx/k+v4BFjLvpdbROtgIgDy6QnGsgqHTRMcIUS/SvYCdSiCQZ8mFawcK/ZgVT8Ww=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18826512}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.3-rc.0": {"name": "@img/sharp-win32-ia32", "version": "0.34.3-rc.0", "dist": {"shasum": "e2a583dc854453c4dfadac14e89b8b44ea6abcba", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.3-rc.0.tgz", "fileCount": 7, "integrity": "sha512-0z4MusI7EaRPoMRG2eHjiCuRN+XBQ8LDy6d2vTj1izL/RUNcFkAZfF+slYtCzHWkDU0m6BuOJ8LjF8rRSSGDCw==", "signatures": [{"sig": "MEQCICTZMIe+XbloJOBft6ApUKGpvtnISm7D1FPWFGn5nZjIAiAKBISMOQ84kqNsksm+Y29sWAThharsZFHN2E5/TFevFA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18886138}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.3-rc.1": {"name": "@img/sharp-win32-ia32", "version": "0.34.3-rc.1", "dist": {"shasum": "615598b618030759c6c63cd5c7cb6213107712fe", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.3-rc.1.tgz", "fileCount": 7, "integrity": "sha512-iukJLNeV5fVmIUu+v1HJN7tXaWtVA36OQb/aqauarAkDZaRSbLSIcOB+VxD7h+HzijzejKcERpgij3sMrMa8Rg==", "signatures": [{"sig": "MEUCIA21IkTZgDR+chYCmg4CLomhD/13zzfTPUGPR52BvDe5AiEA9GRvFREssVyormo7hqnejZ4hLU1IBsL5iUlJBSZq/IU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 19101179}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}, "0.34.3": {"name": "@img/sharp-win32-ia32", "version": "0.34.3", "dist": {"integrity": "sha512-xuCdhH44WxuXgOM714hn4amodJMZl3OEvf0GVTm0BEyMeA2to+8HEdRPShH0SLYptJY1uBw+SCFP9WVQi1Q/cw==", "shasum": "9d4c105e8d5074a351a81a0b6d056e0af913bf76", "tarball": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.3.tgz", "fileCount": 7, "unpackedSize": 19101174, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCY5fXgTFgsM2Nd0KiHxzw2lUKgaUub8Pil2Vc0ncy8ggIhALXEfInYnivRrdb8Vd3x8Kc9Rxfd2ygEw/L8Ep8kee5z"}]}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["win32"], "cpu": ["ia32"]}}, "modified": "2025-07-10T07:57:51.508Z"}