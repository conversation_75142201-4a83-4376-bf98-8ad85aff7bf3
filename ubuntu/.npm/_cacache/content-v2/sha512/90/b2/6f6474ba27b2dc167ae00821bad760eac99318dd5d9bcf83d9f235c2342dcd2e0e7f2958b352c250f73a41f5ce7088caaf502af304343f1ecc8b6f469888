{"name": "eslint-import-resolver-typescript", "dist-tags": {"next": "1.1.1", "beta": "2.6.0-beta", "release-v3": "3.10.1", "latest": "4.4.4"}, "versions": {"1.0.0": {"name": "eslint-import-resolver-typescript", "version": "1.0.0", "dependencies": {"resolve": "^1.4.0"}, "dist": {"shasum": "16eca8fa5068de789948adbc527eb21e34d08182", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.0.0.tgz", "integrity": "sha512-dqUeMdpEoVS5SN1/pJa9P8d/zDecxUq88rV+KwtcZpqS6UA09U6/4Qh3heZLWjAPpRBa3nIcfMiBEZa0Ca5fzA==", "signatures": [{"sig": "MEUCIQDFM+BD01b2FCVkvnW9l7UFHyuHJtzxD5yMLVwca4o82wIgLUVvtXiBqjFxbCl1YPHK0z03C1uEzHBPLBDpQVikuNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "eslint-import-resolver-typescript", "version": "1.0.1", "dependencies": {"resolve": "^1.4.0"}, "dist": {"shasum": "60b90b5fb92ca7940f222da1b7c1373621b93a5c", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.0.1.tgz", "integrity": "sha512-h6veubMIjMAm5RdzNAUIfNvHEjgKIp5Uwnel/3NFrPq9dimCVg2c2Ii2b8HdK9gvU3TUePW7FoRtFLCyE6dvMw==", "signatures": [{"sig": "MEUCIFI6PS2Xi039V5hfDW/LwrgMAaAYmYFTt+dV66Q10KfcAiEA/r9Wc24RRiacXC3yar2Vz75A5RQqiBw2h9lcSJw4cVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "eslint-import-resolver-typescript", "version": "1.0.2", "dependencies": {"resolve": "^1.4.0"}, "dist": {"shasum": "d910270459668c92c4673d0cf74c08d763ad5c98", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.0.2.tgz", "integrity": "sha512-cO0H244gGEdekOhWmF2hq9nsqovGzSDwAMjfpSJf4u33SmuFNmMhU/+N6A7y9dsIgV17ETzXjjRne9sec1CWxA==", "signatures": [{"sig": "MEUCIE1FiDwwwPJD9c9WP1/nrrot/SR0FS8ylIB81qgJuoXaAiEAokjcpZCJ+g1qieOWBNHMtJYruTmTOVuG2ZVHR3voLCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0-rc.0": {"name": "eslint-import-resolver-typescript", "version": "1.1.0-rc.0", "dependencies": {"debug": "^4.0.1", "resolve": "^1.4.0", "tsconfig-paths": "^3.6.0"}, "devDependencies": {"eslint": "^5.6.1", "typescript": "^3.1.1", "eslint-plugin-import": "^2.14.0"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "ea283b0f52dac710520eb146759e426efd5018bf", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.1.0-rc.0.tgz", "fileCount": 3, "integrity": "sha512-/TZy0QFR7IcUFWGi8C6KjdWQ4w3TwnmrIiwzDC64X2yIKFDd7a4RlCyUqt/tpS19KcX4/Hv920sx5YvYbpvAqA==", "signatures": [{"sig": "MEQCIGePsjGDZCStA9km9OQY+9V1CL97yjnf44k8DkaimzETAiBmCT4Maa6TZTeq0lhAAQFr68cDg8PafGwA9m+42e1Esg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3849, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu4RKCRA9TVsSAnZWagAAhA0P/i1cEWwwWgcbedDq/Y4k\nPk8WLwT5UPzMWVwcOVMsXFUisadM496Ff39WACYLwIsPz5MpUwId2tyTmdAH\nDwcD8KGaYGdOAd5yINtfJb8y5cCpu+jt6nJKuBzWlNadDMK9AfqzSSVO141k\n7WqZy7VAhaJ3fn1LZmUKFbn/MkLIoQaYuCGzbJ4Wk7IaatDGhj/O4yF06Vwk\nT2ziZ9WXxQN5+pyopvIwgkUmvv4loxMTWxiWtskJqBxjT+dg5IWOJvYc5kPk\nOgczg1I9fpkCvSWHEKYJHXZ0YVP34wU03Kxk8vNRw71XFgTrrlDVVl8s29wQ\n9+Fe9Dwia5z8pJXfIKpAjnJReBDxDZui4Oi6PdNjdhp7zDdTrF5XJ4iTvPia\nn0yEnJYsRiepVKWX3f4ELeLBrhmBSm4geaOlUoTiH/+hJkTnsZuAGTiPWIoa\nEUFNFtnUtwR1VPC5IT2YzuvcOLSNUYmw+fiTrGErNq2lCdgaGuThXqdmj8j9\nzliYyuFoVlzjYveBHLwX2SYDx9XaQjYx9hzIOhjUDZJn7B4vFzHnfA2j4ZEl\n+SfrKP6bTN7SQHHA8MpZot9S7qvtwIQ4b9G+zZMp7CFtjY9OV080AbCVyXey\nbJXggH33VwjLoFvyps2j8oMcLosKAGAMeCe7WYzCM/FG3kpvQLfUjelP2vUh\n6bsw\r\n=Hn43\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "eslint-import-resolver-typescript", "version": "1.1.0", "dependencies": {"debug": "^4.0.1", "resolve": "^1.4.0", "tsconfig-paths": "^3.6.0"}, "devDependencies": {"eslint": "^5.6.1", "typescript": "^3.1.1", "eslint-plugin-import": "^2.14.0"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "bf403d55d1af3cca72ba5ba7de3f74107b121cf1", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.1.0.tgz", "fileCount": 3, "integrity": "sha512-nke1obF5sp1xdGH7n5t+mEWIYfZ4FKmDK6I+u2iKFomzs7Si3Scq075VsnyUqhcMHx/Zn0XkJEMPR3Y9to06dg==", "signatures": [{"sig": "MEQCIHfYHKJrnyatISCT94NzZnwNohbGfnceTj+Z/ehJPyT0AiAOuNEskBWq6UyPD8UEbyTe/GiEGT9Q2P739VYwzDsBZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu4XYCRA9TVsSAnZWagAAif8QAIqMgyz0TGVmfgWQtkfh\nLVI3b9BQpxQrhrrOWCAuqECicuqEVHAEVDkVQ9a6Z1BDWHS3Aos8Tyqip0bV\nIJ3lMoCkVwq7Q6t7JI89RlpGlaLGLqcIH5/PIlsQuIXWyPNGSY6IJW+7xP9O\nMLUs9H/bTSwWKHiH6ILeNo2ZWITzVgQZBhF9PJxt8tJhyrOMQ7Dm7OjR5uMe\ngRz1eZiTFkx6SnSJ+5nyKAURvffFPjSrTcgxE2udP/Gw8mRaZc0q+g40ojlK\nnPTXWQHo6J+bViy6lYoEfDhiqXI/Y1CPRuv8o/qJMQbKIY9xJHcaiJD4Xfck\nepgArnPJDOKxGFGCAZTP6YfHLa26bDjKKnyGWwFwUUHsWhHM9XIY1LoUbwu7\nuVjICZwUCw+NiNrt9/0WTSb/xi6xZMV3zSnP8dRfjcJ56/ef8PxRR7w3Fu0e\nDUiTA585AEKwDdpl93sN0np1u8SNi0MMEg/eUEF+IZ0yU0RzqcdD93Yoxs0X\n48xWfncDd6MpaFY64EGN+xTat4tq/+NXEV9Nv/wt3iDcPlYNFA9kN5lh7DeP\n/wlgHO9B83bfsf5HTzmmuPcEnL91ipWSmnKxnCcybvBxf19wZtfm7bszwo/k\nnYhtWvD99TMRICppeiW69TbxcoOiw8wqA6vdKmuoDSzGoSSjY9PzILhz3iTq\n1W0R\r\n=5tSY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "eslint-import-resolver-typescript", "version": "1.1.1", "dependencies": {"debug": "^4.0.1", "resolve": "^1.4.0", "tsconfig-paths": "^3.6.0"}, "devDependencies": {"eslint": "^5.6.1", "dummy.js": "file:dummy.js", "typescript": "^3.1.1", "eslint-plugin-import": "^2.14.0"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "e6d42172b95144ef16610fe104ef38340edea591", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-1.1.1.tgz", "fileCount": 3, "integrity": "sha512-jqSfumQ+H5y3FUJ6NjRkbOQSUOlbBucGTN3ELymOtcDBbPjVdm/luvJuCfCaIXGh8sEF26ma1qVdtDgl9ndhUg==", "signatures": [{"sig": "MEUCIQDcxUe3InwsMVe285gDz3cvsrVYTPSuvzHaB/aEY0UluAIgChgiwjlyby/5zMJ1wValzpih18y7GDj1qs9GBQv4KFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0zroCRA9TVsSAnZWagAAV9oP/jVLucBc1zwwWfvudJor\nl1Y0MTbPW9gYO/KbVatE9M/yh2TgmsLdiAUZVxHufZTsHzpNuFGmWSMs56IL\n4kD5NBASdDy1NgfmRl5An1vJ63n8Nrc1t2SAdFw/eQHBC9Er7QY7XHJclEN1\n5uo/KFoPmW+jLROeSTGMhfOrA2hFlEudizm3lHL0wnn//rNH/qKB/frGcAOQ\n79CCsIKCRrUvaVzo2+xzplh93zcxmKh6h5uDfbuq/6LP5G30IHmJV9XrnuRZ\nEXcHoNz/HGaiOXBIiKAAyN+enxLKi+R4LGkPKT6GFH0MKDG1OsjwyebnklTM\n16ygC9dMSeYsZz2CjseV6Mk2OzkANoqgl4r/9i8ExXsLhZXIl3IXhUaTyfYh\nPf9P/12vpDfUcspJVWbKW3Pqjifrzf90F2ia/gQ4uoNM4/XoO7RsDqpWKsbS\nKc4JmePNniFCVoLaCGGW+OpaWimvzOu6wF3A/0C6aEsbpFY/guggqIyxCWL5\n1MMvF3YomY9VK931zYf6Cx232n42EPC1fMtIBdDF1kDsNEz4fL9m9OZZfau3\n0jCaZAtx6lxTQYr/FQCiQWtrRl3XhURGbc1KFoIxWtZG5E6Z4DCMqk+RP77T\nKOqJunJRmQAL6QTctzyKZSaTS1tYkSz0nmqxVUoDydB7UrG6Yy39+H4LOVH2\nt3D+\r\n=0irr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "eslint-import-resolver-typescript", "version": "2.0.0", "dependencies": {"debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.12.0", "tiny-glob": "^0.2.6", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"husky": "^3.0.8", "react": "^16.10.2", "eslint": "^6.5.1", "rollup": "^1.23.1", "dummy.js": "link:dummy.js", "prettier": "^1.18.2", "typescript": "^3.6.3", "@babel/core": "^7.6.2", "@types/node": "^12.7.11", "lint-staged": "^9.4.1", "npm-run-all": "^4.1.5", "@pkgr/rollup": "^0.2.4", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.3.0", "@1stg/tsconfig": "^0.6.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^0.0.8", "@commitlint/cli": "^8.2.0", "@1stg/lint-staged": "^0.8.6", "@1stg/babel-preset": "^0.7.4", "@1stg/husky-config": "^0.3.1", "@1stg/eslint-config": "^0.12.20", "@1stg/remark-config": "^0.2.2", "@1stg/prettier-config": "^0.4.2", "@1stg/commitlint-config": "^0.1.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "e95f126cc12d3018b9cc11692b4dbfd3e17d3ea6", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-bT5Frpl8UWoHBtY25vKUOMoVIMlJQOMefHLyQ4Tz3MQpIZ2N6yYKEEIHMo38bszBNUuMBW6M3+5JNYxeiGFH4w==", "signatures": [{"sig": "MEQCIBmLML1PnU6Vz7z0I5f2XPm2c4PFWe871PTEhVT5COeIAiAPaSCmUqM151iG4iabtAbncdeooshVGXhHNIMfbFapVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnMo6CRA9TVsSAnZWagAAcIAP/2v5y77WSMsIyLjRiNlT\nGBHFE6sR0UUWTQIgqwsuHO9cNbPdqDdOoCRHtyYwncKKVMgTMKtocmdlaUli\noB42LmeP2Zgfu7pHHoZQ2LszhIfXlWpcUxdl4XkztD+0NUG+Lg70W6Zz1Wka\nszqpDO+qE6FCjD0zpxNJmKBj/uX8uU0sXhIaLA9aYijxLhe+5oMOa4AOxrvp\nnZxBz2e/jKmB00hQLNIRCyeF1gp4/kTZiY0PQUa440xepwFySo+qWHUvtOPG\nABtu2xceBxbV+xxzKKEs/GUmPIBahmnno7M0G+RrB6D5KZyK7whqR1t5mlSe\nKZMqnWO6u9FEXkAdp+qFWezkVULHeKq5iZw9O2o9KuvS3D0iVrAslaS1H1fy\nncwYyZMXUmnUT4KDRbWY8k+8Zr/AydCMa39Xpp+2AMq8edOc+LtqNLBWJMrd\nMCpUq9Sa67iufoXXyoY16wADJgN5HGB7HWLzL0QFrsICB3ZDJA32ThW+a6/k\nuln8BVWrs7k35Lby3jsZPjHiD4xUrMTji4o7+xHzavl3djkoXEO86l4p5GW8\n0eb3BZ6R/iegJ5tnLT3Vf4ndrX4UFB0hud165CyLqzO9lnQ/8Yw/KQLN8nf8\nDQrYCneMLxSfNlSPYXXHuCcdhyCExKxmEKuTmyEHeG6/Fcwp6vGv7iet6+H4\nauyb\r\n=mBtP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.1.0": {"name": "eslint-import-resolver-typescript", "version": "2.1.0", "dependencies": {"glob": "^7.1.6", "debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^16.13.1", "dummy.js": "link:dummy.js", "typescript": "^3.9.7", "@types/glob": "^7.1.3", "@types/node": "^14.0.27", "npm-run-all": "^4.1.5", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.9.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.17.1", "@1stg/lib-config": "^0.5.5", "standard-version": "^8.0.2", "yarn-deduplicate": "^2.1.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "dc19a289fd7ddb0a38dcdab2a965630d1fb88aa7", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-sWpQYfW9zL2T11kEVRY8extXS6xAw+Fzh8qouwxDxDKhv2IOYpf6jXNJ+vqudptRqO9QwXoISLoMvb9YoVWELA==", "signatures": [{"sig": "MEQCID/BIllculPF3+OVZ3minwqju0X5f8y/u7v3Jt6LOREjAiBYKGh2mVPz2y2g5TGAr90nSGd+SFb9cgcYfjEsRWL25Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIjrUCRA9TVsSAnZWagAA+pgP/RS6TeSlf2PPnKkz7irg\nDgPF7b4tNgQDAU3rAjASd/Gq8ZsGNAplbB4hBCIVIA6ByG+JplNkK2LkLVbu\nfOEh+CwOujecLmivSR4+jGr5YA/9WNU3O8g4d6cHfYIzgko2UNIkR75Y0pVB\nKkon3L3mFP50HZIO5G7DD2zZ7LxfZcq8E5kIGNofSeCmud5rqFbS768yl1vb\nWUP930pQun/yUkZszvX0iLl53hFGAZI9y8Nb75fjcIVRC7ZGtebmJoLSHt9Q\nKjhrgb5gRjBblNTtYgW/6plk4yFiknuizsysdfASXcmtHHKzJSVpP12D+4Xq\ntz8VOjmOu8jdz8rvEpaMrKhLZbAdY6of1ntMKmGm5DQr+sRne+3ikD2lluVj\n21N+az6DKDmYVY0J4ru4bxZ8go1M8Bvs4xcXANfvTNUQG4SOd58pPVAgk8k6\nYvHD/mdiyHWh0W8P6vwUER0MfpmhUN7gzxudIfToKQiA0fVHR/p+JZplpSyY\nxr4M96r9Y+Dwhyf9SG/FxAa2q55Ub1UmJ0/c9sVqAfbX2Uc7EWaTeIbw5CTg\n/kPGIWV+mENaCri+arW70Psq8JnQlh7dK2lyQX17y1TPIB5Rp1Gc0/oykZiP\nIfzOXJZWkhmyowz1MzPC4rcqH515e/U7ujBUGjNTwGvCf2QrzdwmzLIykPKN\njKan\r\n=LTa+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "2.2.0": {"name": "eslint-import-resolver-typescript", "version": "2.2.0", "dependencies": {"glob": "^7.1.6", "debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^16.13.1", "dummy.js": "link:dummy.js", "typescript": "^3.9.7", "@types/glob": "^7.1.3", "@types/node": "^14.0.27", "npm-run-all": "^4.1.5", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.9.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.17.1", "@1stg/lib-config": "^0.5.5", "standard-version": "^8.0.2", "yarn-deduplicate": "^2.1.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "f7b261e41a38b690f3044660f6d496cc312323b0", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.2.0.tgz", "fileCount": 11, "integrity": "sha512-/NhKEH1gbRlcb9RcaZJe5zRn5eIffGTf1qh3JAyvkEuPli3DEa5HQWWUO5OTfUjj7buUXsDq8lEsdwbbSeqywg==", "signatures": [{"sig": "MEUCIQDmOJDgu5zw727ZLv8KQVzHkxVgbiVVGhCVxd8PaunJ6wIgBdv6yQ+3TOtgVUkJzaEzj3uXdyA5VwsdCW9P/o2VBCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIlpuCRA9TVsSAnZWagAAHywQAITgC6134xfhblpHSQtj\nuJ1N5lYJIFIZUcRObt9W4DmT2QQHIlghr5sntxKpbhwnTrbhu9/li0dZakwd\njCT5daYu/xt9aTNByfPf2WbOnrkTT8SqD94corjFolCm4v8NQxeaXX9dN0fD\n+rIWYtTb7OZDln8cHbMat88qODPWq4Wq25TFSv0n6fXGwR2IR69EqLvO0kEE\nHp2KBbSek08tBB6/4s+rpU+WTkwpDEt5BCfarZY6EqNr9ZoeThL8sRbDYrMe\nrmZ3ZK4TVbPtZwQPtWyR8qmjqJmiHOGx9XVzzr3yiHb/2mEX2sfHblFgmzwL\nH+h5wRIsnu4pBHXSbDPCEaWa5J38pG/3b/pLSG+SR7iorkx+aTOVHZ0pyfV1\nxjaui4W6ymlzYvoz44uh+jUL8t1RmLxCfQUsllXcYTW5Q+IgzNuahSTprl9z\n3/t85jrjLsqjEk9klR7TXoeOsfrMA97PvBrbmh78S5vpuYniVv/ejOvR8Qk8\n2z23Hi/BqAVAFfJKYnEviMqLVC/apOE80ttIhjGEwC29HliUQ2r7t/RNszdD\nSBrMk8ifDwodUbSYvOQVyMS/ztO8ze4KQeuWR+eubhTfuPNTaPWMjo2Zr7eB\nBcMqRsF6Q5wFxqzk5aCTDi7FizSz5cg0WbOotqxR6BYxnZ0GfGZoKHCAwzs2\nUWKA\r\n=RVoX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "2.2.1": {"name": "eslint-import-resolver-typescript", "version": "2.2.1", "dependencies": {"glob": "^7.1.6", "debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^16.13.1", "dummy.js": "link:dummy.js", "typescript": "^3.9.7", "@types/glob": "^7.1.3", "@types/node": "^14.0.27", "npm-run-all": "^4.1.5", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.9.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.17.1", "@1stg/lib-config": "^0.5.5", "standard-version": "^8.0.2", "yarn-deduplicate": "^2.1.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "4b9bdfb51af7e14947ebc40ffdd6c32d5924b905", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.2.1.tgz", "fileCount": 11, "integrity": "sha512-wxlVdwuWY6R5+CoesIy6n8EZX4k9lEeZGWTVBoX9g//8Xma8JMtL/p3AGnG43rRyXmIrX+/0IN8lpOPzrw1fSw==", "signatures": [{"sig": "MEUCIQCOTRQQHFbO1EQWuPHlxYA3o5DuhOq/pNqcvhm2RoabAwIgaCEgTmI4081YjvWP+xBvIIYgx9PX5vuHSvjoHIPXfLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNqeCCRA9TVsSAnZWagAAXhgQAJLeO6SGxcmPcvJiqyO4\nPr4/C7hegxGpXJ/vXOpRrj9sZNUWTF9vnbeUa3OvfH4N0AsbFu0LdQCoI9HP\nNvPcwln5664s1tGOGC1qGuKHrq2DeL82/nYGnYit1mE6/uOy2qCcuE9bPKCo\njsNdeb5gPRwU6lYuCwJJR6IeSHMsbAbkKa4DvhCE/01cnOIpt27pWEtKW/r6\nKLgYKbel/WV93M3pLCSJ5nslWBy0piPMoRXOdqRHZwLsU+ASqdQVEfkpmtLD\ncraeft9WM4V5ek7h1HGItNbyut2GcSOWCbpIvLp+/ddYBXoSIuDwDV0C752O\ndcb44ncTKZXVNVF2suIWhYV+aFULoWsB1nPoxp62rnZiimyn3YCgcg21fv7l\noaaDU0bILrnMfiru02LS2ImCQVdruStMR3ENn+VFml4vQQSta5+4jJPCnZSs\nuzL9/g+RKQWPzfwITh8GArKvNfiepq0bAIxl5PYOaovNki99fkSLi/kBcWzd\ndaLJiSY6Er1eEtfUPqXVoOcADk0HiC3ZJeTlpvlHUsnB4P/Cvs9cC4swz9og\nCWyvwc5MEdMlJextkCjQSZNOB5XU+BmoDuKHR0ZnRBRNgRQzGvY7DXid5ez4\nNpl65MU+O6RAwZ0VRaDfF0nhuuO7vBrGWfA0uJLhBBSKXFTujrq4EFYc5T0o\nU47O\r\n=U3Ml\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.3.0": {"name": "eslint-import-resolver-typescript", "version": "2.3.0", "dependencies": {"glob": "^7.1.6", "debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^16.13.1", "dummy.js": "link:dummy.js", "typescript": "^4.0.2", "@types/glob": "^7.1.3", "@types/node": "^14.6.2", "npm-run-all": "^4.1.5", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.9.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.17.1", "@1stg/lib-config": "^0.5.5", "standard-version": "^8.0.2", "yarn-deduplicate": "^3.0.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "0870988098bc6c6419c87705e6b42bee89425445", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.3.0.tgz", "fileCount": 11, "integrity": "sha512-MHSXvmj5e0SGOOBhBbt7C+fWj1bJbtSYFAD85Xeg8nvUtuooTod2HQb8bfhE9f5QyyNxEfgzqOYFCvmdDIcCuw==", "signatures": [{"sig": "MEUCICk1JNeeEprDTp4rTAcPdLl7h4Z6HVAFzE9zPodF7BXDAiEAgB0FvBmqsbNOzQteHM/7jBwCpXWhZdUbwUMmVhiD2/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTj4OCRA9TVsSAnZWagAAZ3sQAKAuWhQi1Z3q6UiltbY5\nFI52ZMs+1ThadJxU56/04YlaTBQ/cqJ6tewbV8qWoYjlTssR6IcgoClnrBTa\nKlPyrCpGZ5IDIzt068DlREkazpaJ1Rido8TNCRZjNFvtFPne9dQtFzSjCUPG\nvysBZQBu20/ChvTQXuMOfMVB+d2CK2pV45pN6/dgkUoSsuHdnRQZqitbQsQA\nbFOKsuKWUK/ghq865+XGvlUMIYdmxaPYYOPafDHgXFTZhUy3pj4RpB7xhn+/\ntTr1DdW9tqLWLFiR1JdWQwW+bSdQaShW1ZN58aNT91vPvVnbw7cuZYe2AzFQ\nDJT3B33xXTwbeIQpzDhUuCX2a67K31ad1cjOI+5gx5CrCIvRetzJ+TlVSJeT\nmlai5wCDBNPEwXaMPfaD3VTOYMlTkgamBOoHrQhHFRChJl/dMaJmoScNiEKX\nFDtVLMRduB6puLE3+C9AcmbAU7HHQuzsPFkZU67u8JdntAm+OJ1OwTGg+H40\nZWLNM1mAtu12lNMWeoDkogJtyZOdTqy0iFGnXq1dNHEkFsvpfZEj5OK0mJOa\nLjFl462MpB8tZO7uXPzbK8/Ebf10pWRnIh6x8jY8Mw7LJK5Aoc7o/aUqbI+X\nOgbNUTJc0p9MC8fKQbhKM6ko0wlOj9C0ui4LG9cQPTLleRRo1qCR5Ctlmkk2\nnUuD\r\n=PGL7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.4.0": {"name": "eslint-import-resolver-typescript", "version": "2.4.0", "dependencies": {"glob": "^7.1.6", "debug": "^4.1.1", "is-glob": "^4.0.1", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^16.13.1", "dummy.js": "link:dummy.js", "typescript": "^4.0.2", "@types/glob": "^7.1.3", "@types/node": "^14.6.2", "npm-run-all": "^4.1.5", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.9.0", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.17.1", "@1stg/lib-config": "^0.5.5", "standard-version": "^8.0.2", "yarn-deduplicate": "^3.0.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "ec1e7063ebe807f0362a7320543aaed6fe1100e1", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.4.0.tgz", "fileCount": 9, "integrity": "sha512-useJKURidCcldRLCNKWemr1fFQL1SzB3G4a0li6lFGvlc5xGe1hY343bvG07cbpCzPuM/lK19FIJB3XGFSkplA==", "signatures": [{"sig": "MEUCIHAYflPbYDJ+Nu7u47JpWgVtQW4tZIKa903tgsluUMYQAiEA2fdeyKaYDvdfKSoMV+9JInDeBqZNoQ6qTCx8iUpXdBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgK1HMCRA9TVsSAnZWagAAQTQQAJpswiblHcAznfuYHvm8\nDln5QOrfhmZdYpat6+YpUTu0AkYDWiwaR53P/Uxgfe3HURCi2G9V9rdBJWM2\ntHEO8p4T9u5gGY4p1rWYWqaxAxqVPA9fOfsRyL7i218KIFBFKP5PqRv0cR5H\nr3hN7s3NVT97u+yz8CIuD2gKlQ7yiWPaqFJZNL5LmPX7vvG7Hyq1jdI8bUwP\nxVfroW68pgr6YLf629XUO4MC2j7G78cKjCelgYSnnNkbBtjDqUbTa+nRBrln\nR4yGPBl/7Y754/ghjolyx0NPfRO5j/cq/q7WPuWahktGV9w1xk9PYlbHxZvM\nmNtGtlEBVeX/ILb9CvPipMVVET381QLTNGsAORzjfRRbf4ZOQWScjfQJS6Cv\n7lGXi3WzyHjKxuf5tiB0a+Y+QDFltAtA9MB4og8EVRMlVvl02ocp7oGYGzjb\nqgMWhU9ChLDmCcd4CDUWr9Qmp1PFbDFJjTQ7+ARCg659awGIU5aOjeSxtvaO\nw6BgYoHP8Eh9RmRdVOKEqMlCK3O/yvjFWBpU3mEOoxPjFvA778RWfFBnubuv\n475jpDuvNaDYf5zERO/nRtnh/QehGG4oOhaCHFgdeslqPqOmoublGYP57+HA\nx9gQQ4Dl3zNaALrWMQyyTUZMtcMaHgEhr3qOtFki0tlD4Mj6JNL/UJP+eyv3\nuhqW\r\n=qeU4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.5.0": {"name": "eslint-import-resolver-typescript", "version": "2.5.0", "dependencies": {"glob": "^7.1.7", "debug": "^4.3.1", "is-glob": "^4.0.1", "resolve": "^1.20.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^17.0.2", "dummy.js": "link:dummy.js", "typescript": "^4.3.2", "@types/glob": "^7.1.3", "@types/node": "^15.6.1", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.17.5", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.20.0", "@1stg/lib-config": "^2.1.0", "standard-version": "^9.3.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "07661966b272d14ba97f597b51e1a588f9722f0a", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-qZ6e5CFr+I7K4VVhQu3M/9xGv9/YmwsEXrsm3nimw8vWaVHRDrQRp26BgCypTxBp3vUp4o5aVEJRiy0F2DFddQ==", "signatures": [{"sig": "MEYCIQDKB+/mvRFtif6nEhuqaAnTs7tbFW5cmfPSZS5oMdL4agIhAOnXWcJdFV/qplazRgNlw7+EDQMkMTlgt8TBjQbozfc3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPyIHCRA9TVsSAnZWagAAQl8P/jQenf73gNV7w4ZiOcy6\nyLqFFQAn2AX4UGw1VZu8fLA9xMKc/jPCjxBvv1YmPiNb2sZhACmWMEwOr3EP\nSCY+Oin8DTJsF3I+p7/HHqBJ4GCO908gIHPrDeskteaGOR7GUifiDr51G3bp\nHg6HGGiR+x3wCcNv8cM9lfUAkN8GEgffROq+BqXpaLQU+Vm8K9FJTeQNTj1w\n2/SmFFEYShMtSCc7l+OhLDeBL4bGcercsWrPc9shl1eJ6PjLmhMKu1BblCHb\n8teCe3dDNpMIOrYvXTazPSA53Y8V+tXh1IvlJyeUcpj5/bqcHRBiP57Uq/cJ\npF1bh4AdqhppuZRBgBfgVpMDJBc6DcUu13sQi030QwH7uR+93VXHzoD8mjKp\n6s4NGvnsc7cGIL7tRbSM2nq4bG/7qlGSoDwsoxX9xht8RruktQUkBrroI6s2\neLr0H8vt/Qy1eFr4EhxVxY92NhjrvT1mgH+So+2ieIAnzEWNYHtd+ATamZOY\nms1pZ2MGYsM6/EMGeHp4pkAkpvDo8PykX69geVCLhGhZywlPtPx+TMZS27pW\niOlqDhRPGIZTkIm7Wru4VSsAB71xq6Tnodo/bDF4dIdiUBXXJ/B2I9xy7qd4\nXFINbo9gY4NnfmeM9I4JWsUu9S6c3dASR5xKXo0bSlKninwAx3JUmcLAjs5T\nccmG\r\n=5Stk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.6.0-beta": {"name": "eslint-import-resolver-typescript", "version": "2.6.0-beta", "dependencies": {"glob": "^7.1.7", "debug": "^4.3.1", "is-glob": "^4.0.1", "resolve": "^1.20.0", "tsconfig-paths": "^3.9.0"}, "devDependencies": {"react": "^17.0.2", "dummy.js": "link:dummy.js", "typescript": "^4.3.2", "@types/glob": "^7.1.3", "@types/node": "^15.6.1", "@types/debug": "^4.1.5", "@types/unist": "^2.0.3", "type-coverage": "^2.17.5", "@types/is-glob": "^4.0.1", "@types/resolve": "^1.20.0", "@1stg/lib-config": "^2.1.0", "standard-version": "^9.3.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "2afa666d2e7d48cee0c7fab4f8545f63487bec5d", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.6.0-beta.tgz", "fileCount": 10, "integrity": "sha512-tBc/X1toOgibcy/7pIpPegzKddeR43TiPrgL9sz/TiTMtgz59f/zlGjfcbNumnMe1U1BRlg+2xSlOi2oXUO/Ww==", "signatures": [{"sig": "MEQCIDG/jLex+udWT3MDjzt3mIsNqh9pBzrXP5X1FE01ZUGLAiBOkK9zlBbWzYVo+s2bhI9WN8sWuPhbPFgAo9iHoOu6Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNx3mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrbng/9HDNZcLX1nni6UEJ+AS96i1xOAkzNjkm277c0HPGYTuTqc7zt\r\nqSvaf6HujFuadG4ZeOg5p2D4w2Ioe6K676on8v1/u48gdRLBs1xRtKz+XBAy\r\nES4SiF9MlvokQVppLQDGIHw4UIPK+eRjCDbHJdRI0/jCbiPxtbRqHpsNSKOJ\r\nQBQF82LDWQBy6x0pnr5iH/iyDk2wxB7MEKwO4IlmOGKb92Qfc2zzM47I3DL6\r\n5F9yG8XnqXmigztlVzO5MDa9b3ZrActSt//NYmnROkfaSaHRsZIfaeRHlhg1\r\nweoj+BWm52tRBDQV0iUjofG/OHju5yAfxOrU8I/Z+95RcF7iAMMjUQrtDTPu\r\nrFTA62UbScWp1VLqZy1sb2MoJV/EF7Uej0Pygk/tspGuFgFNEOHB3P0NxUYl\r\nZMTZFF6HzrQVc0tWHTbOFShK9AgLqjb1BSViGlFNIvPu+4y6Ns/s/X8wDMJG\r\nqdoDB/rkwHEpgKrs3HvKQf1o3TfMQxKh+ILrvVyDVRUfZkP9wFaLLNvBxMsf\r\nDXy8gv27EMK4MN9d1Idlxqi+M/7nonCFKMoY/fOJoqka7r3EFHxyeH4u5BDK\r\njfGm7FYlbGBxOiHUTJyPK+AFtdkRBwr9XrwcCCGjSoOkN3MsUotOsIK7IGdB\r\n1Q7Pp3dw/okarYdxfqgxRmcuFjmrHD57MWk=\r\n=mqgu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.6.0": {"name": "eslint-import-resolver-typescript", "version": "2.6.0", "dependencies": {"glob": "^7.2.0", "debug": "^4.3.4", "is-glob": "^4.0.3", "resolve": "^1.22.0", "tsconfig-paths": "^3.14.1"}, "devDependencies": {"react": "^17.0.2", "dummy.js": "link:dummy.js", "typescript": "^4.6.2", "@types/glob": "^7.2.0", "@types/node": "^17.0.22", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@types/is-glob": "^4.0.2", "@types/resolve": "^1.20.1", "@1stg/lib-config": "^5.1.1", "standard-version": "^9.3.2", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "4d590da5af56321c4559a1c1429f8cb7e4b14e7f", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.6.0.tgz", "fileCount": 9, "integrity": "sha512-ezQmsZthv3vjegzQpzQqbxgc1aujIbFlwsIL+ed3U1l2kJqwY5x3DatFmn1q0exvjPoMopmoCUClTTnj5+aUZQ==", "signatures": [{"sig": "MEQCIA5C1bWPlrRmF0dT+B6hbxLNW3yn4o+x3ouw/Msr4jKRAiB7sj7/eXuu76Rne1JD2IR/Btl+fKfLnAGE4KHwJhcaaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOvp5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos9A/9FcDOlV9sHl9UVOKNLB94/kWkbtgP3dG8hT4LC7wP+Db8TY+g\r\nh2QHrxKBthCAZN74vQTq7VF5CJX5TxUOJcvJi1N1h5WX5mXWX8gTzNPSZBr0\r\nTBi2QyBmjVohNMbD5pIzKk2qLGOOQT3MRFRF2fA7HcUv/YXju/ELPzz77Sxl\r\nzp1wp6nWkAjwxUdMSjSPqVQPruohoEqvxo79mleYUrgQIgFjQoQ74ziZ5p6+\r\nRH1JQZQHQBShYJZfEb42PdCzu/QFMQ39KfaXinSq/WVjyGPysTk+SFw84Gx7\r\nnEEniweRLBOBtMnBAdImWvIyXSkl807TANq8qGiCje56+BB9g1WskHrmEkrZ\r\nRlsG8rCyEPIE0ACFktkPDOvRw7GgoxuCMF9jBEVtKPHY2S26QEvVAR4RcnHZ\r\nyRf9WkIlPD4Ldi9NgB7MBGquB8qNGXIe522Ce0pfwid7NYQxtft4Ha4yOIQR\r\na4P0U6x1oxZLLWsITTQkpgSvLmKmQReKm7411XC8XMpd1XLJUwmD/BcFHzDs\r\nxPXVOUIcbSMch3TgFiig/D+yzJxxFjI/QHtcI4VgfMG8muLx9EY6w2WWNh6q\r\nnPSs0p0i/NKZMaFxDjXPd2BI6XOLqdPiEA9M8w8JqQjg7TaNDncw/ZNkKKbX\r\nJvHvpC0HIMMjehRFcRG89M4G9PCcIgrYvgs=\r\n=Z/iv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.7.0": {"name": "eslint-import-resolver-typescript", "version": "2.7.0", "dependencies": {"glob": "^7.2.0", "debug": "^4.3.4", "is-glob": "^4.0.3", "resolve": "^1.22.0", "tsconfig-paths": "^3.14.1"}, "devDependencies": {"react": "^17.0.2", "dummy.js": "link:dummy.js", "typescript": "^4.6.2", "@types/glob": "^7.2.0", "@types/node": "^17.0.22", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@types/is-glob": "^4.0.2", "@types/resolve": "^1.20.1", "@1stg/lib-config": "^5.1.1", "standard-version": "^9.3.2", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "1f9d391b636dccdbaa4a3b1a87eb9a8237e23963", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.7.0.tgz", "fileCount": 9, "integrity": "sha512-MNHS3u5pebvROX4MjGP9coda589ZGfL1SqdxUV4kSrcclfDRWvNE2D+eljbnWVMvWDVRgT89nhscMHPKYGcObQ==", "signatures": [{"sig": "MEUCIQDKQgRmMhE/Yc5BOLbijc6rCFz/p9Sn0cWbcdaufz28wgIgTK8oK0+jqTkrztsGtH/keyArLfOUbWKMGiZzifWwyZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOvwPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrVA/+JOwa1fNFKy7ENLEzIMTS/iz0rJQ/fdzn1bK3mJ6kEWMxRzEp\r\nBZmrQUclpIuUSkT+8IXRkk+uX2ZtZZCxLBEAFHbV2ZCJuBBE1O222WTy8O/K\r\na0Yn/ViACFMSbbGUb9c1DYmVhrcgiOuVj0vLf3EeCd2uL1U6HjpyaXwKNaiM\r\nbxaZr2DQCHSLSxfrbFv8Or/zGN8x2gOhgh1HaTtNDQdCtpXg2bwyKoRdaAuA\r\nFj3kFBIFVB0FCGrX7DKgd9P+o/HtsoIowMob/nY71xm0NvsjUcJRdKiRuNxf\r\nmpp31ECyh/coPjSPxFyIseg74nyYy2TcoID6veG6El7Xncn7Ah/6F3Sp/ZLn\r\nYQQrRrnYw/UAPVIpMIMhSS/vWQZRmLV65wxMTm3G+43QJuw0BHl18xiIAQ2p\r\nTZ6twStdEOZU/Rl+01OrKbeoFjaVlCi9zUaqK/QJXhPUrn0hpe+kZNmfDHqJ\r\nJUsEqmFnZEEf7srRhQaHfWd/6g+kyOQ9PAuVOYL+sVl7g3ipAxzbqPX99uyh\r\nbkrS26yWxG+oMU27G63DIF+L+LJ4FV53vbw9iCz/EbPHEY+VXM1LcxOsiERB\r\nFJGADp+P5Lj4xK5WzgGkCmX2XdLreaYS5z/rntUnXZyVQw9Q8rmclQMbI7qY\r\nydQPQHpUyeq+3l9aqtcf7TV6Dl0tGR4BfW8=\r\n=lYyf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.7.1": {"name": "eslint-import-resolver-typescript", "version": "2.7.1", "dependencies": {"glob": "^7.2.0", "debug": "^4.3.4", "is-glob": "^4.0.3", "resolve": "^1.22.0", "tsconfig-paths": "^3.14.1"}, "devDependencies": {"react": "^18.0.0", "dummy.js": "link:dummy.js", "typescript": "^4.6.3", "@types/glob": "^7.2.0", "@types/node": "^17.0.23", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@types/is-glob": "^4.0.2", "@types/resolve": "^1.20.1", "@1stg/lib-config": "^5.4.0", "standard-version": "^9.3.2", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "a90a4a1c80da8d632df25994c4c5fdcdd02b8751", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.7.1.tgz", "fileCount": 9, "integrity": "sha512-00UbgGwV8bSgUv34igBDbTOtKhqoRMy9bFjNehT40bXg6585PNIct8HhXZ0SybqB9rWtXj9crcku8ndDn/gIqQ==", "signatures": [{"sig": "MEUCIQC/4cYMWTPQrIXW8MdUrhusif3Fsr6qrXGxxeW4W6RgYQIgQTC1raAVneUEcCH7kiaaVccXeJ3CXT6/QGEg3xVcWrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSRZ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+0A//TVQVjD29krVWFvdXs8JV/iffoadjrnD4SUrBXIai0xbul9G0\r\ngs2+u/RqP4ntbzC8mmQo2RYMZyY9cke8d4KMWjQ0ATYeFaOZAuWODRipdqYJ\r\ns5QLFzfrqew9hc/TyXie/wpbdsVt2ks0gXXrWPZ15C+pO6TMkaHWsAOMdsOC\r\n4V2UoqSlLLFI3x1cyT+FzFZn9gj/mSz70KGy99NJ1ncl6d2SXyC4Ajiam7Oc\r\nW2Bix+mURGvgQCh0V3LnSyMZhU3Z9SEkFEb3mz8p0GTitsi2BdEFaSdrGnCA\r\ngAlyRl8UzqIfvBx3gpxYbwA+kxskg1op19ATdmBzQZLAoJK7LWtpz1AkRDtq\r\nZaRcP30ciLnO0xbawyYHCPuE45JkfOaEVA9Z7V4xpmVnJU+4mmV1FCRSkpe3\r\nmthM8VAXfGavt4iJ81Mznq+QXKJDIrS+jsJwZc9DjZDLKWpPGy5x903F/ay0\r\ndkWgTjAS//w0t4PJilx9yWSPfiHlNjAY7ofEY71BCbR7WAGHKd1/XzgskX8D\r\nPhl3IPGIgmQ28OD7bMGupu7UlMy8RSCHZngrajSdZiEpYrz2wOOAgiQFKlMt\r\nEqMP0SrYKVRyhY0aU0TlcZir7KOcuc1gYvfSKTUKfEFv/YaprEAHtUZwOT8r\r\n2pfqJDlxYJSD4xR+P/kcli8+RIuL9gmaXeg=\r\n=J+zt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "3.0.0": {"name": "eslint-import-resolver-typescript", "version": "3.0.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "resolve": "^1.22.1", "synckit": "^0.7.1", "get-tsconfig": "^4.0.6"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/glob": "^7.2.0", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@types/is-glob": "^4.0.2", "@types/resolve": "^1.20.2", "@1stg/lib-config": "^6.2.3", "standard-version": "^9.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "cc81a71ee2d4b6cce500578ad6c9eb7a8596cca6", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.0.0.tgz", "fileCount": 11, "integrity": "sha512-ZNQ8AUe6Che+3AePG7TWHwYGaQskUJNM8yPPp9nTHh4uIMLkqtdvX4Q0m6dq/RViWWdjQ9eH3m3qBNR29lDpaw==", "signatures": [{"sig": "MEUCIALg9Zg6VRRmFc/AHSybvCGE41j0yphG4UYVSfo5MWDUAiEAs/XIF8tTA8Aill//FbkT1xtcPZ3a+DocLQJtQJPWCmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitrm7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1gA//Ta1rV8R9cSfk+w2uAEX6NEIqHlip47ZGPFRWpzpdItO87ohI\r\nVp739r/PcWvC32zvl8EANUGpa3YPpZAnwUxnRFsk53o6IwIoMdpMEH8CCEOX\r\nFN7jk9fuitBznyldKRwxBhXMG0d9Y8NoHrCadsrNLCr9r9uRN8jX4y32jI46\r\nT8D63UtFZ/f3ems6tuKe+w+oVrPuNaOIMrJHBJVoj5XYigO5YyljbgeuHloF\r\nsqt5a4v5og7jyMoSsa/bogbBn2DNFzELg4NEzy6SvOfSbHHoGaTn7ixXzpNK\r\nkjLWEbUwtFZpvbWcpmXLAXsOiLc3H4T6XD2uAL8k8KC4QTR/FGvQ/d4F3UEC\r\nvHHp9KMQaGLXVi2eZHZdanNVF5Mpc3dlU8ZhS8s7ZJV9jF/orB8f3Wvtl69K\r\nZMS18mPj9ciK+Nw8CE2lzOUqmPa+447OpScLDxsWlSZIjmS2fhpL+xU0l4co\r\nw5mUTigKbmYhvWp8qhCKor/D2XdwPvLOTRalmVcBLOjYKOCdFAZEbDLuZc9R\r\n49QbFHT54U4MJ9Rib+xt+I16Tm+HnNBSg/3OJG9Q9XijqI09T0oSnu+BPEvA\r\nQBVg8wj0CT0gZU29Guav8gy5y5SZ8cLSoZ7DOaBSs9615eJt+VDJcyuIfQWD\r\nM79llpbEL4wQ6y178rJTs2YNSf4NMl39zyk=\r\n=/GWo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20 || ^14.18.0 || >=16.0.0"}}, "3.1.0": {"name": "eslint-import-resolver-typescript", "version": "3.1.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.0.6", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.9.3"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@1stg/lib-config": "^6.3.0", "standard-version": "^9.5.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "c2d244ad9285a27470959743ed55297748ee8ee4", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.0.tgz", "fileCount": 11, "integrity": "sha512-sWRwY40hH3YKZL4eMbXHKAbmnoN+CQ/XoehVX4POeJYhaM0zKf/HZC3qzvYcA9mdaTD/63H7NSRMujTkFgvUXw==", "signatures": [{"sig": "MEYCIQC8mkbw6P+1UmX6lobxmp0jR2K2n8aVZr4Pk6OgihS6gQIhAMt4PTLmL6vs9yRW/1qsHpGBWSa3skS9d/l3dagTr67X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJittaDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc3g//U5ov/gvB9a0kYf9Z0Ii2ZuWIZ8AfUnh/RymqvPAG0HEfySih\r\n3N8ck1Q9gIGTa3rhRuNwZZ7q/LZFi0SbzUshxcJn2JPZPndSjZGeXJQkMdI0\r\nNbaAJexQBgErxJNuIx57KlJQpXpTCJ23oxZCYSYOsb38bjyE2gc264HHJ8df\r\nU4VCmzJRMQg6NGb8GMY5GIvW66BmsCxHpe2HinECR+8NVFXmJukFf9nt1y3q\r\nEmHJ6Lf7PIfpUq/TcCWbGE3h3YwQDOgsaQDbQP6QQX9u6QEECHnfkRDGRusi\r\nFy5qhcHfxqGNzyYX1fqYzeldi1/Pm5GLSU39AUkkMAXs8j7II4Tdn3v7J2xM\r\nINC+VtZnk0TssgA3ol8vjCmWpi07+BT8qgk3o7dm7jEbzlJcUDEPKyZEOMq+\r\nu4seclexN2dg8WrlFV04qVzghfSydiFXif2U8yLshgL1IzWFIRRKtur0KHkO\r\nYc9xBXpQ+/xZSsTSZdaeS3sCwmX48nD2ELNM2UUOA5B7sUuvVNO97oA4htz+\r\nYyal6VpyfIDc87HJXluIYXryTaU2vPhZf5pvZcIkeuesv8t6stAbHljSrtXh\r\njgj03ACAPgzRbSztmH3zt4cjOHvaTLqiGTvkd6c4TUHAzUsrDlFy9dN3oFEE\r\n4RIMQPUoqzMv+m0y7iqHtPffX5shvuHFIXs=\r\n=HfW6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20 || ^14.18.0 || >=16.0.0"}}, "3.1.1": {"name": "eslint-import-resolver-typescript", "version": "3.1.1", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.0.6", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.9.3"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.1", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@1stg/lib-config": "^6.3.0", "standard-version": "^9.5.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "9a3e4b9260a39608b7e42fd12190f19f9913fcc3", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.1.tgz", "fileCount": 11, "integrity": "sha512-rnC7s69VAXeg3RVhPIhiX8M/fHiPaDyK8zqw8wUDYy34weWQcfyvN5sw/74HhGKiv/teD+BF8bQMZthoyrWTvA==", "signatures": [{"sig": "MEYCIQDcGASG26GJgx8BoRFupJtpjAQL0CnMXUTABRA9IKOx/gIhAM6aFYZ0hhKVu2cKpAjeWYCPcPbKkaq6g7pgCtiVt5Ha", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuYTxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwjg/8Cr20lsbeyyb1GIobVbHRvQicpVC6GODJgK/w2/ec8Wc/WOYE\r\n5PlENFXaroIwnnK/1Jqcm1SlPZdey0vJLFiGxRay9ERmbjeu5HmgBgBNeKMs\r\nyUGC3ZpokxfmQi6VTr/MnhQd25X0aJemukaBOEHdnh2PyGe+5GYxnUjX0/zf\r\nkCBuuhALWM0Sv+utp9YrymO6xBtBKwu4VJOLj2k3DM/8AbPygFST83DOFvvk\r\nbiqKbuJ8RcDS2bUwgr5GTCEvAzLArC92Mdne+BkunsOiBURTduiE13aCpR3u\r\nh8GSqfxtZdhHXNwl6tKgNv1Rad+0wsRsxpOLGK6tVqnnOY/E47XjsMGz++15\r\n5rhz/DAnVSL+Ihtpj+0kAZRnLSBx0IsJvxduOVXUJPicBRZF/X33fdfJ0HCY\r\nKR4qTnTB8gsGnr7eIosYPyyIQbqK1KdeCsV0Y8tlA3nbFIn4ep9zmqX6BbzQ\r\newBpndR4rC2bY6Ip7oDuzEADT8Pek7PIQoUzpGcQUCs3feLLNIFdKmxkSxW8\r\nvuH12wo7psGZaNK8XL4sRUmKTDvT0W71n4r1C1HrsUVeLwYa4e/70TyD3I6M\r\nYklq8yUAp1YVCDnqUh9tBXqbPS4KHWOw3O3mkpecB+Rt7FF4Ha9LrCBx5i/S\r\nZbwUdIdFWA3RpsvdjkqiAa2iJyiTEdcStzI=\r\n=+mua\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20 || ^14.18.0 || >=16.0.0"}}, "3.1.2": {"name": "eslint-import-resolver-typescript", "version": "3.1.2", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.0.0", "standard-version": "^9.5.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "988460fb709205d8f4e8ad58a0cafb3b401e86d5", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.2.tgz", "fileCount": 11, "integrity": "sha512-M/qdvFCkdYcl2Wy4zHr4iPVbq1vSoKDwzyxg2bO9v7eZ49/fIB+ZWUet7/+UHeqDdFgiTtkr9AJ/vWJwNTbkCQ==", "signatures": [{"sig": "MEUCIFmFtiNdfaYysHUNbsq+RPaEnsYzN2f4ZxvNbryhBeLiAiEAsPntrfBTDFd6GfXJAQ4YQ70ELKjKiLYhmnzcpjw8uyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiveomACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEMA//YkaxgjYpkPuWLd05UP7Blgrm4UJv5pX/8dJR84a/krF5rl4x\r\nspCsBKsUAyE/268TOMjTQ60dkX0OY8Fh/+wDbTwtv4kfBisBk4MBHTiqH4QW\r\ncO0hFWl5A1Dx9ztTFumvpPxy3tYA4NhYSO96tSgFS48njrqdHcj8guvqbr0p\r\nBt5ScrC1ShdaP16XxkgFjcUdiK/Sd+lVL/xorx9rzp9y0IYjCwoMYRxTEiPq\r\nKVx7ebwRr5T3S5sqXquQPYbVmgyRP6jDQymZjjoZt1m22KMI5PN5hH4P5gOb\r\nf3xflcafuQNZlS2H5Y9UHdZY8NAYlD6kz8W4GbIqLImi3qh82tEEvp4RiPsj\r\nGnw+kgLxe3iEtqJMC4SMTuy0Rbljmdsk9FbxkzS/QgPw2t1TswGauoN2ySqU\r\npDeCHWlufwp3O8SK/VHETaMNvH9HqZwfRPsFt4OYY8ll7TuWh0a8L7zQcFj/\r\n3oXaHVHqoFfWoLpv99BhWBcTfMH2UeHV5ohKiDBoziP5uOoK1BNzkIS75/e/\r\n/9qLS5c4K8/o16YlFKtnBbr8+Dyae7oDvx8M3gitVp+Rd/0t2eEM6RgNoflL\r\ntRGTbv6rQVMLOiRa0NKQ3rQNmwtbIBdpY4wE3HI+qosqOzlpDtovLTFnFVQe\r\ns5yKtD21UbVSMIs/DigCZ3UZzSZC4A6r5mk=\r\n=+z80\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20 || ^14.18.0 || >=16.0.0"}}, "3.1.3": {"name": "eslint-import-resolver-typescript", "version": "3.1.3", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.1.0", "standard-version": "^9.5.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "ffb2014469184d64ff5ee4cb5b944d9115838fb4", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.3.tgz", "fileCount": 11, "integrity": "sha512-u7YJAWmgJlpwvKsEl0khVJdRsdLNfReL2Sc8hrc21iEEVEOTKH0Ea3nUtfdlhHuQieJ7lFpOJd0LbDaWpb3+RA==", "signatures": [{"sig": "MEUCIQDycnkDl+LgpduJpG/4yNROL2IuD6iMZh5bX94jyYrJDgIgH3jDd5O52w/mXGP0Iuig+Z/q5cULfxw+ge5/kvVdq/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivq0oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpFA//YqTcF+/a41QJP79mEkssSJP+L1Wv9fdWOnQy3hYgI+doK1uW\r\n5YnmbZ6kDtlPvNxV7rwQF4uOIai03q6CLT1fdSXp/jVHODsGHZHRk8Z+I5VP\r\nVDz15rDNXcQLH2BusSLy2M0fJK/rXWnF5WLvkH7M3yJK7399AAyNOX5mEiXq\r\nRgyFTU1Cw8h1nZJMkQMY/dwovjErnniUQTFmuOT4p6Asj6jz+BI41zrQJ0hc\r\nhGGutBFfd/aYiVu1m5QWrlKOyaQm2gmjaqVUcv56y6qwleZuubYf1kM36dq3\r\nh7/bEe/25Gs8ZPpsbHndzqrcfuJuY7ATQqKYNRPOUsO+el+2SjrYIuCPcwv0\r\nvZDwsOg+Na5y50i0NnWbC+R2Vtbw51ewmNxMGeHoxdFO9CEa/PDJOc9BnjFA\r\nvnYGCBzScG3Dr6y1Cvrr8gbrlrTTzyfQNd4PAQ8tZvpweVeULttnR8DB1wn+\r\nj8PlegdNHiXmteIQruEEjryMEQ3g/hwMq54T1VQARbISHNoRArqsXG8BNE/e\r\n9QkZdgQKBr1DSr8YBQGGpb4xD68qVUh5D0eEL5sQT3OfTi3X+B03+U7Rh3mW\r\nELk47hx2+7yD4g0Pg8x4pFLoj+2MEnd8jMW76onfmiijZiNsGIt7fwwe4ieu\r\nME+5Cqs+2Xqne7t72cUz7xhpXVKla6/d9Ck=\r\n=KETM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20 || ^14.18.0 || >=16.0.0"}}, "3.1.4": {"name": "eslint-import-resolver-typescript", "version": "3.1.4", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "a1b1425143b28594e9a483b025ab3600aea391cd", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.4.tgz", "fileCount": 11, "integrity": "sha512-cDOnlg4ZRVMC5ZQ8WNpd5UsOg1YFskGBw15KXSC+sogExW8zjSycc55iBSNKdKuveCMof2VtLz0Dw4WkXAHkag==", "signatures": [{"sig": "MEUCIQDyGp1ZlkONlbYKaXlM8XzPK7yVrBb0RviFXIDyHQ7ncAIgRkIyzK5UCXCx8ZiOOVO8fumKK0SqLb6PJ95+lFzt/Ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivyDNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWew//WmEulJCmjKQPam0P9ilGDSObBEbPOPeOkVF+9LHT/LPhE22T\r\ny5M9rcx3xEJX06l3/TsjOF8UFgB8uweOALO24rUM/5MfqW519jP/AcJA4GN4\r\nO3oTdohMBoYmHZO7CRdVZ8a52LX0RMy2OiiBoYNJgW0NImLmzhSaCnDqJJ4q\r\n2NqIV5D3b9RWCrglsAkaziTC0laLAuJ8ZbzQDtQUhcfMeT2IuEjhZ4TQkVb4\r\ntwm9NcHST0h6nljMVptEcfpctEMecMOreHkxoqhkCAO5jaCHqtOgnnnU9T6y\r\nwTx59OonhQI2onME4dh0x8LbKXZ0w3LTqS2s0mPJnMH4K1tz9tUvQPtjfJ/+\r\nMsykbQldmbhDc46VOe86c+iP5SYVWHbJuCGyVKlcqhKtZpcrj4gEYDQWrUpj\r\nqrHF/5AX2A/cWO02ApHYrYtE7NB6ktzFmYR4f/8s0TWZqgUg/vRAwR+oj2Di\r\nPakWW08i7ZnzqnC5cy2taeOp++Vo7YKoYWArWFYhSi7jYdTwxCS837nNeZdQ\r\nZbo7BkId76JePdnZfNmW2XVUsoicuEVs+0FAsKJK//AhQZo06O9hRK3ZANsG\r\nGWYDpBtc+7f0iE+5BWg0GDPCT8M/gNxExg4Bo4BDiJk3oWEON+4MKhyEoWjV\r\nt6HfhzoPkNkvixDAmk9N8TpafgH6bR+xYes=\r\n=LaWk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}}, "3.1.5": {"name": "eslint-import-resolver-typescript", "version": "3.1.5", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.3", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "d22d6911d496855ae7758ba7f9bbc078ce3b3cfd", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.1.5.tgz", "fileCount": 11, "integrity": "sha512-e6RV/fghzX5DGP98dg3ZmGF4qr1QzVgJdbVkYl8jJ2iO7Ox9wOdTNMBoYRqbvavf2ofT8xKTd4JnY+X4t2TtXw==", "signatures": [{"sig": "MEUCIGuQ5MArjCgSwqSFxrXMBnpVO8QRJQZZtXgJctFx8UNmAiEAy8G/gDNybU+y4pyvC7QAzQekkV4wHH5pKdUQzZrJlec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwGzNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQwQ/+PAXlNWecCD9lROfN+00wXMuImPePQ5+/CAhxbOe/k/1DHsBk\r\n8Nq6JcBaEBlgcwE6L48eBX3acABDkVXmjKEa/uXGmi97lB1QeumYGgCBvi6N\r\nP7Ij2AHp/5IM2H17lB1sJ6XSL1BNBzHnNQ1ZdyN2btNM1yc/0en9GmnFDDdX\r\nP+yRo61O5k94e92FGJyUs4PmHVX6EJnxYKJ/arZO+YoKRPIcaFQk88KZ+7+b\r\nzqTbx+2j+tbRcUi1L9hLYlVteXw+zEG11n1ErHt4jDA/uaUXRSdW2Qx2Wvjy\r\n9g3WotysmfyguuempFHEIf40XjbR38G9kwd+Q/O6tfhqT9SggMYfAIwqDW7q\r\nPihOPIMXYui/lumn9F8duYRWHVLDrqyA2jsrqbNddLXrsGFpvAT2BcSi13ES\r\ns7amd1Ll0AX0L6uEaduh49YQcNt8lRmw14AHLCUIcSmpwAyhWWA2hyys69+t\r\nGZF83itysgvcjSi7LRWJHQL+ZoP3yFGmsFX6ofMrlYruXIMO9AFw9A6Bm31p\r\nuERtE9lKkhxbCGCGwG2PNnES90dr1W5/+yXmk+TJACDiAnd06JcD7MfHejUv\r\nIHVPrcA/lAZhoPRj7NtUp9BT8gG5wAQkz7+yxb2YKzj1CQFJZLXIUTCt6ipC\r\nBJCAcNHAwageu4XzlfoSqTrhI5o+5YoPLmg=\r\n=AilN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.0": {"name": "eslint-import-resolver-typescript", "version": "3.2.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.3", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "46564cd263e730744248997664f9225f92e7ca6b", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.0.tgz", "fileCount": 11, "integrity": "sha512-ibNQzMUB7NcBU1/pB8WuQJ1Z9MuoYx4ULzFNlnHzTi0m+eagEgETARIQNNuKjZYi4X/LeDnhCYOGrPTvw5ehAA==", "signatures": [{"sig": "MEYCIQDD1qMk1lWC1O2+iFFdQ3k7gKiQ47G+OFvyB540fyV6NAIhAMeC5YhNhHtPQnA7BWvPhwrVVgPS/F1EfCnm+s9xSZEZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwTchACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXVg/+L80GQA9AyR75kRCfSyoWEL9Ih6hSS8CMnLaADn7VRM39Fhzt\r\nA1gVHxr5WUoTnaFfIPcIp4bTac/RZ2pYi0yPRSH7fEiBowV63w7pK4+11k2o\r\n071v642FY6Xk3BOvsHy6bvetRFfTukMSBwNVvGEeIlK/5ADZ+w4ySTieq1uM\r\nJByvYd/XuJIIquYih9MT0QJASGPh+fI1i9cfSZwEHiOax8310/Ci5ImOQvv5\r\nJn6G2QGPgz4j8GOfeMlCd1Os/kEX55e2kXiwf7J2dGJjv/qk7VqxAahrxIcX\r\nr6MDES5udV/XfDH04VHPjZYAxpBqgr2H1B4jb32v011TL+pLBZ2E0Praa0S8\r\nPXJiUDQglgE9trE0+GJXRCReP6fs70RfioIfhu7GAQB/PXApXzmjUiqjG0gM\r\nBWx3KUUXHRz0+a4xO1GkwpJa/KGTs41Bez16OBbDGTZGqE6XRlLtKkkSndSt\r\nPvMbhC+G3FuoHZdyE1JrS5uljJc+N+Z/zulktJIAdl4pWqeTiVRku0PM4UAf\r\nu0ZtQcHvilGbXuOdsh8EJI+ecxwLSYkh/T6+glgdWUUFpEZiYy/9LCMfWhNd\r\nthSjEeR0/eWiIMvm8HDHD4LO2jB28r/zMxMRjfp3GH2R3n+LWgaer2bWC71Z\r\npv9vWpk5+w+Vz7OBam1hAfQodfdqcEW6sjY=\r\n=O4ew\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.1": {"name": "eslint-import-resolver-typescript", "version": "3.2.1", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.1", "get-tsconfig": "^4.1.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.0", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.3", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "62770f5659547f258ade68ff69661894dfd86efd", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.1.tgz", "fileCount": 11, "integrity": "sha512-3BStJ19GFJCzvhyPV8UO2xPxsAqQ5v41h4WeUsF/Oyy70AjeQx2l1RfzpJL1H7K7xNMiayty4uj3BkNWhgHDtg==", "signatures": [{"sig": "MEUCIDDWs6SPUSoN72XrqsivGO5FKXiwY8VXLskNrIPH56MOAiEAgCA5B2WmCr0npnygKfutWLIPn89Fz9Q9RgDbWPHIy4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwmXLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYCQ/9GNh4LhoCnjBzvrTDJWcRc8QmwPBZisja0xwawAK/rkkUY6QW\r\nVETQzWckBT+TlUUHRYggvcjl1kPCNQtDwoI9mDY1XMvHTTEtRemhKQYkMNl+\r\n1KdLyovt2Z3c1tIrJ9W2bofLnpo5sGNo8PDtXTkZrwpqnj+bKplU0m8CecSs\r\nXorEkpQ43MTrvygYPrqCM83QAoie8YfErOgPTm1PyY7P6UoOj6sJN+IvvkZ4\r\nlHh7eiyP4q4SaB5PTSFk5YeljG8YSDUyJfroXG1nqwECxPnyeOTMeLN1tb00\r\nOJ9UeNDzxKkD81Z50C1AqAXCI0xY2epsDO4NNtDPUx+zMEbBuyTOQ/AMNlHt\r\nM/kU1zWLNbYcGv6geqElGZh1s347ZdVBLrV8iFfjeZLzhrTHOKblQkOSbpGs\r\nfMPiGu7cdbOSoZYbk8ae6HnQovbMJYu1o/EbloSy4rX7KSCQgOn4HUsrP3pG\r\nRdop+FDoiPkHtK5/HLiZYB4vaKY/YbUMoYYynZeIUMLorriPKtR1/HAclAlg\r\nKgC+3uYnoHzO/UfvuhhP6ixRSFvrKvqpsxdZn7tjlSBa8fcjVvtc19fUvZlU\r\nhZkYQ87v4DuGS7Mrru4hjn/fetIDhDzNIbWAQXseM6qZkxEf9gnGf+fhpsAt\r\nG4RX+cfpQj5vP3n7Tz4QWTLoA2yPCziupA4=\r\n=BkOn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.2": {"name": "eslint-import-resolver-typescript", "version": "3.2.2", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.2", "get-tsconfig": "npm:@unts/get-tsconfig@^4.1.1", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "eslint": "^8.19.0", "dummy.js": "link:dummy.js", "prettier": "^2.7.1", "cross-env": "^7.0.3", "commitlint": "^17.0.3", "typescript": "^4.7.4", "@types/node": "^18.0.1", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "@pkgr/rollup": "^3.1.3", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.6", "simple-git-hooks": "^2.8.0", "eslint-plugin-import": "^2.26.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "ea14e1368f1b89091f8bf0417853c42d1d04b424", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.2.tgz", "fileCount": 3, "integrity": "sha512-HQpwLVfbPFUsyb/dw6XflHpLRnYV+xbId0YYZ/xvne5X4kMkmwRWyTKCgKRZvakKoyo3pRGtR80Y0ThesNN2dw==", "signatures": [{"sig": "MEYCIQCaF1t0VAeMyr5Z48FISVMCGLDM1/KeynFwk8OYXW+o3AIhAMEhRhfzM5P/dlfNxRbq3aDNPnHoMRB72ZBRIXUVnlnl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixIfCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3hw/+InLr2UbcQacW4rX01Y3u2Wm4HnIWiPAQczzhX1VQMxDTbb6c\r\ndhnlAw5sVeisRcAsDMVjkesCzKM1L78V6hLVc6bRVHstCrJukuln9gVDp7k0\r\nAGmTla7VmeNxWq1Rc+4AcNn4xdbF8emHw6iazTWt9Xbs7dPRGC8iIudiMa2v\r\n6Z8i5UA6o2OL4ryFCFQWouYHu2ZRHqyN+5Tza1B6R5+ByjEKkwyFaFcIxrCG\r\nhh0ToIjB8y1BNlJzaMyoVx/2WsOGrZ3U1+6u0Ky88S4uosforfa1CmVLTFld\r\njBoZWmSbUIRsT4ricG95RLZTnBWR1ftDY5ZkGgDPNhERaLMOzHs1dcfuqjAF\r\n9ow+1+diWG8CzEndykLmZtZ6MQ44VL5Cun6MdeT78f0ky2S6kflpLaV22+UC\r\nE070qKF2lqRyPHcyLm4PkLtaCcRUUiajNmSqKNNMcYdt6Q9u2nvBlwib5crD\r\nfMrHkP+eQGowMHs9T3R/h6zfGP60OuIG42R789gZOTadeoZdnfH96uQpMeyH\r\n6VphsWJf/K1VOnymy7CPwfN6AsqKhq5H3JGOURwZxrS5XACVn7Z+ZqkgQQAt\r\nLbbvQKti1WnXgwSO5ZRwmOCdUCO2PhpQpJ8QQqjEWvoznkL34+UsaFdUfAgI\r\nFNdKoKPLhWzo1BvRFd9pg3vGK1nO5gAYOYo=\r\n=GXLd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.4": {"name": "eslint-import-resolver-typescript", "version": "3.2.4", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.2", "get-tsconfig": "npm:@unts/get-tsconfig@^4.1.1", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "eslint": "^8.19.0", "dummy.js": "link:dummy.js", "prettier": "^2.7.1", "cross-env": "^7.0.3", "commitlint": "^17.0.3", "typescript": "^4.7.4", "@types/node": "^18.0.1", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "@pkgr/rollup": "^3.1.3", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.0", "@1stg/lib-config": "^7.2.6", "simple-git-hooks": "^2.8.0", "eslint-plugin-import": "^2.26.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "74e930272de0ed84489f6a139d2c0db80bf7c8cf", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.4.tgz", "fileCount": 11, "integrity": "sha512-XmB2RZq534N3cZajuyMb8c2TJCkCHtU7gUHZg2iJaULIgfIclfQoih08C4/4RmdKZgymAkfHTo4sdmljC6/5qA==", "signatures": [{"sig": "MEUCIC0TFG5hO0F1KRWYyDbZziRQ6vxUHZ41S64AkNTlnldnAiEAlNJ0YtnwWfP6wZKqradcvgOljZ22KH+FlKgtOyVQIsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixPh3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohExAAjvKkU+vqFPVkCSajDSUn//UrR0Yw8V+VwzL6SABZaHvnZfOt\r\nEC7/nuCRoQWdYAvWeHJ3fi809dHaPKqjZpTw6cDtEFJDZ9D0kPH+MvR2NSC1\r\ne00GCHWti5nBT7ihD6LJhRlmCeEnPhsJbCeoMEgvYZlELIL2Su25oszIcQHF\r\n9rHgypoga6ubdpYUqY4lja36MExihHdxFViGfsELVMBYTmWSUWWC4MbfsFEp\r\naeIWH75h8WvmyRZImWWazFOYuvq8GZSOvWftl7VV+5AjBBeHasuQOYaXX66l\r\nlqVfJyFBfez5cZ2dMOJ0fJBr18FODpzn8+z0YIvIMEEiP+EHre5jtPpwkLSW\r\nFpzuTF5BDIBY4hEWS+4OMzz38m0LDsOMgubMxCq0zsNeqcH5Jj9CLFdmHeBB\r\nlpH66EJXQ2mSFN3qMp0eaD0d6YEtwsPFcNbHsHRJQ3wa1TWJHkL+h12RDxvt\r\n0MCIox2eIo7wbANda1YiWbpVKlGadJPdITCxbFi5XeuxEcCZF38AgIP9lyiZ\r\nghhoq7+690dO4SrgU5U5FpdVCkinTQDGQ4P/Buj/3757eMaS5P5hfI7N7cE3\r\niL6iOnSpeMgJQ0eFT05UpODCokB0CzFEuVhJTeqiOrCjmMvClmbewGaCAuJA\r\nI7Zw0ORrTOADu70EiKsYh9ivdwO0goMlFPM=\r\n=75/o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.5": {"name": "eslint-import-resolver-typescript", "version": "3.2.5", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.2", "get-tsconfig": "npm:@unts/get-tsconfig@^4.1.1", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "typescript": "^4.7.4", "@types/node": "^18.0.3", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.21.2", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.1", "@1stg/lib-config": "^8.0.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "cec82e42d93f61a039672e2ba7dc3e3663c86219", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.5.tgz", "fileCount": 11, "integrity": "sha512-yEBi/EWxFFMjcZTBxrgdu5cFAXB2atOhYDhp0P0yHqjZa5YiPNqQVt4/lNNVWwW7Kf8IIZmyeBboWOgsfffe7w==", "signatures": [{"sig": "MEQCIFaV7iOe8SLwNY4jR3Au6AVhwWgANXcMxbZ9SJY5RP+WAiARKOnLQuURn7u5x4jCn5TEwxoLTuP8iWiXgtHXtIfMpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyGjNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC1g//cZZfkDbGbjh64kvgqGMOVOtvfQBiAnVHaOqpR30yb9ex3XWZ\r\nZPlLjEQ1jItGysIUXI+fC8Sf6zRqRnOO3YDntcFNahVBZZhWBSFcWbBHt0h2\r\nb5hUt7056UOKdkjImxeA3uLPHYI/PYHUwytGCpT26kgHWuUX265PjclrinV5\r\nyY+eSEHCLA/hL4qnXCUH3VS1jGFKXYffqBQgbAP/f193Gdqe6WPbAZNLHq15\r\neNT19sMKyNHDQYHH+T0Sg4WTWxKwhdQWutFu85r6iDIbQ1uxIsVYhwhObDOe\r\ntsQcWbEed5xkXKWI25BFDr/SFcgqa1A1ArLZVt50g8JN0bMGK9bDSF/jN4U4\r\naw5hZXsGocSPp5cT1EvdsCiun+mVRs7eYG6rIFugerk+KbQKx8DZnbgPRoRI\r\npY7ATUbeVE8cRQF1MV3h4Pv97MH+GfViF3Wws6etGEFIRnp4+Sdbp5+txL8R\r\nTBc6q7OTY1Sr7XHfZyp/0Efa/3NbghKlAbsPFDD69xGtcTcaGUo6KUcLYpgU\r\nAIjDxQXiJAF5vGQFATjPJYky9oScvq2jjxHTZdWfHxigYpa1Vl3tAKi6NiTD\r\nBz5x0wRX00+Ys6BgdjE57JCDDxmN+K1uaclh3s6lEeL/kAa1maIaQlul9jSM\r\nhxy18sBOJ6gWsNNyZ+7SZN6kgabg3fL7t5c=\r\n=nd29\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.6": {"name": "eslint-import-resolver-typescript", "version": "3.2.6", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.7.2", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.4", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.2", "@1stg/lib-config": "^9.0.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "dc967647bdc34e8fbf7baf004ad94a30c0fef302", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.6.tgz", "fileCount": 11, "integrity": "sha512-Gk7+viMP9Ho5Q36gmXw3UJrnSCbMTStLS+TuF49SdsOUQGNvhg7ZVsnaieuN0FNNWPB+Xf5tBwADgEuoRPeDDw==", "signatures": [{"sig": "MEUCIQDApsqjOwkkW8eFCGJerq+S/d14+pYkbZAq1hQzrlTuhgIgFi3Z9koiShp6H8mWljFXoI9irgOFfCfXmldde2mF+ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0S/xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLiw/9GGVFdVmO2dmuXXLsCBTV+8DF9TWHVtod4uuA+QKEtEQlYqNL\r\nt/NjcGPf8cdB9o8vm0vfeFlrr5PZz/kulSV3XMhddZeGuJyeT3v+2FLkt6W8\r\nTTJxPM7CPMlioAflwYCEsepORu5qkoZy27DETNZvX/qUKcGjoqL4X62BMua8\r\n1y1C95G4k3xtyaZ7PTMS6Sn8tmZVe/fy1WCjldnXDFz9fN/X7/E2syksBIb3\r\nzz/v4NfljVbztrnMR7mkItnYgvuXXznnuEs+91E9IAcFAnJt83FqGpcdaJ4+\r\nrwrhQtecCgrTHLr8CiVv01ntd6ImUrp54rXWyiJ5WBQUecKN8j+IWvQacrag\r\nSajJo+jQC2yeqFAY+aMQQHxeNRbu2Usv1xHLEWIBbGmTOdXBk+cs7/zTfwZW\r\nnm2uc0+0qlNxe7JkoMccFwBBtKRUPMO0jaJ9DdWGU/zSdqSATxlUFxioXcWg\r\nOdtTOOys93Z3CDDNXDlgIawrHjxsW+OTrKkEIOVIt2xfKKicNqFwHB50SKDN\r\nhF26Ll1tN9+ToktbosN5666mdDGZdiwQfkg9ZLKfi9tEMXEpYn/azmyST10y\r\nazErY+wVpdea8BObm2cwNoM/r6BB4OcdxbcbrFry2xpU9nIYj1AhKK4LJGAx\r\nsan7L1FvLKoXx0N8EZFfbJXI2Ww4481A1ek=\r\n=l2Bk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.2.7": {"name": "eslint-import-resolver-typescript", "version": "3.2.7", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.1", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.5", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.0.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.2", "@1stg/lib-config": "^9.0.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "b873d549d9b00a880c54f9b352f7336c303794b0", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.2.7.tgz", "fileCount": 11, "integrity": "sha512-WvcsRy3aPmwVsuS/XVliAJWpIdTlaFXXZPZk3TCbvvF8RtaAkjAhcLL5bl5VEoTmE+XnTHjIbWMzNZcOQpK/DA==", "signatures": [{"sig": "MEUCIC94aT00o9xwSEW504Zbhv9IPKJEPhta82wVD1AeR12tAiEA7rsnxZLDCRPxZYGt1bMnOLz2ErdSqrjlxlvNZK4a6io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1DT/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3GA//Rj+r9sXZ8dDfukaKUX6OsBPhtdl9latcD7+u5N2yQpsFLFmZ\r\n0cdQXByAwGGsvv+81lislj2RkGwkMacFbQ0xid0UKITMJWVEJLEcAE1HCOi6\r\nhqNC4FRAB0TpahWU30XlbbcPZNZhsqVfjm6jGO7vMZIcypcc4NZIZ0a4RxdS\r\n0txdJerWZ9UX4npq+fgGtqxBon1dWG+RK6NTFUEmpS8D+o2zGJxsQWi0eKFt\r\nX/XQl2pujOFygv4am+OlFgciPDBdUnaTZwGorrI/uBL8oMttx2ltho3NwZDy\r\nq3NcEWWY6SIg4RgHiCuk15DRf00eHM3FtgWv4YEV2BnpVwxgZPX/a8ARLvHO\r\npzRkWUhwzENp3dSXpJu7dFaT20ddRwNcMs6XzfLKlcbXHXUzmBgXCzCs0iq7\r\nck/IHcvlAHtJ8d1JEqDpS4v8soteJZtU3ORnZoNngKonzwVKq6lP76EVa5ZP\r\nB1k+rY8bgZVJ3YDCRL5cmoxsjuhmh9SVI2ORkQ+UvH3NGiELzHaSYbnSyRfa\r\nPpzxTwvchhQyBKEhAqHq1HRBKIA4dT3rZAYMenVc5kqGejLeA04cB2XMFhI7\r\nPyNueU6TB0vYUaQBUg1BABrTkkR6X5FP37jmaygDobTgvjs1tquRmnAR1P7z\r\n/Nvi9cdbBM9tCXmG67Iv9fDi5NIPK+o3z9E=\r\n=LFND\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.3.0": {"name": "eslint-import-resolver-typescript", "version": "3.3.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.1", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.23.2", "@1stg/lib-config": "^9.0.1", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.5", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "c2b9f420563bdcb4b84d550d81e579f8dc867d5b", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.3.0.tgz", "fileCount": 11, "integrity": "sha512-vlooCGKfDX59rH5TbtluOekinPlIS5Ab+dyQUoCCJoE1IV11R/kn6J+RoMBuBkJhzJEIKJ4myQJhw6lGIXfkRA==", "signatures": [{"sig": "MEUCIQDhl7O0wPD+zSPLYTRXV1H1WpzaZlwgG1Vv8y0Q31UEmgIgaDkeG7gPncLKzqpW9gZyapzPaBC3EpkosOTBUCMprQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1tS1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVoQ/9Fneo31XrJwZF71lZHP8zcRJyJq14+NW6u8UioYD0/VjDJxqf\r\nPS6fO4Y1oiSwYqqSZ1WGHQUbt3RKqtVJx3CW7Id+CJt5P4KJ+RkZZvcrfgbf\r\nzJRCqcpVhvKFY47PZUm+ARwolHO+aPrIxfVHRzOod7dn12T87pF5pKZp1+3U\r\n2yZ1nA/0GOmkWyxNSZfYnPxYoSTu1/LcTS+d9/IX1bSoUdjcsvzRlb3pGekF\r\nBWzjZUr5UI5TymyUczOcM1zLOWNixknNOdaJcX3I2mPTMUp0LLbNAJfYbkQl\r\nSaFx+NTG0LJZ8rRKjpgIjqQrnnHF2QOv/Jtr8qDXD6PmvC1vaVK1k0t67+mw\r\nhS5j8anXfALx+imalhhdXbPo5vSja6nkG0JAP96SEkNfDbd5wmt5nkTlJucF\r\nrvX5StATsgTI2r+8Aaj0NvHxKIpPJiWb8/AvEqG4YfvH3nZSUnLOKg9GQL/M\r\nTzGwguvFoaoJjgMYUgf0YCdrnZZmSdIK5hASyEVW3maTSxK4ubQCP4wGqX96\r\nhptrHM5/rqtSruOudmU4ermu0GZsItkhUptzynLAL/sCmmMWajK2N+dhUMug\r\nUsyI2GGD3GODEPNrATKjyGeIyyxAnCy+8HxOWEhIckMppUp+1vnLdiKsapSi\r\nHxmUb8EcNmAf/tMWfBNA7kBBG1JhEuxpyMo=\r\n=pATl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.4.0": {"name": "eslint-import-resolver-typescript", "version": "3.4.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.1", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.1", "@1stg/lib-config": "^9.0.2", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.6", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "a7e334b86d638f49956f2b0dfbde29daa9c32dcd", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.4.0.tgz", "fileCount": 11, "integrity": "sha512-rBCgiEovwX/HQ8ESWV+XIWZaFiRtDeAXNZdcTATB8UbMuadc9qfGOlIP+vy+c7nsgfEBN4NTwy5qunGNptDP0Q==", "signatures": [{"sig": "MEUCIQDZN6bbeUgJXAbZEIGusDs/GQpSytqe43cKjjwCu0sfbQIgVepGi6iyjWoP/uh0DSPF4u3y0+SJ9TFuMw9m3dIpokk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5+51ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6AA/8CUHTwCEFrucrR7+T10/CikDbTWpHwd6VSK5+uMUNr0witJBP\r\nI1T2fwVH4TE43mQfyJRl+Hf8Lff+hMYXJPmBNsVqTi62VPcKrL5n+i4Bw5ao\r\nEi2d112goVpEl3hLIfVI3I1rTaru7FsV+qP3yzow4GO1oO9DJA7SV/BRlmde\r\nOHrVhZfbeX10nK/uXRkB/tNQ0YSdoOh+AxkOlXCCjzOdMPH3XzzPOTG5MqiH\r\n0yvb79NNRbBpaiaqs1gAyGwGE3I2k9QGsndEMg7ty0v7irPG8/nno3WJMGjC\r\nksPvu6oBA1+TlJByacz9YZ2z0Y22rtPKdPd/boxeWNeUdf3lXMipYC/+mPaA\r\nXJ5OnLGrvvaOrfPqT9qL+Aa8xj5SnskZxwbCgULlnGafV5QPEoDOz22gB0GF\r\n4eTl5mvvFaQG3VHIl6uo8nG7ZIzN1HEpxNPn88erfIBBBQl2gmOyW6sFoNGF\r\n+cmw5tEffABCH5axn0jo4jQwj/8jhZq8EZB03iNoAms0TboTXI7x2DbtsoDY\r\n0PyRD1NykXBsfH7GaN1JtstMEvIbYdBfsi2DjDF93qz09JIDL00yItkWC3HN\r\nSXDd+zbbzOOcByO4EHD2XHtJVIX4/1cmbqlpwXcmTBxwdxixEs+bTtPOSoo2\r\niP2RS0rolw6uvpbjLjgbMwsTT6mKdlN0t9M=\r\n=H0+h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.4.1": {"name": "eslint-import-resolver-typescript", "version": "3.4.1", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.1", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.1", "@1stg/lib-config": "^9.0.2", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.6", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "072e5f7b4bf5d62d32d0b3a071fe551b45d15454", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.4.1.tgz", "fileCount": 11, "integrity": "sha512-rcD4V2nnxk76JF6nuLcclGpya18KLhr/lwpl5xFXrVWZtdRSepfCGHk/oFn9HNstWX317Nuo/E3Z1vymPyPhlQ==", "signatures": [{"sig": "MEYCIQDNhZP0wtvq6UB9sCLSZkhnh0fxNpdSIZkPSjfxRWlSywIhALo7Q4PYId4cK6N4cegN2CZ4d05QQF3yLojyAFkKUQvU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9toNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn4w/9FsIzqM4LnhY8TaE/QmE4E+9xBYvCNfzCg0OKovAOBhRR3HPp\r\nS/rDCY7/+izlI+RnEoKAlxEVTEqH3HctrMwnWY5Lfp74lX+pAt6DmD+nvEwd\r\nV8aMEBgGO5NfqHFrPqdHTsMincIqakBWX1naLi7WE1iY54TQ2s9i3OvLimZu\r\nnZN7RYd2PeUKfqyTaaF9j5HueBNX5t0s/+gpYs2de4ALEATb6o+uQs4tlJPH\r\nXvi7W9yrjhzl4+S9vIeB0TwNWi0pColrzLuqsrr9DrjhVMVs2qsl+akuVLPu\r\nTwDemgJovUNmmn6Ir5vsB6vgxv/gapFG4/MMwiY968eOnOmzP8RrvCbc93f7\r\nTT4a+B9TpZ8Xaf0SEyRngNwh80gfimKdTh8c2LQSGf6PFuUqiMgtshKPBm27\r\n2KtdzGo2UWLHtFcPjihn+706CSl/vRBawel+RUTxlZns7NszabMnYnlEw2XU\r\n+9OOxjw60FfIRbpeNl30/pWtbJjXxv/F1WFyb59/NAvzhRt6EHMJXNzr77EW\r\n6VDpoPR8oAKSLewp4c+x/dBdeBUVLgnA8bRaXQGBqNpFGe0hmHUxTnf8jfMB\r\nFjdX0AEKt8mHEOMTzoEitAxYALqcXVJQ35Uue4zbDzjxjaqaDpm1UfpO9by6\r\nAbAM00Jm3cSuk56emdVM344KFXMLzcRiOFA=\r\n=drxc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.4.2": {"name": "eslint-import-resolver-typescript", "version": "3.4.2", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.3", "get-tsconfig": "^4.2.0", "is-core-module": "^2.9.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^7.0.8", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.1", "@1stg/lib-config": "^9.0.2", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "@changesets/changelog-github": "^0.4.6", "@size-limit/preset-small-lib": "^7.0.8", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "9ee568aad73e63eee37b7b5344da5467806b90a3", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.4.2.tgz", "fileCount": 11, "integrity": "sha512-8SuWlRIEO83X9PsJSK9VjgH0EDk1ZzNI36+r3C0xNYVJ+O1+TprOFtTwqqyPMHG+br/I9A5Q80RE7K3/eIr9XA==", "signatures": [{"sig": "MEUCIEsu699Degm1vZsMsVCLFqGOMtK4Bdq6c7QLI2m/reOoAiEA7ZbIXeSe8O1v7A9Nn/j1zfQVaUOaLNSc7NiYApKbAbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/XOcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrf8w//ekKZjQAPBQ5SVggWJ8DHAtrc5n2/wwy7hc6jiH/6pdi83R04\r\nOq6Xtx8mgAO2bKuMp+/fQc9/z2eoeATg3xuXI/273C9Pww+GNDfP4Q2PpJjQ\r\nzdLnbEqPy8y0oDZv1abvymjqvJ2Ta3stpJTz7tOrmYSBGUWNw0ZfvRUJJix1\r\n3aEOROtTSFYBbJYXFAxuhVQifqbTf2tBiXxD9CHJwk0TdgVaEM7JCH8mREBw\r\nRureZXP/IpSZNctrQQvvz4o8/jYA52aEEtreAHxsfp7h2tVCHrAa3LfESqI6\r\nFT/7yURnawX9OgLC6f6RBzotB/pV0N2i46sZTplDZKs2hUbnPcJctpqXXuTo\r\noVN3cWZ7hJr9DiL/5uYKDhz4Yewi3AQCV3hNtoRNEJtqYeqgD1cit42rQb/B\r\n2otXR8ysuzv5F/rQ0fuVSJYl3P/+hlu5PXaHRNdOHn8bcfMikJ9x4BvvDY4s\r\nd86/WIzmwKvvJMgRBq4i1ajGOP0+Uvc648mtC1SEUKnsWstA0rmFV8rc4ZnD\r\neuv90pWgcvtfET3RhhGn3I1E4U7tRdiaU+gQwUJa/uq4gzcV95LZ9MaRvXXj\r\n6IeRY5sseqwjVRpC9jPXVH8Sbkzl9WHbYWORIYH4s0Zn1avE6yONAzfOVdS+\r\nhVeXBgxfvgrsCsSlhhoaSjrjtKQiab7EmDE=\r\n=Eth1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.5.0": {"name": "eslint-import-resolver-typescript", "version": "3.5.0", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.3", "get-tsconfig": "^4.2.0", "is-core-module": "^2.10.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.0.1", "typescript": "^4.7.4", "@types/node": "^18.7.9", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.3", "@1stg/lib-config": "^10.1.1", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "size-limit-preset-node-lib": "^0.1.0", "@changesets/changelog-github": "^0.4.6"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "490ba48cafc5a2fb209bbc7e360defb4c292ed59", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.0.tgz", "fileCount": 12, "integrity": "sha512-DEfpfuk+O/T5e9HBZOxocmwMuUGkvQQd5WRiMJF9kKNT9amByqOyGlWoAZAQiv0SZSy4GMtG1clmnvQA/RzA0A==", "signatures": [{"sig": "MEUCIEci2O9k9X83GJi3sRid3X9QH9mGbxymGVVt1koZuR6JAiEAkXOHF5yRl6xjsO/Xcbjj+1t4UrJjdhNPgXm7vhhEXDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA5QgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/gRAAobFTjCm995vK7pP7zaC6OXuXDFPzsWVMv33bCyGgsZHt240r\r\nWhVjdMWO473lmVWzCoeS554vG7nwdEJyvsireOV9bH74fcr2+OVR/C2jYvve\r\nSZcbh/01565dGVzpRJpsVPvj7qLZP3Dcir2s7uYrorqoR/sbfsNQy29KOeLR\r\nqna/BCXZ3nVDf/2H6nIfTopZgbSggGD4IGHpWn+qWV2r8DcyFELSzc811snt\r\nWbxFKlwYAlvDJdF36LZ4WMQV/NWoz6Li3jfIqEmvQEZ0CRmBbIHonjvqLl6q\r\nyNqHxJ1D92cvL6KukckPguJ0aVTfTJy3toYA57H5wq2gobgjr+vFY5WcpV9+\r\n8WSat43XOn7Na3LzZbEq95sh78gm3Ci+4f78/Xztmwdg6ak6n4p6DbLNaCOi\r\nT+VBZStE4xOo8wB8O1EGP+N2diQUpth0UNhNK0aQh6yvB3yWxScqAB0D58e0\r\nUJcnLJoslVVHJNa8CgHX24VLGRLd12K/IfgDvWKrQnhgw5PrzXO2yFOaazQr\r\n4wxdW/MVUmLLRDmFUIaDStQyjG4jKzfKGyHM2MFiNneUBsxo5q6TZj+ank6W\r\n8B/imIie4wGEJYpXEQZvZl1ti2BSlU0aou2ooDjy/001wwAVQFzCPq3eBgQG\r\nkaKjq1hUhFcbemfb24jFF918JqzY63bzsQ8=\r\n=C9dh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.5.1": {"name": "eslint-import-resolver-typescript", "version": "3.5.1", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.3", "get-tsconfig": "^4.2.0", "is-core-module": "^2.10.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.0.1", "typescript": "^4.7.4", "@types/node": "^18.7.9", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.4", "@1stg/lib-config": "^10.1.1", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "size-limit-preset-node-lib": "^0.1.0", "@changesets/changelog-github": "^0.4.6"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "c72634da072eebd04fe73007fa58a62c333c8147", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.1.tgz", "fileCount": 12, "integrity": "sha512-U7LUjNJPYjNsHvAUAkt/RU3fcTSpbllA0//35B4eLYTX74frmOepbt7F7J3D1IGtj9k21buOpaqtDd4ZlS/BYQ==", "signatures": [{"sig": "MEUCIGtczB4tvXJPWe7FPWu6ul181g7b4TjfOygpcNhkb864AiEA+a9xZ33JsQUFKhC2CoiDiIPUzp8D23MjkM78EZ2ww38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGKkRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7kw//UAZpQFLKh2+kUBUxtgaB8WODK3QPkzUgRLaw4/ttPtDGkCE3\r\nolvo7JgHrKwCTsMIsTAsvWnfVo25DFxyg5byqxuWsYeLtH7GAT2vBtwR3qDd\r\nxq8/QRIhCfyCk9QhqRVajTa3NK++juLX5MCA8JAVPvo0McYKApOVUoATIYZ1\r\nA0SoMNNAntMWWydsRcGEQ2y+n1cgp8aMpJscZclGT4yt6gc7pgZx4Yw1ZhKH\r\n4U3m7v3+e/oj627zICtn0lGYQP5XbJFIs+69OX8mUd6k6W9IVRsJHLGgSXK6\r\nRY34ZqBqLFpK6EHegmFk70w+IjtU2FmmN9jY16bi5W6Y3SBeIrcUCIrwmULd\r\na3BUDiUNU3O0QUdNPbsPmaI3CB29g2siVSV0TF5xFmIuFozIkhM8Qn2fUqDq\r\nV5MrtDpYy9JlvReR/ZCfKgvKjsLDRbxRKz3qqZQeIkCxXwdWDP7Wjqf/cEWu\r\nB9TzXYHZfF4Lk6Vk8L4hb1nf3FXoAfT06eWAQ7KQCZ1i8WvAHyLSo5Af9YjZ\r\nvtNUY18QUlg7aqKYifCYJ/mWjCezPTpj09CYe0vrIEJJ2/LMUxvoFoXm1xpZ\r\nb9st75z78vcddWDbf5OpcIIlXcoGAMe9xXoXPpSsL5L6lXX/v6SyQjd2LdIL\r\nJP7QSU060QZi6KvhWAWhVahqThTTFEOtG/o=\r\n=KTaI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts"}, "3.5.2": {"name": "eslint-import-resolver-typescript", "version": "3.5.2", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.4", "get-tsconfig": "^4.2.0", "is-core-module": "^2.10.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.1.0", "typescript": "^4.8.4", "@types/node": "^18.7.18", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.1.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.24.4", "@1stg/lib-config": "^10.3.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "size-limit-preset-node-lib": "^0.1.0", "@changesets/changelog-github": "^0.4.6"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "9431acded7d898fd94591a08ea9eec3514c7de91", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.2.tgz", "fileCount": 12, "integrity": "sha512-zX4ebnnyXiykjhcBvKIf5TNvt8K7yX6bllTRZ14MiurKPjDpCAZujlszTdB8pcNXhZcOf+god4s9SjQa5GnytQ==", "signatures": [{"sig": "MEQCIFeDgLnoRKhRlWP9Me7o5IEUzOMLJYIGONwTUYc4jsY6AiBFd19QPzn+vHR23IsCF+cfRUaEqZXZE5OMqEOmjVobfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUToPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc5A/+Ow07pE9ruCorJEE1YJwEuxRr4Rh6K08eraiP1A5ltmO2gWbS\r\n9W/xywEeDOzluml7ACEl9L582BPAAJCDgi87Vh9/dxyw/rNIkmYUkyWDJYqO\r\nd4Y/krYy+5bhF/N5ZCampxTPB8oB9XvoINMs09ieXNFQTlmz96XhkkRq17sr\r\nupPYUBL3qnMRLXOy6A1Va5fNns8Qt4JNRURMCiY3kOI78pO8PapbqdV29uag\r\nWGNNWYSlY2LUh4KAl4O1fvHuAtCAXdwPQy6QtWn2YssTmFT7yuEMPhBGiyCR\r\nLOVXgK73zOIg16EvvERzZ4wYYM8/JPs0TpNFTO6p2f4ZYUiccOkSE98FTMBb\r\nNfZpgoK8GPcmwoaTq2zleqysqpeZP+N9ayth8xyOs8MYjKuaJ74yf2Db8vR2\r\n7Hjp5F/AKh8GZ0zuO17ZNM+J7aKs0T4JoR8YDeC6+HHTuF3fcTgvff9YcCxC\r\nH3d+LPNy62VTqJ9TzDqLzhP5AROZNeCdPdBLfQ5KP54ea+UTqDIGIU5ESC34\r\n5ehfhBB1wp2cImgfxzaeJQGSqCmKiMGU0ylax8NLG5Ai4G5/PJD4ffTXsM2O\r\n5OL+Lpew3nm+Bppk07b0GwFosHpA3nklsi/DC7HB44JzemO+nyIzWv9foeeX\r\nAycJd+6y4YoQg4/nFgFyqnYODGpeJxVvXNI=\r\n=iZGW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.5.3": {"name": "eslint-import-resolver-typescript", "version": "3.5.3", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.2", "is-glob": "^4.0.3", "synckit": "^0.8.4", "get-tsconfig": "^4.2.0", "is-core-module": "^2.10.0", "enhanced-resolve": "^5.10.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.1.0", "typescript": "^4.8.4", "@types/node": "^18.11.18", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.22.0", "@mozilla/glean": "^1.3.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.25.2", "@1stg/lib-config": "^10.3.0", "@types/is-core-module": "^2.2.0", "@types/enhanced-resolve": "^3.0.7", "size-limit-preset-node-lib": "^0.1.0", "@changesets/changelog-github": "^0.4.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "db5ed9e906651b7a59dd84870aaef0e78c663a05", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.3.tgz", "fileCount": 12, "integrity": "sha512-njRcKYBc3isE42LaTcJNVANR3R99H9bAxBDMNDr2W7yq5gYPxbU3MkdhsQukxZ/Xg9C2vcyLlDsbKfRDg0QvCQ==", "signatures": [{"sig": "MEUCIHLMA4icZl8iO2NUSmRGrn+/UAtYvIXUAXAT1S4mQeveAiEA9tteJ/TB1tizJjoLWpBz76cdqfid6UKhF/PWflB1Sm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvntjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrrcg/+IT39WoI30DjIW7tJrd/jAUs5BtRQNe+F8P6MOWI+bQXdr0tn\r\nKsCtYAm4LNS9x1DwZaNfuYTegzw5+fryTikdM6DB8wK6eJY9dVODGqTODtvn\r\n0GMmBfrgoXtPlRG1dVytY4VUqJQbjvxKHyfSkq2+hjKq6lJ+4W+rMSA9Jt7b\r\navajBSrVZrey/toroB33/2TmMYH2sjZyPJNntlEG+CgXcK2x+CmLxLyn61MS\r\nkMspoporXR4YhaIRPNuGLFCklJQBAoBtebq55TBm7nlgZRfkORLjtJFRrz8G\r\nLtZ6wVBBr+uF6c8Eqg/PdH4Firz66rZPoGMzK9ZuV35cwwGislR1JBtKrLl3\r\nQPxXL/1dWoPtGR4HseXKwomC9Iw+s9jhsvh98YpUqijUnGD69ncy0arne9CF\r\nx9iJzPGOduq+rSUldzHUo+KNwwYo15sMgbRSsqJB/vi7ZOEulhB+kPMf63uJ\r\nEqqoo2ylkdGhw91rDumwHW/seLiihmEbVqJnZBozVTWPtV5OOPfQEFTpJvDN\r\n3DaVYOHZlaEHGMNhgFS5eVhl6vbObDNfjA8JPOF669R4Z4R3wXUAsFz/XxJI\r\n2FicGOIx/zINzUof+hbG2bo5CmJR+50SneAY0rlaTSvWyttMRIA49ey3Fo+G\r\ng75C8L+VZlT0ZL1xofOn58FGzvcml0bxS5E=\r\n=w8MY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.5.4": {"name": "eslint-import-resolver-typescript", "version": "3.5.4", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.3", "is-glob": "^4.0.3", "synckit": "^0.8.5", "get-tsconfig": "^4.5.0", "is-core-module": "^2.11.0", "enhanced-resolve": "^5.12.0"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.2.4", "typescript": "^5.0.2", "@types/node": "^18.15.11", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.25.0", "@mozilla/glean": "^1.3.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.26.1", "@1stg/lib-config": "^10.3.0", "@types/is-core-module": "^2.2.0", "size-limit-preset-node-lib": "^0.2.0", "@changesets/changelog-github": "^0.4.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "7370c326c3c08f0c1839c592d79d20b704de15d4", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.4.tgz", "fileCount": 12, "integrity": "sha512-9xUpnedEmSfG57sN1UvWPiEhfJ8bPt0Wg2XysA7Mlc79iFGhmJtRUg9LxtkK81FhMUui0YuR2E8iUsVhePkh4A==", "signatures": [{"sig": "MEUCIQChx696PGE7Ab0brMxUOA952vjzyTCKNTuGdNXDH1NOCgIgEhhoRPAfh9so8vbu/vcMZqGsQmqMGCSkzeJ1IoITHOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJFXiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroGQ/+ILpHLydfVJ+eU5rBDUMRkmhQJmAQQslptRFJqsDYzIfgPzHE\r\nv6a0e8HAUFdSsGwpGxi40nJH6X2AyQ0hfClwic0u4+wlU/VfObKsGtr6tcCb\r\nPpC2zGb/ny4T1s8Ev+EggpHGXjr8VJLGwb2gLIj0cyOKAPffGcED42pqP2GW\r\nfgwa3dEKdgIOt+dTMkaCrnL0FXVhXzuf96pcrmSxL0HYFwDD6hGYKA5vLpUL\r\nROLFzfuRoZyTmcRoBSSMfQVcEqPJQwSryKHa1i0UHxXHAPt9SbnIYdWubttB\r\nIyQh2lvBzYrEzfnkFD7b922vucRYGgioKZEsEmdPDN7tSti6dfoOk4NOWSYm\r\nniPMLita1efMjwkGm5hQufc5WdIpKAYyXv+oCM5/FJfdW8GFzP64023IPigv\r\n5muj3td0hLUFPkq08upDWSY3ei6qdB71I02wrrptBU2igGR4T/8VNwqRGc3N\r\nzl4xJxOh9biAsx8w1EYgerptD28TyAfXfRGdBTrm1X10n/g2lIm2thYGjhCk\r\n96T3lHlmRF5PR9ulQN1ENwm9bVTodS6pvX7R/W7rB1PcdJz4enkFIwn7g/Xt\r\nf/WpPYeMTAP8oSSclOlLwPHG8QKWmBjAWptvFjuFTAmnLGd8Qmfi0d+IMpda\r\nzuqtfdFqHBXGZOtXl1T5G6mq1QKo6tgbjbA=\r\n=0lme\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.5.5": {"name": "eslint-import-resolver-typescript", "version": "3.5.5", "dependencies": {"debug": "^4.3.4", "globby": "^13.1.3", "is-glob": "^4.0.3", "synckit": "^0.8.5", "get-tsconfig": "^4.5.0", "is-core-module": "^2.11.0", "enhanced-resolve": "^5.12.0", "eslint-module-utils": "^2.7.4"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.2.4", "typescript": "^5.0.3", "@types/node": "^18.15.11", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.25.0", "@mozilla/glean": "^1.3.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.26.1", "@1stg/lib-config": "^11.0.1", "@types/is-core-module": "^2.2.0", "size-limit-preset-node-lib": "^0.2.0", "@changesets/changelog-github": "^0.4.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "0a9034ae7ed94b254a360fbea89187b60ea7456d", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.5.5.tgz", "fileCount": 12, "integrity": "sha512-TdJqPHs2lW5J9Zpe17DZNQuDnox4xo2o+0tE7Pggain9Rbc19ik8kFtXdxZ250FVx2kF4vlt2RSf4qlUpG7bhw==", "signatures": [{"sig": "MEUCIQCdwou+VQK4ZJ7VWZ1u+S1EWaCF/NXSqLL+230xW2QH2gIgDqqkYTMrLATDFvWQyvV3RTxvfo3zYuojDqdUxJxHLrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLaJOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorhBAAgF4k6zwti7bSVG2iMz1SJ3qcqWsmXCFLOzCxFHZ7ENQzorPe\r\nbGiQdYSOLY/6HsHNh8pFOVTlnYOASoqKg5WDoZjtWsRHkKDXGorfib6N4sOv\r\nkdkvphDS5asQi6B1UfGtROqHiP5re+KrsS8kQh5Ia37ufdX9qZrwwOD0rS+B\r\n+fQfoorWYiEQ4ZUW6BRhhiCr1iWJJcM+5nH1g/vl9mTleHV6BdiaRWTeTks+\r\nKjXCU4lqC+PchXNiNsPqc1/cThROVK1mhCcBJVh6KrMm3Qk/+k2TI6rAucs6\r\nuHBlwPqdWbuh9y0uGhf1Ldx7xQFFOTrtlchAIwUElPEuwsNaefl0q7rRpgdB\r\n0LTzk/iquy0VEt5pJ5kXukgfnaEyOO/QiCTOhgCQTSbIDZ3oKt5BhFFMnHHC\r\nKCiFhv7VhWuPyen9Kw7LenbGr71k9xfdM69Goq5ON5pg8ut4bycyhtNYUiDm\r\n2JT3UdLqNfBvZoGTjqOVPpVwxsUIYBa+zUthe27lENJDZYRsEc66lhPhMOdd\r\ngTruNK7XbPvI652Jj82tVcdaOvhgKwFZJkiXbleHaHSRPvOEcxS/DWh65Mmt\r\n4uTWenvGbof8GHWih4l4o9+W6G7/SbISF6Z12E6dLmElabJfj3XWjG/vR5Wl\r\n/4wpKpV5q0MJvH9yjKHVjlSkZ2/Q5B//I/s=\r\n=iKJR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.6.0": {"name": "eslint-import-resolver-typescript", "version": "3.6.0", "dependencies": {"debug": "^4.3.4", "is-glob": "^4.0.3", "fast-glob": "^3.3.1", "get-tsconfig": "^4.5.0", "is-core-module": "^2.11.0", "enhanced-resolve": "^5.12.0", "eslint-module-utils": "^2.7.4"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.2.4", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/debug": "^4.1.7", "@types/unist": "^2.0.6", "type-coverage": "^2.25.0", "@mozilla/glean": "^1.3.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.26.1", "@1stg/lib-config": "^11.0.1", "@types/is-core-module": "^2.2.0", "size-limit-preset-node-lib": "^0.2.0", "@changesets/changelog-github": "^0.4.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "36f93e1eb65a635e688e16cae4bead54552e3bbd", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.6.0.tgz", "fileCount": 9, "integrity": "sha512-QTHR9ddNnn35RTxlaEnx2gCxqFlF2SEN0SE2d17SqwyM7YOSI2GHWRYp5BiRkObTUNYPupC/3Fq2a0PpT+EKpg==", "signatures": [{"sig": "MEUCIH3fH/Ai5HmCyksn3KQl+cB4TgO0VPJBmBrPPsIjLA78AiEAidmtJ3hANkYn2sUncpvfSv5ogHGxNdk2H6qO6MpQyxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45077}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.6.1": {"name": "eslint-import-resolver-typescript", "version": "3.6.1", "dependencies": {"debug": "^4.3.4", "is-glob": "^4.0.3", "fast-glob": "^3.3.1", "get-tsconfig": "^4.5.0", "is-core-module": "^2.11.0", "enhanced-resolve": "^5.12.0", "eslint-module-utils": "^2.7.4"}, "devDependencies": {"react": "^18.2.0", "dummy.js": "link:dummy.js", "size-limit": "^8.2.4", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/debug": "^4.1.7", "@types/unist": "^2.0.8", "type-coverage": "^2.25.0", "@mozilla/glean": "^1.3.0", "@types/is-glob": "^4.0.2", "@changesets/cli": "^2.26.2", "@1stg/lib-config": "^11.0.1", "@types/is-core-module": "^2.2.0", "size-limit-preset-node-lib": "^0.2.0", "@changesets/changelog-github": "^0.4.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*"}, "dist": {"shasum": "7b983680edd3f1c5bce1a5829ae0bc2d57fe9efa", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.6.1.tgz", "fileCount": 9, "integrity": "sha512-xgdptdoi5W3niYeuQxKmzVDTATvLYqhpwmykwsh7f6HIOStGWEIL9iqZgQDF9u9OEzrRwR8no5q2VT+bjAujTg==", "signatures": [{"sig": "MEUCIBX/zQnBgLSpq35l+wn7CJKqAzVAfMw+VG3URxnsqJdgAiEAtjhArv45SBWEaqe1k0PMOXzAVygFjk3bv3HtCjCljJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45142}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.6.3": {"name": "eslint-import-resolver-typescript", "version": "3.6.3", "dependencies": {"debug": "^4.3.5", "is-glob": "^4.0.3", "fast-glob": "^3.3.2", "get-tsconfig": "^4.7.5", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "eslint-module-utils": "^2.8.1", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.2.0", "eslint": "^8.57.0", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.42", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.10", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@types/is-glob": "^4.0.4", "@changesets/cli": "^2.27.7", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.9.0", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "bb8e388f6afc0f940ce5d2c5fd4a3d147f038d9e", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.6.3.tgz", "fileCount": 9, "integrity": "sha512-ud9aw4szY9cCT1EWWdGv1L1XR6hh2PaRWif0j2QjQ0pgTY/69iw+W0Z4qZv5wHahOl8isEr+k/JnyAqNQkLkIA==", "signatures": [{"sig": "MEUCID0XGnP4nRBijX76TgCfZsA2D6ndSB1db00IxLL/RKeWAiEAoZO1xGyrkzc6bJDmKLyQu9DZ81pM79JdbHH9iGQD3J4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46763}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.7.0": {"name": "eslint-import-resolver-typescript", "version": "3.7.0", "dependencies": {"debug": "^4.3.7", "is-glob": "^4.0.3", "fast-glob": "^3.3.2", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.5", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.67", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@types/is-glob": "^4.0.4", "@changesets/cli": "^2.27.10", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "e69925936a771a9cb2de418ccebc4cdf6c0818aa", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.7.0.tgz", "fileCount": 8, "integrity": "sha512-Vrwyi8HHxY97K5ebydMtffsWAn1SCR9eol49eCd5fJS4O1WV7PaAjbcjmbfJJSMz/t4Mal212Uz/fQZrOB8mow==", "signatures": [{"sig": "MEYCIQC9y5vaaHemf8QRPLKQxGIoHk78b+Apf4XqpzdeZS2V6wIhANA2zbjp2hvINn6c1lrh6or2rGIFfxftBkDsTYZfIyta", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52616}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.0": {"name": "eslint-import-resolver-typescript", "version": "3.8.0", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.10", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.74", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "44a0f776d296b9c3823f4859441b54c2ec5fef9a", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.0.tgz", "fileCount": 8, "integrity": "sha512-fItUrP/+xwpavWgadrn6lsvcMe80s08xIVFXkUXvhR4cZD2ga96kRF/z/iFGDI7ZDnvtlaZ0wGic7Tw+DhgVnA==", "signatures": [{"sig": "MEUCICgY+R04KBiLAWGgoMz+AsMny3VJk2HXqz3hZqhvC5Q0AiEA1vk/hpP5HCH03d5ZWSKlJFXv+PCcy90ACrYGyQgZBAs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 61046}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.1": {"name": "eslint-import-resolver-typescript", "version": "3.8.1", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.10", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.74", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "43aab1e51080eb157d4a35fb3db9d737ea79e2f6", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.1.tgz", "fileCount": 8, "integrity": "sha512-qw5TPA12HTmb9CkcuiNrFtwhM1ae2FWysLeRrTbQ+/JKS///gbL3fQ5LRhAZnzkcqkScOvkB5Y5o+xgyQz1VVg==", "signatures": [{"sig": "MEQCIGZ/7vWDGAaVVHw72Vdf3MKSLjGP8PXlpfFgn3sNn7OyAiB1JmeijTuPZyhyB9HoB7U0+AcRlZR54+lFhk6AQIS5zg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 61305}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.2": {"name": "eslint-import-resolver-typescript", "version": "3.8.2", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.11", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.74", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "e5223f19f1cb11f68cad7dd5403cfe48d9b9b67a", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.2.tgz", "fileCount": 8, "integrity": "sha512-o0nvXxsatYCDTzI1K5b3aYGQ6PjpDGJEVN86zqJw5SEewhmmggfRTotd2dqWr2t2zbeYpIEWGTCkgtUpIEIcaQ==", "signatures": [{"sig": "MEQCIHDgMHMpdTEV6J7Qk5hyhxoHJeC/fTPWCWwWJAhQkILxAiAR/AOkVLpb0QqphCss0yB8RcjFYzyvSOCd8WaDh9saNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 61305}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.3": {"name": "eslint-import-resolver-typescript", "version": "3.8.3", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "size-limit": "^11.0.0", "typescript": "^5.3.2", "@types/node": "^18.19.74", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "1721a1e4417e57a8fe6bf9463d0db8e220285eef", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.3.tgz", "fileCount": 8, "integrity": "sha512-A0bu4Ks2QqDWNpeEgTQMPTngaMhuDu4yv6xpftBMAf+1ziXnpx+eSR1WRfoPTe2BAiAjHFZ7kSNx1fvr5g5pmQ==", "signatures": [{"sig": "MEUCIQDB1CIgmgblCikadUppot4uvS/ClcqHbplpnFgwwxQP9QIgXMFY3SRD54CVNjO1dMCjV3/EoMGp1eYf7cyl5GU2ZtQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 61845}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.4": {"name": "eslint-import-resolver-typescript", "version": "3.8.4", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "cross-env": "^7.0.3", "size-limit": "^11.0.0", "typescript": "~5.1.0", "@types/node": "^18.19.78", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "6729767f638de277946ae7349c4e53cc6f798ae5", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.4.tgz", "fileCount": 8, "integrity": "sha512-vjTGvhr528DzCOLQnBxvoB9a2YuzegT1ogfrUwOqMXS/J6vNYQKSHDJxxDVU1gRuTiUK8N2wyp8Uik9JSPAygA==", "signatures": [{"sig": "MEQCIDjKfqAs4tUzk4Xqo7+dPAuaU2dTcogaBoc8EgtlbJccAiB14uXHc1e20TC3YOBrLZwdudqDCNig8Mh10GVvKHcBwA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 62694}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.5": {"name": "eslint-import-resolver-typescript", "version": "3.8.5", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "cross-env": "^7.0.3", "size-limit": "^11.0.0", "typescript": "~5.1.0", "@types/node": "^18.19.78", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "ef7113c83d5b14d036efbf6bc354581b56afa8a5", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.5.tgz", "fileCount": 8, "integrity": "sha512-0ZRnzOqKc7TRm85w6REOUkVLHevN6nWd/xZsmKhSD/dcDktoxQaQAg59e5EK/QEsGFf7o5JSpE6qTwCEz0WjTw==", "signatures": [{"sig": "MEUCIGAArNcnTgZiJwNNCOGXqAEO+xPFc9NbJZ5eNuCIjVbzAiEAwHS9qgVBlaokTRTRh9ht7xAgecZW+073psUTsVyEn5A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65745}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.6": {"name": "eslint-import-resolver-typescript", "version": "3.8.6", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "cross-env": "^7.0.3", "size-limit": "^11.0.0", "typescript": "~5.1.0", "@types/node": "^18.19.78", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "002477b92aa9a15c74fb380571221f80d4404f65", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.6.tgz", "fileCount": 8, "integrity": "sha512-d9UjvYpj/REmUoZvOtDEmayPlwyP4zOwwMBgtC6RtrpZta8u1AIVmxgZBYJIcCKKXwAcLs+DX2yn2LeMaTqKcQ==", "signatures": [{"sig": "MEUCIFinWhx6Vs8N7XkFugoKoDcYMVVny2+KfGEYMN3gJ1czAiEAnpGNt9CuZlZFHmoId6pGhhQvqc4kdPdqG2pMhpyGpNM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66446}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.8.7": {"name": "eslint-import-resolver-typescript", "version": "3.8.7", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.4", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.0.2", "enhanced-resolve": "^5.15.0", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "cross-env": "^7.0.3", "size-limit": "^11.0.0", "typescript": "~5.1.0", "@types/node": "^18.19.78", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.5.0", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "997bb279098c555dd78469ad59e491560f79ebf5", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.7.tgz", "fileCount": 8, "integrity": "sha512-U7k84gOzrfl09c33qrIbD3TkWTWu3nt3dK5sDajHSekfoLlYGusIwSdPlPzVeA6TFpi0Wpj+ZdBD8hX4hxPoww==", "signatures": [{"sig": "MEUCIQDQkwG/cylddacTsT3srUyEv49NKy+iZtnJtAEwL2HDGAIgNbHvyEbzs4H02EpXKE5CPGuj8K+5sr8MaJhCqOGIAN8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66428}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.9.0": {"name": "eslint-import-resolver-typescript", "version": "3.9.0", "dependencies": {"debug": "^4.3.7", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "oxc-resolver": "^5.0.0", "is-bun-module": "^1.0.2", "@nolyfill/is-core-module": "1.0.39"}, "devDependencies": {"react": "^18.3.1", "eslint": "^8.57.1", "dummy.js": "link:dummy.js", "prettier": "^2.8.8", "cross-env": "^7.0.3", "type-fest": "^4.37.0", "size-limit": "^11.0.0", "typescript": "~5.1.0", "@types/node": "^18.19.78", "lint-staged": "^13.3.0", "@pkgr/rollup": "^4.1.3", "@types/debug": "^4.1.12", "@types/unist": "^2.0.11", "npm-run-all2": "^5.0.2", "type-coverage": "^2.27.0", "@mozilla/glean": "^3.0.0", "@changesets/cli": "^2.27.12", "@commitlint/cli": "^17.8.1", "@1stg/lib-config": "^12.0.1", "simple-git-hooks": "^2.11.1", "@1stg/eslint-config": "7.0.1", "eslint-plugin-import": "npm:eslint-plugin-i@^2.29.1", "eslint-plugin-import-x": "^4.7.0", "yarn-berry-deduplicate": "^6.1.1", "size-limit-preset-node-lib": "^0.3.0", "@changesets/changelog-github": "^0.5.1", "eslint-import-resolver-typescript": "link:."}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "e94219827696f7d17139c343642859d60e277e78", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.9.0.tgz", "fileCount": 8, "integrity": "sha512-EUcFmaz0zAa6P2C9jAb5XDymRld8S6TURvWcIW7y+czOW+K8hrjgQgbhBsNE0J/dDZ6HLfcn70LqnIil9W+ICw==", "signatures": [{"sig": "MEQCIA0u9Lil+Pe/Ww71I8PYyIiOaWH02f0fkVm+i9vg3r6HAiBmwBRxE6DVgMzfHr5/JVARj6Dm5gvs3QMes4HqLWzx5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 64943}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.9.1": {"name": "eslint-import-resolver-typescript", "version": "3.9.1", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.3.0", "rspack-resolver": "^1.1.0", "@nolyfill/is-core-module": "1.0.39"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "0ab8d0ed911e875684a96976a118adee5d1c9daa", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.9.1.tgz", "fileCount": 8, "integrity": "sha512-euxa5rTGqHeqVxmOHT25hpk58PxkQ4mNoX6Yun4ooGaCHAxOCojJYNvjmyeOQxj/LyW+3fulH0+xtk+p2kPPTw==", "signatures": [{"sig": "MEUCIElrxbjODoYD66SdsuboJu3RTEvGWVEspeXV/y7m3DwZAiEAjHfjjuihvPB7/KvNcZx+vHwzeJ4jGxDqMys+ijvUcpM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 63413}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.0.0": {"name": "eslint-import-resolver-typescript", "version": "4.0.0", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.3.0", "rspack-resolver": "^1.1.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "3c5fe80059fb235f098b3fb08a949152182d8f4f", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.0.0.tgz", "fileCount": 22, "integrity": "sha512-dnZZVh4I0yYrKDwT+G9+QvatOjArurtwZS3n2i5SKfFYiy+669z2wkR8ZI5gh8yUGJYH86kUhp0FkSlKerl02w==", "signatures": [{"sig": "MEQCIHzqsArvvLSDBaARbJ3WJdOFc2jcegOy+lLo8A3M3Lf/AiAtJB+V82Yt3D23wkE9eUy/1Q4mP9Ppm8k1mk6SjY8r1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53101}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.1.0": {"name": "eslint-import-resolver-typescript", "version": "4.1.0", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.3.0", "rspack-resolver": "^1.1.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "d7e1bf4f762a9015145f476c8e1c658a8f449ce7", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.1.0.tgz", "fileCount": 22, "integrity": "sha512-V3DQFboowa/c+tBVm9qeUpD9onx31fwSWAnggNBd+gPBVuv/3JvvHCCAX/JEfaO/G6ruKaoMNVq06u7vnMmePA==", "signatures": [{"sig": "MEQCIHpnyFcD+v0eiGw4KJ5JJuVN3tua68c461i0lrE0cLydAiBcEJXZ0U2GDvImoG6Q23C8XXvtjxHnOGUgSIjCl7pEiw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53855}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.1.1": {"name": "eslint-import-resolver-typescript", "version": "4.1.1", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^1.3.0", "rspack-resolver": "^1.1.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "56c34f436377c959ebbd6e88740e37cfd5d6dca8", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.1.1.tgz", "fileCount": 22, "integrity": "sha512-91As8PwH6xjPwndAbvhTuZ3DUfdl4HttocWRyaLOd5T4uUo6km8EDO7Sve4jzH50V1wbgql2nOFbE3GGfARJag==", "signatures": [{"sig": "MEUCIBg1wF3lnqM9n/MclHGh9Wx08awhS9VagyPXbwq5RoMuAiEA0rJKpWDpXhaA/8tOdbB5jYqS1F2QoDY3HdIoSiAlqZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53956}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.0": {"name": "eslint-import-resolver-typescript", "version": "4.2.0", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "rspack-resolver": "^1.1.2"}, "peerDependencies": {"eslint": "*", "is-bun-module": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "fe03dbfb61360dd1d575da28c8b52dba62cbc1bc", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.0.tgz", "fileCount": 22, "integrity": "sha512-VpsqcxzMY5q/XdXzlyCxSLPCbwNLMgCFGjCHzuQPgNiQvOzTVM5VCEHVA/3Gn5m8egfW/A+7Cn+/58ZVRNd5sg==", "signatures": [{"sig": "MEUCIQCMdlfYGXot4GENBifTlp1MrRKwUgkdJ2eRBPsie0D0HwIgT+Dq5s27xvne+M168X7i4Eh+ivmnpzf2H3TqBYYRBY4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54338}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"is-bun-module": {"optional": true}, "eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.1": {"name": "eslint-import-resolver-typescript", "version": "4.2.1", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "rspack-resolver": "^1.2.0"}, "peerDependencies": {"eslint": "*", "is-bun-module": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "a48bbe43490dea494eed34c2bf5152249aba7d84", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.1.tgz", "fileCount": 22, "integrity": "sha512-jAAKR08YRFtlRpxK3OnixV0JB88lH3Xo7HWn1KpKlEvtVUlLMAcRCcBvWT1KAnfT4jPAs4veyFxuUqSdg/Vd3g==", "signatures": [{"sig": "MEUCIQDXTn7iwcw0kgBFGkNBvIWuagwp6oLp1fnti4BGxtG7qAIgXoyGbbcrF3/y1KB8GYg3GoMnjNpe9lHq5YCA/6adRYU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54366}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"is-bun-module": {"optional": true}, "eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.2": {"name": "eslint-import-resolver-typescript", "version": "4.2.2", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "rspack-resolver": "^1.2.2"}, "peerDependencies": {"eslint": "*", "is-bun-module": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "1b5d302af7b4b8c0b3c1076f3faf8bbfa9321b0d", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.2.tgz", "fileCount": 22, "integrity": "sha512-Rg1YEsb9UKLQ8BOv27cS3TZ6LhEAKQVgVOXArcE/sQrlnX8+FjmJRSC29ij1qrn+eurFuMsCFUcs7/+27T0vqQ==", "signatures": [{"sig": "MEQCIDbk9N4kSolzohQKTBKxafhXAAFxSRcSfiQy5CZ7febmAiBagyt6uyY1zGTL5FhiohEw1Qme2fvM74ie5a9ZUlPvZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53688}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"is-bun-module": {"optional": true}, "eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.3": {"name": "eslint-import-resolver-typescript", "version": "4.2.3", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "rspack-resolver": "^1.2.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "09feaa0ed7d691d6025969cc33e553b12be10e05", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.3.tgz", "fileCount": 22, "integrity": "sha512-trG0f6LY+g7CJCV8AN6O+cU5B13tONNDF9ZcnxDtUQQqvufNFDn3zcb/EIslXHwl1IjloZoVvOOcKtBaO9HlBw==", "signatures": [{"sig": "MEQCIFMhl6YhsahSD5C2cEgsiY15LydJfdLmgtbmy6wNg4ZYAiBWde1l+b9wA7UhVRSAq+znCyzkSNfKf+Tuuv5EKQq0dA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52474}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.4": {"name": "eslint-import-resolver-typescript", "version": "4.2.4", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.1"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "7b0b96dba608462bb080919b4caa692bdab4e6e3", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.4.tgz", "fileCount": 22, "integrity": "sha512-aXuJ7khvdh/n6UGQ6bk2Hqdk0kJdrkUCxRQy62QXB+aJ9xaCZnynFOMqhwGxOlWnt6k2fHwIPAOaOM5dBCmFZg==", "signatures": [{"sig": "MEUCIFOrQxzqGRupJzJSfHCIBngRz5gaBdwl8r27NYLyRr5gAiEAp5SVkOhv/y7GmUBWVLyMWbaOAGHNCshigqTqzQtvXCM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53301}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "4.2.5": {"name": "eslint-import-resolver-typescript", "version": "4.2.5", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "7936d5406ba805700422262c649953d9c7476106", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.5.tgz", "fileCount": 22, "integrity": "sha512-VtSNsVbyDlubDcx5Lb1K1Y8G4MxUuC9XVALX1z2EIXaLobCedvFPQ2XRemobQStn04G9MRi3iu1JFLKI4/8fig==", "signatures": [{"sig": "MEYCIQDCdkGs6qmYCCoaWQNe1X68+PMjXK1tVjWfKr/9SDH2nwIhAKT5w7v30adOYOixQiwiNNdzxUCKNVCNWm4zGB0XW5T4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53369}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "3.10.0": {"name": "eslint-import-resolver-typescript", "version": "3.10.0", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.2", "@nolyfill/is-core-module": "1.0.39"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "5bca4c579e17174e95bf67526b424d07b46c352e", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.10.0.tgz", "fileCount": 8, "integrity": "sha512-aV3/dVsT0/H9BtpNwbaqvl+0xGMRGzncLyhm793NFGvbwGGvzyAykqWZ8oZlZuGwuHkwJjhWJkG1cM3ynvd2pQ==", "signatures": [{"sig": "MEUCIQDIkW8v3YYJeJduIoe9A9J1u1VyjoSbaev9mJ0SMBYL2gIgLy+R7dGW1RFa210LRJVJIH2UJNrmCcMBB2DoVtzGG8k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 63743}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.2.6": {"name": "eslint-import-resolver-typescript", "version": "4.2.6", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "c95cc94875dba19088a6c186170e0932488b2293", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.6.tgz", "fileCount": 22, "integrity": "sha512-LWPT+Qsa7J4YTUz+FaLj1DKpqZGlxiVkg93eyZOh57fimMRLzUMEhB2MiLo3pog77cxL29t8FytzeMRM/zn+ug==", "signatures": [{"sig": "MEUCIG2p9ESzkAZ/ZCR63JR+sDwRUsIFmehVDOG/4IupslJQAiEA/YzMcReq5/MglKKTlCU6/e3eE7IU8IxSxFRK4A28xfc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 53437}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.2.7": {"name": "eslint-import-resolver-typescript", "version": "4.2.7", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "be504e858b7788b91f7f4a7a697f403b2533e75e", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.2.7.tgz", "fileCount": 23, "integrity": "sha512-9SaurN++KhzVgS0Ejldr0mtuCh3PCX3csvQhdF4FCHgSVoHofIkgvlMki+uVoQ1C2KVdBXNAlQOWSIiuOjYcdw==", "signatures": [{"sig": "MEUCIQCsuOUrwFRTHiNPN6R9nrAI9JBJtckqwzoEMAzDk8G08QIgF97ABSuZ5D28Xjip2Ap9bHFWoRhRHGKD0cdw2y5Fl1E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 53632}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.0": {"name": "eslint-import-resolver-typescript", "version": "4.3.0", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.2"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "97c02144c37d7b1a0ab4e6f489c17e14ed1075df", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.0.tgz", "fileCount": 23, "integrity": "sha512-C<PERSON>+mTqZOy2Jl3UNoHIX2HLvNDdmjefqTu7T9LB05r6uMRhFVHy0MDmY1U4FLlV+LqXd/2/7wUsXFtW8kkUzqfA==", "signatures": [{"sig": "MEYCIQDQK3kcX+XWLFUB5HtG6HmzR4mkXpZEss3gN9fnlz97jwIhAPmDghGDLaCCLw1A7/I6rVQrzL3EDDwsw8DuIsjsATn/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 54299}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.1": {"name": "eslint-import-resolver-typescript", "version": "4.3.1", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.3.3"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "6721c639716de3685363ddb284e2cec60cee60ee", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.1.tgz", "fileCount": 23, "integrity": "sha512-/dR9YMomeBlvfuvX5q0C3Y/2PHC9OCRdT2ijFwdfq/4Bq+4m5/lqstEp9k3P6ocha1pCbhoY9fkwVYLmOqR0VQ==", "signatures": [{"sig": "MEUCIQCJUuMDDpFpNUnf78pvRWyCdzjVYbIV/PoOjvzZEyRWXQIgIhd9KZWdMw6OqUX7LxgE9XeJL+4JKAejo9VW7JM8mxA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 54299}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.2": {"name": "eslint-import-resolver-typescript", "version": "4.3.2", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.12", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.4.1"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "1d2371be6d073bade177ee04a4548dbacdc334c0", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.2.tgz", "fileCount": 23, "integrity": "sha512-T2LqBXj87ndEC9t1LrDiPkzalSFzD4rrXr6BTzGdgMx1jdQM4T972guQvg7Ih+LNO51GURXI/qMHS5GF3h1ilw==", "signatures": [{"sig": "MEUCIEMRyTaYEa+ZhuxVxGlUiXPZ4/v/kxlwwr2D34hNQTGYAiEA577jzEYMdna+oB8LOBIjgu+c1Wvi7Tjd9Gu6haSu49o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 54296}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.3": {"name": "eslint-import-resolver-typescript", "version": "4.3.3", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.13", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.6.0"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "20bfa88bd17746a4511eae69ef5bee16d31d0fb7", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.3.tgz", "fileCount": 23, "integrity": "sha512-mBgGvAG+3NGx2yk8w/qiBDOrQNwNe0LfxNzimnj0B7lqElJUV12X+1wf81oERAKpPVl506z53Xi1sns4/pvdTg==", "signatures": [{"sig": "MEUCICbrDmhjbfjKRPaMnLH4Rk/7ZcOcBDziszDEx/aNcqpCAiEA0ylIcNrtnYF84wavGzE22i4yb+QGuikXeppENI3OXUQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 54387}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "3.10.1": {"name": "eslint-import-resolver-typescript", "version": "3.10.1", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.13", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.6.2", "@nolyfill/is-core-module": "1.0.39"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "23dac32efa86a88e2b8232eb244ac499ad636db2", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.10.1.tgz", "fileCount": 8, "integrity": "sha512-A1rHYb06zjMGAxdLSkN2fXPBwuSaQ0iO5M/hdyS0Ajj1VBaRp0sPD3dn1FhME3c/JluGFbwSxyCfqdSbtQLAHQ==", "signatures": [{"sig": "MEUCIQDbeSDxgpQZJC9OPsTgUPTR/oU/hyM8sYSRKeqdsN3guAIgTdDuvLrT7SC4EuFhNHo2HajxUpVJMtRLctSRFoqDpsc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 63913}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.4": {"name": "eslint-import-resolver-typescript", "version": "4.3.4", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.13", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.6.3"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "3d04161698925b5dc9c297966442c2761a319de4", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.4.tgz", "fileCount": 23, "integrity": "sha512-buzw5z5VtiQMysYLH9iW9BV04YyZebsw+gPi+c4FCjfS9i6COYOrEWw9t3m3wA9PFBfqcBCqWf32qrXLbwafDw==", "signatures": [{"sig": "MEQCIDZWRaAUTs0pUvTIgK1X3ttnTkvQf+0/KptyQ62DCSvkAiBc86ZvC0DFrKyM2p4c1SclDntKEU4ZVlqhp8x84YpUgg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 55329}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.3.5": {"name": "eslint-import-resolver-typescript", "version": "4.3.5", "dependencies": {"debug": "^4.4.0", "tinyglobby": "^0.2.13", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.6.3"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "81a7f1ab7f52d00980a2fe031e8d27aa095baf91", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.3.5.tgz", "fileCount": 23, "integrity": "sha512-QGwhLrwn/WGOsdrWvjhm9n8BvKN/Wr41SQERMV7DQ2hm9+Ozas39CyQUxum///l2G2vefQVr7VbIaCFS5h9g5g==", "signatures": [{"sig": "MEQCIEvYioYi09ZY6e1Ipgy8SL4c7PE+cWlVpWaF6YyatrUsAiAomNi2f/7VRDRfpwLdNExrTbphd/+YjCnyxBd5SHt10g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.3.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 55123}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.4.0": {"name": "eslint-import-resolver-typescript", "version": "4.4.0", "dependencies": {"debug": "^4.4.1", "tinyglobby": "^0.2.13", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.1", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.7.2", "eslint-import-context": "^0.1.3"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "b503ab8d621a6977fe7b16a02c3cf5ef2dcb0395", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.4.0.tgz", "fileCount": 23, "integrity": "sha512-wGgsNnIzv9Rm4UbjZ5ELHtyOMLpYPa/UcMhqtiRx6sL80ySmbc3D/E6zeHHU3JtpxCvaIafo+V53+2u68LIdGA==", "signatures": [{"sig": "MEUCIGlSCLJ70T7HDPgQdyOhYskMQS70N/bs3rd48Lkg6jZDAiEAqCMYX252SlYgi7PCP3kI4mZiBJX86cdXBai4v9qrBbw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58113}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.4.1": {"name": "eslint-import-resolver-typescript", "version": "4.4.1", "dependencies": {"debug": "^4.4.1", "tinyglobby": "^0.2.14", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.1", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.7.2", "eslint-import-context": "^0.1.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "3da41dcaa17ddfc520ad2d2d1e30b8ac4e137e8e", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.4.1.tgz", "fileCount": 23, "integrity": "sha512-KHQnjMAn/Hbs1AcMs2YfJTeNoWsaOoMRvJUKr77Y2dv7jNOaT8/IJYlvfN/ZIwTxUsv2B6amwv7u9bt2Vl9lZg==", "signatures": [{"sig": "MEUCICigh5Uv2+M2VaXkzyoVWFdBjaaTTgVOH4xb1/t87gnZAiEA2XC2FEP4ltxA+9WCeYToecQpOQC8JehXXVVg5Q0U0iw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 58113}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.4.2": {"name": "eslint-import-resolver-typescript", "version": "4.4.2", "dependencies": {"debug": "^4.4.1", "tinyglobby": "^0.2.14", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.1", "is-bun-module": "^2.0.0", "unrs-resolver": "^1.7.2", "eslint-import-context": "^0.1.5"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "7d29b4663e13540354fe8c094c854b1a512a8e4e", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.4.2.tgz", "fileCount": 23, "integrity": "sha512-GdSOy0PwLYpQCrmnEQujvA+X0NKrdnVCICEbZq1zlmjjD12NHOHCN9MYyrGFR9ydCs4wJwHEV9tts44ajSlGeA==", "signatures": [{"sig": "MEUCIDuRAziywuuAz1Y5gTPU87ilANhc109/KhXfSC8gCOfOAiEA9Ib4RGcpPvzzljO8cbzc6brIy/9j5bc+iccKQ6Rnt8o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.4.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59263}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.4.3": {"name": "eslint-import-resolver-typescript", "version": "4.4.3", "dependencies": {"debug": "^4.4.1", "tinyglobby": "^0.2.14", "get-tsconfig": "^4.10.1", "is-bun-module": "^2.0.0", "stable-hash-x": "^0.1.1", "unrs-resolver": "^1.7.11", "eslint-import-context": "^0.1.8"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"shasum": "7fdd06f8fd7cdb05656980e3fe9d2c246365b2a2", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.4.3.tgz", "fileCount": 23, "integrity": "sha512-elVDn1eWKFrWlzxlWl9xMt8LltjKl161Ix50JFC50tHXI5/TRP32SNEqlJ/bo/HV+g7Rou/tlPQU2AcRtIhrOg==", "signatures": [{"sig": "MEYCIQCVndM9NplxWZ43BVx3skgpaDBchSKH4ku92AM2JnAhoQIhAJ+23I7iEy+GVzakx8kKWzxxa87ECpMIRLzTapVB+QlR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.4.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59335}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}, "4.4.4": {"name": "eslint-import-resolver-typescript", "version": "4.4.4", "dependencies": {"debug": "^4.4.1", "eslint-import-context": "^0.1.8", "get-tsconfig": "^4.10.1", "is-bun-module": "^2.0.0", "stable-hash-x": "^0.2.0", "tinyglobby": "^0.2.14", "unrs-resolver": "^1.7.11"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "dist": {"integrity": "sha512-1iM2zeBvrYmUNTj2vSC/90JTHDth+dfOfiNKkxApWRsTJYNrc8rOdxxIf5vazX+BiAXTeOT0UvWpGI/7qIWQOw==", "shasum": "3e83a9c25f4a053fe20e1b07b47e04e8519a8720", "tarball": "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-4.4.4.tgz", "fileCount": 23, "unpackedSize": 59971, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-import-resolver-typescript@4.4.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBl2p4fao0+tEZTSUm3SeWuU3MJme0lV0y21Fl7WkUt5AiEA804EQLoE6Ui9JMlno0xxkV17Q6gUNMt2OlkOecsBRo4="}]}, "engines": {"node": "^16.17.0 || >=18.6.0"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}, "funding": "https://opencollective.com/eslint-import-resolver-typescript"}}, "modified": "2025-06-25T17:46:33.663Z"}