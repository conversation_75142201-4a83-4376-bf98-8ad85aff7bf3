{"name": "eslint-plugin-import-x", "dist-tags": {"latest": "4.16.1"}, "versions": {"0.0.0": {"name": "eslint-plugin-import-x", "version": "0.0.0", "dependencies": {"debug": "^3.2.7", "hasown": "^2.0.1", "semver": "^6.3.1", "is-glob": "^4.0.3", "doctrine": "^2.1.0", "minimatch": "^3.1.2", "object.values": "^1.1.7", "array-includes": "^3.1.7", "is-core-module": "^2.13.1", "object.groupby": "^1.0.2", "tsconfig-paths": "^3.15.0", "object.fromentries": "^2.0.7", "eslint-module-utils": "^2.8.0", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "eslint-import-resolver-node": "^0.3.9", "array.prototype.findlastindex": "^1.2.4"}, "devDependencies": {"nyc": "^11.9.0", "chai": "^4.3.10", "glob": "^7.2.3", "mocha": "^3.5.3", "redux": "^3.7.2", "sinon": "^2.4.1", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "rimraf": "^2.7.1", "babylon": "^6.18.0", "babel-cli": "^6.26.0", "cross-env": "^4.0.0", "jackspeak": "=2.1.1", "linklocal": "^2.8.2", "npm-which": "^3.0.1", "babel-core": "^6.26.3", "in-publish": "^2.0.1", "typescript": "^2.8.1 || ~3.9.5 || ~4.5.2", "babel-eslint": "=8.0.3 || ^8.2.6", "jsonc-parser": "=3.2.0", "babel-register": "^6.26.0", "lodash.isarray": "^4.0.0", "markdownlint-cli": "~0.35", "babel-preset-flow": "^6.23.0", "fs-copy-file-sync": "^1.1.1", "eslint-plugin-json": "^2.1.2", "babel-preset-airbnb": "^2.6.0", "eslint-module-utils": "file:./utils", "safe-publish-latest": "^2.0.0", "eslint-doc-generator": "^1.6.1", "eslint-plugin-import": "2.x", "babel-plugin-istanbul": "^4.1.6", "@test-scope/some-module": "file:./tests/files/symlinked-module", "typescript-eslint-parser": "^15 || ^20 || ^22", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "eslint-import-resolver-node": "file:./resolvers/node", "eslint-plugin-eslint-plugin": "^2.3.0", "babel-plugin-module-resolver": "^2.7.1", "eslint-import-resolver-webpack": "file:./resolvers/webpack", "@angular-eslint/template-parser": "^13.5.0", "eslint-import-resolver-typescript": "^1.0.2 || ^1.1.1", "eslint-import-test-order-redirect": "file:./tests/files/order-redirect", "@eslint/import-test-order-redirect-scoped": "file:./tests/files/order-redirect-scoped"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}, "directories": {"test": "tests"}, "dist": {"shasum": "a35435411f7040691dded652d3460ea7640bb43c", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.0.0.tgz", "fileCount": 115, "integrity": "sha512-dBWeXEL3VM33ZkbHFedvItarwpdG8sU0n5HyuA8rDX1jnmEmR0zM0J2aMkfQ2iecvn7O3DsKsthYZOpMJRVN1g==", "signatures": [{"sig": "MEQCIGCMHBetQHMh5LWnb2kyixxcKkJCq68Hv3iNRM/q0r7fAiBIOQVswOHFM7iPFUeBtIl9Bw3ggNNIqyBuYvOv+xj91g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1213542}, "engines": {"node": ">=4"}}, "0.1.0": {"name": "eslint-plugin-import-x", "version": "0.1.0", "dependencies": {"debug": "^3.2.7", "semver": "^6.3.1", "is-glob": "^4.0.3", "doctrine": "^2.1.0", "minimatch": "^3.1.2", "tsconfig-paths": "^3.15.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"chai": "^4.3.10", "glob": "^7.2.3", "jest": "^29.7.0", "redux": "^3.7.2", "sinon": "^2.4.1", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "rimraf": "^2.7.1", "svelte": "^4.2.12", "prettier": "^3.2.5", "@babel/cli": "^7.23.9", "typescript": "^5.4.2", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "jsonc-parser": "^3.2.1", "lodash.isarray": "^4.0.0", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^2.1.2", "@babel/preset-react": "^7.23.3", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "eslint-plugin-eslint-plugin": "^2.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^13.5.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "fc6b44dba1ec5bf2e7fc471101a9b397cc1a5cd0", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.1.0.tgz", "fileCount": 124, "integrity": "sha512-a0ljaLRfJRNZPjTTMu8/0kva0vDwnfLYGAIZFI0zd16QHUDC3w9Q35qshlAkcO4dRiuxONtXXJJcQhKsczmlaw==", "signatures": [{"sig": "MEQCIBzwDdgClKVIw2RP8z7s7CDG6fNRZUGYjveRSALQfhhyAiAkwkZvm9h6C4n1vUQL290GkdlHn6HP7L7DOAhuRRrvcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1243455}, "engines": {"node": ">=12"}}, "0.1.1": {"name": "eslint-plugin-import-x", "version": "0.1.1", "dependencies": {"debug": "^3.2.7", "semver": "^6.3.1", "is-glob": "^4.0.3", "doctrine": "^2.1.0", "minimatch": "^3.1.2", "tsconfig-paths": "^3.15.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"chai": "^4.3.10", "glob": "^7.2.3", "jest": "^29.7.0", "redux": "^3.7.2", "sinon": "^2.4.1", "escope": "^3.6.0", "eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "rimraf": "^2.7.1", "svelte": "^4.2.12", "prettier": "^3.2.5", "@babel/cli": "^7.23.9", "typescript": "^5.4.2", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "jsonc-parser": "^3.2.1", "lodash.isarray": "^4.0.0", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^2.1.2", "@babel/preset-react": "^7.23.3", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@typescript-eslint/parser": "^2.23.0 || ^3.3.0 || ^4.29.3 || ^5.10.0", "eslint-plugin-eslint-plugin": "^2.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^13.5.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "9739581ae422709bfaf701d25d35beeb8b2d70bc", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.1.1.tgz", "fileCount": 77, "integrity": "sha512-ioNowi7FvFmy48ctYJ2YoBjGWWH4E49fA/VauT07XVFaFxnTax1/wOVYbqN0WqZOMVHvBD7EXUKm9z9dDyJZ4w==", "signatures": [{"sig": "MEQCIB6VXytv/XEkmBAmLlcYyibnKw5I1UXXoQcOhy/T0y/2AiBQ9evNSt4l83WNnsAM6D8lKY/uyuiOF57Yj7Ah7g2auA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1134185}, "engines": {"node": ">=12"}}, "0.2.0": {"name": "eslint-plugin-import-x", "version": "0.2.0", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "prettier": "^3.2.5", "@babel/cli": "^7.23.9", "typescript": "^5.4.2", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@typescript-eslint/parser": "^5.62.0", "eslint-plugin-eslint-plugin": "^2.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.2.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "4afc8e018bcb839d580f89e447d53230f0bd2054", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.2.0.tgz", "fileCount": 77, "integrity": "sha512-4xGcyeQQptVJ6ZUW8tMY7CtykMA0CCAcu8hxPgN1yWBj3vfg7VxxiJFGY7jagUlCtDU/rTlpMfR2RfMWR7TkgA==", "signatures": [{"sig": "MEYCIQD75vjsPfGjSsr0W5/UekjDXvUWvHB2Gw7WABq4X4d1ywIhAPYV8Vfdj3tD3L3UJ0bHiA5M4oJYuygUT5MejXUFXg19", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1134331}, "engines": {"node": ">=12"}}, "0.3.0": {"name": "eslint-plugin-import-x", "version": "0.3.0", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "prettier": "^3.2.5", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.26", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "eslint-plugin-eslint-plugin": "^2.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.2.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "2b05660e7cf7d6147d867e05aa35805535d3e95a", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.3.0.tgz", "fileCount": 237, "integrity": "sha512-kWKxlaqDL+aP5IMDCQ98CgXO2Cw+hPHgHKYr3H9GBfCyzN4LoTjVZzwgE616giOf2+rSbdABOf9n2d9EY8QPxw==", "signatures": [{"sig": "MEQCIBYBQYzp4op9YXEVRcKw3v7s1nKG2jfKpFnp1TcePazyAiAkrSpLql/FyRiDq1xKfRRjx/K4o6OnTelARnm4W8rDMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 582824}, "engines": {"node": ">=12"}}, "0.3.1": {"name": "eslint-plugin-import-x", "version": "0.3.1", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "prettier": "^3.2.5", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.26", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^2.3.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.2.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "15c2cc3bcb89a6cb6520ec7ad2a1cc4e29c430f6", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.3.1.tgz", "fileCount": 237, "integrity": "sha512-VlMlsACLLJTaq74A6w+uwPs/CQm8Pes3jnc9NG1qjHmJtiUxbDw/VyOGxIn4IEjMY2snTXUMBDQlqcq6rluzsA==", "signatures": [{"sig": "MEUCIHVt2mAFmS5WQK4B47/kSe2tLRpjIsRVBGCTBIqKVnVHAiEA2vJ9dTikpaxEbqiE25PKM2elXtiwiSbkrSDwSpIdsxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 627313}, "engines": {"node": ">=12"}}, "0.4.0": {"name": "eslint-plugin-import-x", "version": "0.4.0", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "prettier": "^3.2.5", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.28", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "0d4927a6b3d093b0d61ccc92bf3bcfdb06b6ae2d", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.4.0.tgz", "fileCount": 234, "integrity": "sha512-rZw3CIY832LDPTMRWE8P0Tc0+OWlhIBR4+EBOy9dkkWbVJp78HYX3FO1iGxIZONReloYNl1FzjeppPPlrAnPcA==", "signatures": [{"sig": "MEUCICmZalIJiC7WcuSvWnuTvzQBJNGTzmYmyHSFHb9RRSifAiEAyEJ+8GDf1M0PQNJcuRuJpVvcW57K6NHRN2kXdXHN5YQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623422}, "engines": {"node": ">=16"}}, "0.4.1": {"name": "eslint-plugin-import-x", "version": "0.4.1", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "prettier": "^3.2.5", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.28", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8"}, "dist": {"shasum": "6ef879c639cd4a97df8800ee2d917da56469c4c7", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.4.1.tgz", "fileCount": 234, "integrity": "sha512-UqbL8DEewDKdCQZmSsm9lzdzyyDhWmWo//HQTnbLkNW7nIWukluuc6IaE5dAFQSa9mK/M8IHTywvOvMMaX25XQ==", "signatures": [{"sig": "MEYCIQCS15FcyYKyTbWI0Ov9j6GhRzCFYpL1NsgeXB0FiB2q2gIhANa5F4DGlst/qFTVioVSJRpS4mgusK0IA7W2ZR6n1L15", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624241}, "engines": {"node": ">=16"}}, "0.4.2": {"name": "eslint-plugin-import-x", "version": "0.4.2", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "eslint-compat-utils": "^0.5.0", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.8", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.28", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8 || ^9.0.0-0"}, "dist": {"shasum": "ef75360c1b589b2792b98eb339845f3611da4c40", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.4.2.tgz", "fileCount": 240, "integrity": "sha512-GVSoldpDMtBuw03tkocRt68fG+6qoZcgEdc2c2PkTIX/NeMYCaeHlCsYelMoDaRczNPg4lNOzJk1mXXTL8UPSw==", "signatures": [{"sig": "MEUCIH/Jzcj9wJijezwkKCiALHPmJ85c4gIr6gb5YOuP9vFNAiEAhH1RlQnkjsLFCMHGKT25Fgti9QK+q+CvevOV6RzXvDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 627532}, "engines": {"node": ">=16"}}, "0.4.3": {"name": "eslint-plugin-import-x", "version": "0.4.3", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "eslint-compat-utils": "^0.5.0", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.8", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.28", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8 || ^9.0.0-0"}, "dist": {"shasum": "aa496366c35ad007585a7b83c5552bd80e73417e", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.4.3.tgz", "fileCount": 243, "integrity": "sha512-5nfy/FjvhH16fu4p7/8uOUYEQ+yMqROhW1g3q4zYzfZHeGDt3Ckl2H1P5ihnXxBUeBW5Vx20gLe58SUUh2oEVQ==", "signatures": [{"sig": "MEQCIDIqNPNuxWen5qBTonTfoavBsUfmsNwld9fCtD1OABpAAiBLia620BH5rsWnUtqk3r7sQbC/cF0ZgHdWNM1pzReseA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 628220}, "engines": {"node": ">=16"}}, "0.4.4": {"name": "eslint-plugin-import-x", "version": "0.4.4", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "eslint-compat-utils": "^0.5.0", "@typescript-eslint/utils": "^5.62.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.8", "cross-env": "^7.0.3", "type-fest": "^4.12.0", "typescript": "~5.1.0", "@babel/core": "^7.24.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.28", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.5", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.0", "@babel/preset-flow": "^7.24.0", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.23.3", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.23.10", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^5.62.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.0", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@babel/plugin-proposal-decorators": "^7.24.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@typescript-eslint/typescript-estree": "^5.62.0", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.23.3"}, "peerDependencies": {"eslint": "^7.2.0 || ^8 || ^9.0.0-0"}, "dist": {"shasum": "f050e1455911c73097bcde0ef97605d5c22d6a50", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.4.4.tgz", "fileCount": 243, "integrity": "sha512-+6vns/GOAL0K5tzQ7ZescD2vFBz3cICZqT9R5CQ9h/bTA+Jkae8DuHT2gYhFb2K97kzsLnmPmKM51Iq9g6vTRA==", "signatures": [{"sig": "MEUCIFn0pELPSfgFzQkQFmQ4FNR52BXquWDJV8ewSyWtXuXuAiEA81LEYRevWIwo5kjhp12pRxqwQsqbaJK3TRoPHW55YuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 628087}, "engines": {"node": ">=16"}}, "0.5.0": {"name": "eslint-plugin-import-x", "version": "0.5.0", "dependencies": {"debug": "^4.3.4", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "e0b26584d1c166368d7fd9e338cb4edea8832443", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.5.0.tgz", "fileCount": 243, "integrity": "sha512-C7R8Z4IzxmsoOPMtSzwuOBW5FH6iRlxHR6iTks+MzVlrk3r3TUxokkWTx3ypdj9nGOEP+CG/5e6ebZzHbxgbbQ==", "signatures": [{"sig": "MEQCICvMaks2LLr+axh6CWWfcNIn/wazpEJPOeCDj7J0rgeNAiALuVELqNzLWcFD5SvJJUh9pcSsVZxUQSDIbHycVD7wCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632933}, "engines": {"node": ">=16"}}, "0.5.1": {"name": "eslint-plugin-import-x", "version": "0.5.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "0781096ee737ccfc0f9a028d286839bfa53b1080", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.5.1.tgz", "fileCount": 237, "integrity": "sha512-2JK8bbFOLes+gG6tgdnM8safCxMAj4u2wjX8X1BRFPfnY7Ct2hFYESoIcVwABX/DDcdpQFLGtKmzbNEWJZD9iQ==", "signatures": [{"sig": "MEYCIQCEe9eDROS1hP9ktqT1IThRYNzHMSyxry7heqqk8FjMwAIhAMcr9CiY1YvhAQqLMOGWsOV9bFsRsXb9lJ364+bILWjp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631245}, "engines": {"node": ">=16"}}, "0.5.2": {"name": "eslint-plugin-import-x", "version": "0.5.2", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "a2d23b8c3ec2a049772902ba4addc9f3ee549e50", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.5.2.tgz", "fileCount": 237, "integrity": "sha512-6f1YMmg3PdLwfiJDYnCRPfh67zJKbwbOKL99l6xGZDmIFkMht/4xyudafGEcDOmDlgp36e41W4RXDfOn7+pGRg==", "signatures": [{"sig": "MEQCIHZI40ruvwLVpkT8lk8puTtIVCUfPEjL9j8ib5TBpcYrAiBRSxnd5NGCSPtng0P2vASqRyGbFxayGkAxhzjFmRnVMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631317}, "engines": {"node": ">=16"}}, "0.5.3": {"name": "eslint-plugin-import-x", "version": "0.5.3", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "5965a567885521734c05c6832de93ece18da12ad", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-0.5.3.tgz", "fileCount": 237, "integrity": "sha512-hJ/wkMcsLQXAZL3+txXIDpbW5cqwdm1rLTqV4VRY03aIbzE3zWE7rPZKW6Gzf7xyl1u3V1iYC6tOG77d9NF4GQ==", "signatures": [{"sig": "MEUCIAQ4Nffdsvgl3b7PEYCU2A97bXQoFrCYn+2u6AAdWm+FAiEAjyHUpyljNWsGw0UQygZe91eTDHFf/1CdpRZUaRxSIyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 638087}, "engines": {"node": ">=16"}}, "3.0.0": {"name": "eslint-plugin-import-x", "version": "3.0.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "196d8f5643d6f4f4371b0c254a38c154e327255b", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-3.0.0.tgz", "fileCount": 240, "integrity": "sha512-zk3QklFELk7mrSlhP9C27NpKx86G5YtIEvDV1dIJRS3VOIm5tCHQCln2JkwbO5lpYOvyYSoro35PCAAVG9lY8w==", "signatures": [{"sig": "MEYCIQCmm/fjVrOkXg4y8rvFgph9nJJ+tvlDrwtZN+rCaMtdngIhAPGK+q93+DeMIZPkQ6UjNNPZS49UP+R1eNspJMaXtxN4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 639267}, "engines": {"node": ">=16"}}, "3.0.1": {"name": "eslint-plugin-import-x", "version": "3.0.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "@rtsao/scc": "^1.1.0", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "e65b383a6e6708d3d7961bd36f157d7f5b5bb51d", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-3.0.1.tgz", "fileCount": 243, "integrity": "sha512-jzQgJuE4ssxwNi0aMBkOL8whd4eHb0Z/uFWsk8uEoYB7xwTkAptSKojLzRswxgf/1bhH6QgcLjgabUBQqluBIg==", "signatures": [{"sig": "MEUCIQCGXOYddwmACGWZkU/WnUJ8VxoVlLN+xldFB54lNTqcagIgfz9GeIAe6YK/zoUybd89YVTosoFybw1BT8B0hy8Eb3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648114}, "engines": {"node": ">=16"}}, "3.1.0": {"name": "eslint-plugin-import-x", "version": "3.1.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.2", "semver": "^7.6.0", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^7.4.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^7.2.0 || ^8", "rimraf": "^5.0.5", "svelte": "^4.2.12", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.4.11", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "typescript": "^5.4.3", "@babel/core": "^7.24.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.7", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^8.56.6", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.23.7", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.24.3", "@babel/preset-flow": "^7.24.1", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.1", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.24.1", "eslint-doc-generator": "^1.7.0", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.1", "@typescript-eslint/parser": "^7.4.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.3.0", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-proposal-decorators": "^7.24.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.1"}, "peerDependencies": {"eslint": "^8.56.0 || ^9.0.0-0"}, "dist": {"shasum": "e1d132bde47c431b37f3b294d9ff813098375e7d", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-3.1.0.tgz", "fileCount": 240, "integrity": "sha512-/UbPA+bYY7nIxcjL3kpcDY3UNdoLHFhyBFzHox2M0ypcUoueTn6woZUUmzzi5et/dXChksasYYFeKE2wshOrhg==", "signatures": [{"sig": "MEUCIC5GsglrkqLqxQza8eFpPkJPZ37Hxwxdev2P4hh0l4ujAiEAuicVsIiaAhUMMcPLS4s0B6QxsUzJsIaV/PybfLKCDk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 642767}, "engines": {"node": ">=16"}}, "4.0.0": {"name": "eslint-plugin-import-x", "version": "4.0.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/typescript-estree": "^8.1.0"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "c367c27dfab458c92da59e3ff21768cad4e99a09", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.0.0.tgz", "fileCount": 240, "integrity": "sha512-5bW<PERSON>+2p3DKlpLSP830cAUmRUoYEnnvuBmSOSlURffEUuXL68uQUX0v2JpoXxyoDRIQWApzbqhnFeHA0XoQWosA==", "signatures": [{"sig": "MEUCIG8/uPDEEnSdVbKlvHwaRxJvAFGCKpJUMJ2TM3i5pIgeAiEA0pB9DcUW2e9FhkebS/3W4KB4T/CIgm7uyb+0RamSfD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 643475}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.1.0": {"name": "eslint-plugin-import-x", "version": "4.1.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/typescript-estree": "^8.1.0"}, "devDependencies": {"jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "4e7689c32685291d58f08cdd0bffa2dc1c6c9470", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.1.0.tgz", "fileCount": 267, "integrity": "sha512-1BYJU0C5NBJLY4qukmwDbFrf2w8fLGEU9zZV3viWa7gNnbn4o4meQy5O4LVXn56eFh9Y4fQxu3udhIqQuVITvw==", "signatures": [{"sig": "MEQCICgQD8TbjUnLJG9flnxNaof5ZiE7sWKF9uFiH34Gsa1CAiAQ2CFfSgJNqs0AgaiYEl8caX1BUNsTisDRxu6umgP20A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 674640}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.1.1": {"name": "eslint-plugin-import-x", "version": "4.1.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/typescript-estree": "^8.1.0"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "715efe257eddeb5986c68cda73908d019c954280", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.1.1.tgz", "fileCount": 267, "integrity": "sha512-dBEM8fACIFNt4H7GoOaRmnH6evJW6JSTJTYYgmRd3vI4geBTjgDM/JyUDKUwIw0HDSyI+u7Vs3vFRXUo/BOAtA==", "signatures": [{"sig": "MEQCIG8MUl6FPhK8nH9E1hlAtJNApn+yeaL6gi10Hjubo3U4AiB+QImXiikVZy2Y7tFzYzR5Wiv1t3m6KtaATyqWH4AOkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 675086}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.2.0": {"name": "eslint-plugin-import-x", "version": "4.2.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "8b4da3a22a55baeb282ce3a64e49b2cd343f6f23", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.2.0.tgz", "fileCount": 267, "integrity": "sha512-kEPB9oeuKSZ8U2LfH6DDoov5V4gTid5JHny9P0JyvKmB4LZNG8kGdqJyCq46QRimbp8FKTlOtsSIO5hdhoZS8A==", "signatures": [{"sig": "MEUCIFKHbUEgceQ6qI2OmwyLtBREK+wZO20W2o9KuRtPHzBNAiEAzXXE+r6fm5NFNiEU4ckzc0SxFDaE4jEBppDUcv79zTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 676714}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.2.1": {"name": "eslint-plugin-import-x", "version": "4.2.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "575f311d51d960f46609dfa3fea9f027cd0cf60b", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.2.1.tgz", "fileCount": 267, "integrity": "sha512-WWi2GedccIJa0zXxx3WDnTgouGQTtdYK1nhXMwywbqqAgB0Ov+p1pYBsWh3VaB0bvBOwLse6OfVII7jZD9xo5Q==", "signatures": [{"sig": "MEUCIQDstINlGSEYXRVz/5NHKfgqgBYgSJuFuXqWTxuz8bidjgIgdZdKvk+5RSzPx/2uM7foMKMYUhMr8r3YPNghzdB3sQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 679234}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.3.0": {"name": "eslint-plugin-import-x", "version": "4.3.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "1b59ca3bda6a34d2eb0c09196ccd1f905fc30861", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.3.0.tgz", "fileCount": 267, "integrity": "sha512-PxGzP7gAjF2DLeRnQtbYkkgZDg1intFyYr/XS1LgTYXUDrSXMHGkXx8++6i2eDv2jMs0jfeO6G6ykyeWxiFX7w==", "signatures": [{"sig": "MEYCIQCD7VD/oCydm66SexDzIwFWe6bbbcFsPvevnM4VqschCQIhAOLqAwrtGw7+WOEw0BfuXc6MdNKEhp9AtXhLniQ/ezWr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 682842}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.3.1": {"name": "eslint-plugin-import-x", "version": "4.3.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "098647388fc93d5e4e2fc449869c93dd6b6e47d3", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.3.1.tgz", "fileCount": 267, "integrity": "sha512-5TriWkXulDl486XnYYRgsL+VQoS/7mhN/2ci02iLCuL7gdhbiWxnsuL/NTcaKY9fpMgsMFjWZBtIGW7pb+RX0g==", "signatures": [{"sig": "MEUCID5QY1fEfy7/hSoHxTsNbwTYDsTPbuGRRIkkeuV1F0N4AiEA5A/V/eFz6bL0wrxhIAIXr2lwDAKsKANb14GC44RmLJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 683153}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.4.0": {"name": "eslint-plugin-import-x", "version": "4.4.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "23bef5ed0c2bed302045aa5b8f004758d76de346", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.4.0.tgz", "fileCount": 267, "integrity": "sha512-me58aWTjdkPtgmOzPe+uP0bebpN5etH4bJRnYzy85Rn9g/3QyASg6kTCqdwNzyaJRqMI2ii2o8s01P2LZpREHg==", "signatures": [{"sig": "MEQCICbjsHk/WLKhImdA37Nt2Y0aDW0w4C5+IbMO2tZsNO+NAiAosAFhdMf94LaOkUt8dS3fOQSyi0iLGe8tbJFVRw1atQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 684227}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.4.2": {"name": "eslint-plugin-import-x", "version": "4.4.2", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.9.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.8.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.0", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.0", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.1.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.1.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "2fef264059e2c4a25d149cf0323fe9205427a74c", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.4.2.tgz", "fileCount": 270, "integrity": "sha512-mDRXPSLQ0UQZQw91QdG4/qZT6hgeW2MJTczAbgPseUZuPEtIjjdPOolXroRkulnOn3fzj6gNgvk+wchMJiHElg==", "signatures": [{"sig": "MEUCIQCDZ3BdRzAsTppRYIqyhdZWFjBgT6keJlT12nRH2Xtm2wIgf4SQDEpOsrfnRTThQbdLM9QVgScsHYXVimYG8VZoE7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686392}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.4.3": {"name": "eslint-plugin-import-x", "version": "4.4.3", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.15.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.15.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.1", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.15.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.15.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.15.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "eeb25e6742b834cd12ba1fbd63127624213258b5", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.4.3.tgz", "fileCount": 270, "integrity": "sha512-QBprHvhLsfDhP++2T1NnjsOUt6bLDX3NMHaYwAB1FD3xmYTkdFH+HS1OamGhz28jLkRyIZa6UNAzTxbHnJwz5w==", "signatures": [{"sig": "MEUCIQDL+nTpohAhV9gNqZlGSOu2vumlAHEwSamLS4m6JMZ6MwIgS+ww9k2QeCuTDVHTfYvZ1Z50Aiyb5Dgw/5QONxk1ngs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686837}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.5.0": {"name": "eslint-plugin-import-x", "version": "4.5.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/scope-manager": "^8.1.0"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.15.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.15.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.1", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "@types/doctrine": "^0.0.9", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.15.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.15.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.15.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "a2e65113513c4ee44bec13ed600389558b041cf0", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.5.0.tgz", "fileCount": 273, "integrity": "sha512-l0OTfnPF8RwmSXfjT75N8d6ZYLVrVYWpaGlgvVkVqFERCI5SyBfDP7QEMr3kt0zWi2sOa9EQ47clbdFsHkF83Q==", "signatures": [{"sig": "MEQCIEkmk+uS9yLlPD3E9uShpQj7I2lr0nAFvFXB+RJuPUTXAiBIpeEzDDWWbx4g5VAsnp7tbXRQv5ryOrtr8TNKMK1jdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 695013}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.5.1": {"name": "eslint-plugin-import-x", "version": "4.5.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/scope-manager": "^8.1.0"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.15.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.15.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.1", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.16.0", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.15.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.15.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.15.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "07ea3e6053ca027d8c4568c3073f53c77020d6bf", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.5.1.tgz", "fileCount": 273, "integrity": "sha512-Wyut9jDeHdfZSebiWRmmOYDBov33M0ZZ3x9J/lD1v4M3nBgMNC02XH6Kq271pMxJWqctVRCjA+X5AQJZ2FezoQ==", "signatures": [{"sig": "MEYCIQDxjS9QrJyohQA63bqG9WChRoqJW+RSZgyH9/zHUxD6+QIhAL1uAphq7rt51UUA/M7k6zCbQwJ7NxPe7b4RDHzHVVIK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 695070}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.6.0": {"name": "eslint-plugin-import-x", "version": "4.6.0", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/scope-manager": "^8.1.0"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.15.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.15.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.1", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "enhanced-resolve": "^5.17.1", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.15.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.15.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.15.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "ff8f8401f75302875a32e64e1a1911f664a07b75", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.6.0.tgz", "fileCount": 276, "integrity": "sha512-hDdeNZWnu5Kv/AUk0rf12Ub1yjLQTPvj38Ar4oipYuPo5gy2gonLcF0mCuOMlMgjT8+8A1KYug/IsxuMi8tSMQ==", "signatures": [{"sig": "MEUCIGgQhNOtJi+AphXtGjN2f7x+pBD1lgwPCITXTwifQvH5AiEAtUHItmmE9d5RE3f6LPNINyLVPcdJuAo8yuql9VtI724=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 699001}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.6.1": {"name": "eslint-plugin-import-x", "version": "4.6.1", "dependencies": {"debug": "^4.3.4", "tslib": "^2.6.3", "semver": "^7.6.3", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3", "stable-hash": "^0.0.4", "get-tsconfig": "^4.7.3", "@types/doctrine": "^0.0.9", "enhanced-resolve": "^5.17.1", "@typescript-eslint/utils": "^8.1.0", "eslint-import-resolver-node": "^0.3.9", "@typescript-eslint/scope-manager": "^8.1.0"}, "devDependencies": {"zod": "^3.23.8", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.15.0", "rimraf": "^5.0.10", "svelte": "^4.2.12", "eslint9": "npm:eslint@^9.15.0", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@swc/core": "^1.7.6", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.14.0", "eslint8.56": "npm:eslint@^8.56.0", "typescript": "^5.5.4", "@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "@swc/helpers": "^0.5.12", "@types/debug": "^4.1.12", "npm-run-all2": "^6.1.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.23.1", "@1stg/tsconfig": "^2.3.3", "@swc-node/jest": "^1.8.12", "@types/eslint9": "npm:@types/eslint@^9.6.1", "@types/is-glob": "^4.0.4", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.1", "eslint-plugin-n": "^16.6.2", "@types/klaw-sync": "^6.0.5", "@babel/preset-env": "^7.25.3", "@types/eslint8.56": "npm:@types/eslint@^8.56.11", "@babel/preset-flow": "^7.24.7", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-json": "^3.1.0", "@babel/preset-react": "^7.24.7", "@unts/patch-package": "^8.0.0", "@babel/eslint-parser": "^7.25.1", "eslint-doc-generator": "^1.7.1", "@1stg/prettier-config": "^4.0.1", "eslint-plugin-unicorn": "^56.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.1.3", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.24.7", "@typescript-eslint/parser": "^8.15.0", "@total-typescript/ts-reset": "^0.5.1", "eslint-plugin-eslint-plugin": "^5.4.1", "@changesets/changelog-github": "^0.5.0", "@typescript-eslint/rule-tester": "^8.15.0", "eslint-import-resolver-webpack": "^0.13.8", "@angular-eslint/template-parser": "^17.5.2", "@typescript-eslint/eslint-plugin": "^8.15.0", "@babel/plugin-proposal-decorators": "^7.24.7", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.24.7"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "2fdb9a25addd92247f5d9b198bfa654eeaea2f52", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.6.1.tgz", "fileCount": 276, "integrity": "sha512-wluSUifMIb7UfwWXqx7Yx0lE/SGCcGXECLx/9bCmbY2nneLwvAZ4vkd1IXDjPKFvdcdUgr1BaRnaRpx3k2+Pfw==", "signatures": [{"sig": "MEYCIQDs4GEt40mqFtXp2QiYWKSL9uCOnm7LOYcp/I6pW7eP0AIhAMVi9THyDC45x6a0wXYxcUXkKfeh0GKuLJW0dIU2fkvN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 699168}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.7.0": {"name": "eslint-plugin-import-x", "version": "4.7.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "oxc-resolver": "^5.0.0", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "devDependencies": {"zod": "^3.24.2", "jest": "^29.7.0", "redux": "^5.0.1", "escope": "^4.0.0", "eslint": "^9.22.0", "rimraf": "^6.0.1", "eslint9": "npm:eslint@^9.22.0", "ts-node": "^10.9.2", "prettier": "^3.5.3", "@swc/core": "^1.11.9", "cross-env": "^7.0.3", "klaw-sync": "^6.0.0", "type-fest": "^4.37.0", "eslint8.56": "npm:eslint@~8.56.0", "typescript": "^5.8.2", "@babel/core": "^7.26.10", "@types/jest": "^29.5.14", "@types/node": "^20.17.24", "lint-staged": "^15.5.0", "@swc/helpers": "^0.5.15", "@types/debug": "^4.1.12", "npm-run-all2": "^7.0.2", "@types/eslint": "^9.6.1", "hermes-eslint": "^0.26.0", "@1stg/tsconfig": "^3.0.0", "@swc-node/jest": "^1.8.13", "@types/is-glob": "^4.0.4", "@babel/register": "^7.25.9", "@changesets/cli": "^2.28.1", "@commitlint/cli": "^19.8.0", "eslint-plugin-n": "^17.16.2", "path-serializer": "^0.3.4", "@types/klaw-sync": "^6.0.5", "simple-git-hooks": "^2.11.1", "@1stg/lint-staged": "^4.0.4", "@babel/preset-env": "^7.26.9", "@types/eslint8.56": "npm:@types/eslint@~8.56.0", "eslint-plugin-mdx": "^3.2.0", "eslint-plugin-yml": "^1.17.0", "@babel/preset-flow": "^7.25.9", "@types/json-schema": "^7.0.15", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-json": "^4.0.1", "@1stg/remark-preset": "^3.0.0", "@babel/preset-react": "^7.26.3", "@unts/patch-package": "^8.1.1", "@babel/eslint-parser": "^7.26.10", "eslint-doc-generator": "^2.1.1", "@1stg/prettier-config": "^4.0.4", "eslint-plugin-unicorn": "^56.0.1", "@1stg/simple-git-hooks": "^0.2.4", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import-x": "link:.", "eslint-plugin-prettier": "^5.2.3", "yarn-berry-deduplicate": "^6.1.1", "@1stg/commitlint-config": "^5.0.0", "@test-scope/some-module": "link:./test/fixtures/symlinked-module", "@babel/preset-typescript": "^7.26.0", "@typescript-eslint/parser": "^8.26.1", "@total-typescript/ts-reset": "^0.6.1", "eslint-plugin-eslint-plugin": "^6.4.0", "@changesets/changelog-github": "^0.5.1", "@typescript-eslint/rule-tester": "^8.26.1", "eslint-import-resolver-webpack": "^0.13.10", "@angular-eslint/template-parser": "^19.2.1", "@typescript-eslint/eslint-plugin": "^8.26.1", "@babel/plugin-proposal-decorators": "^7.25.9", "eslint-import-resolver-typescript": "^3.8.7", "eslint-import-test-order-redirect": "link:./test/fixtures/order-redirect", "@eslint/import-test-order-redirect-scoped": "link:./test/fixtures/order-redirect-scoped", "@babel/plugin-proposal-export-default-from": "^7.25.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "2d1af3c2a44fcc9a5ba6942efa073233d532bf63", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.7.0.tgz", "fileCount": 276, "integrity": "sha512-LHxq8V6SJ99hSFYAexxUKk3gVsjb8fuNRGsbMinwlJGvcuREP9SVzCCNKJ3POdDowEHdExy/bPN6YfjraueIXA==", "signatures": [{"sig": "MEQCIFXRbiesoPBBJs03H36dSK2/H6HwwosCgmDY1dxQHfccAiBGSyA4K/ZDMslQAu8tCxxLxS2IRFxiJlLu7HU6fnq9Ag==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 698203}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.7.1": {"name": "eslint-plugin-import-x", "version": "4.7.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.1.0", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "9484b6b2db50373b727ee1183824c298d130e362", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.7.1.tgz", "fileCount": 276, "integrity": "sha512-Gaf0YL05qYNk6MRFujSNGtwGl5ExQE4P8aREBELpYYjA37guFpv0AR6B+Y226KgfkRn0iP4FlQIqG4JhfN6k3A==", "signatures": [{"sig": "MEUCIQDC/Ck/CGL95ce/TaFxeo3P01QkJk/VUT4XAGxMXA5MigIgYDu89tngqCx7FLAQAF0zy/yn/mAjuS7c74Om7iZu8RI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 694847}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.7.2": {"name": "eslint-plugin-import-x", "version": "4.7.2", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.1.0", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "3296f93106c38f98de374bf472ea96349ae56350", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.7.2.tgz", "fileCount": 276, "integrity": "sha512-+GpGWKbQMK3izrwU4XoRGdAJHwvxuboiNusqU25nNXlRsmnxj8B5niQRuFK1aHEvcbIKE6D9ZfwjsLmBQbnJmw==", "signatures": [{"sig": "MEQCIBMKdJCEhs0dFRUPptZK4eOdXayumY1gkFgESTLmKqJDAiAkcIviR1D2K+2Kjg6tkDfSLsTrZPMWwZvMW+lHrARiwQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 695275}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.8.0": {"name": "eslint-plugin-import-x", "version": "4.8.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "doctrine": "^3.0.0", "picomatch": "^4.0.2", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.1.0", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "dea2175ff472bd78d88abc53b653e16baa5e0366", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.8.0.tgz", "fileCount": 279, "integrity": "sha512-rCZftKo2QvFfV62MomJuIog74NrsfdEriJZxcNPEnjwrHpfHge6ZfUe+Pp2f4pqZe+ArtH8RapwqxLc1O+KJYw==", "signatures": [{"sig": "MEUCIGHRxwKK2B0bPIYMUMJf2G+a4apaQZ2Il3bvbQVX9+RYAiEAjOeDXZ3pyEnbodHHJiymbIAGE2EW1LxwjI+r2wsA8Nc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 698149}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.8.1": {"name": "eslint-plugin-import-x", "version": "4.8.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "doctrine": "^3.0.0", "picomatch": "^4.0.2", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.2.1", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "d65c5794ed9b2df2dab227c89ca33ff01aa4ad59", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.8.1.tgz", "fileCount": 279, "integrity": "sha512-Jyr0mGWYia2gRl4XvbMVZ4OvpJe3+Gu/X0c4zHhHItjsHb4gkHkSPUPHWxZPyzWXnsClAZu41LMJDY4lfJnP3g==", "signatures": [{"sig": "MEUCIHUkej46OZG19CWr/0pxuZVhJc0EiolSLne52L+SQRMJAiEArN7d+SJy7vf44n767UXho0PNjXiQAuxSB5uTJ+nbTyA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 698149}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.9.0": {"name": "eslint-plugin-import-x", "version": "4.9.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "doctrine": "^3.0.0", "picomatch": "^4.0.2", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.2.1", "@typescript-eslint/utils": "^8.26.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "50983dc52903bba525e7e970e438a214b97e8783", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.9.0.tgz", "fileCount": 279, "integrity": "sha512-qdrsei0heLV8z9QpY2/PHF/r/3fF15w3JeVXqWlLzPMiiwYx0VAwIjxN6SzdaPVuGeIMAbQHHS1Wwdn1/bsCgw==", "signatures": [{"sig": "MEUCIDX8i+J4Fyk/PJXIS8l1/Ue7oQYu9GkQxdOyUxb7PJHLAiEAoShIfWlCJ7x4Enh02MAfQat9bac10sn+X2uLKwDpJD0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 737592}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.9.1": {"name": "eslint-plugin-import-x", "version": "4.9.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.2.2", "@typescript-eslint/utils": "^8.27.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "c13b37e662111dc1a0e6ab8c4f4ee72b955d15d1", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.9.1.tgz", "fileCount": 276, "integrity": "sha512-YJ9W12tfDBBYVUUI5FVls6ZrzbVmfrHcQkjeHrG6I7QxWAlIbueRD+G4zPTg1FwlBouunTYm9dhJMVJZdj9wwQ==", "signatures": [{"sig": "MEUCIQC1Lv3Z3HeXUJWci0EGga4a2kVS6Gf1OTA49afaBaUhPgIgH51S56fvW6wdoHQfe8pNMvFCaVVupao8fLnqz1fqk7E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 734664}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.9.2": {"name": "eslint-plugin-import-x", "version": "4.9.2", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "@types/doctrine": "^0.0.9", "rspack-resolver": "^1.2.2", "@typescript-eslint/utils": "^8.27.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "b0012486943cc7b5327244c50ffb00eb406f48c4", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.9.2.tgz", "fileCount": 276, "integrity": "sha512-wFuy/kJRziBoJBr/Nw3H0TIUAqOwqeUXeeTtKhffDuQLNzQTEDOWTOMJRVwkExErNJ8HYqpAM4rSzsCM4vfmxA==", "signatures": [{"sig": "MEUCIEPJ+43kUaGXl4QriXtFDuyDcz17uiHFSnvvIyj9Hj6KAiEAtaDMn5mGM22NfWqWUlOX01rg1opirD0hn5l6nsE75y0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 734487}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.9.3": {"name": "eslint-plugin-import-x", "version": "4.9.3", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.3.1", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.28.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "29149ddd02aaccb010c9682532bd3c0080843dab", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.9.3.tgz", "fileCount": 276, "integrity": "sha512-NrPUarxpFzGpQVXdVWkGttDD8WIxBuM/dRNw5kKFxrlGdjAJ3l8ma0LK5hsK5Qp79GBGM+HY1zYVbHqateTklA==", "signatures": [{"sig": "MEYCIQCG+jZYzkh35KZJjX2O5bRLEWqPsBr+dkNwA7cKm2RWWQIhAJfwLc1wNMKdK8D7ghjtA4X7fAmMsZIFcwcrn9+Hrq6P", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 734860}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.9.4": {"name": "eslint-plugin-import-x", "version": "4.9.4", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.3.3", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.28.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "f77122bcb964ea7df449693796ec69cb3ef5edfb", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.9.4.tgz", "fileCount": 276, "integrity": "sha512-IPWbN0KBgBCpAiSlUcS17zc1eqMzRlYz15AzsFrw2Qfqt+e0IupxYbvYD96bGLKVlNdkNwa4ggv1skztpaZR/g==", "signatures": [{"sig": "MEQCICOKPH3ByxRQzKW5eDsdmtnxW7TRAXKJhLGrbCgBbrLsAiAMPc5a4iqccIg9YbBDeqsndoQ1Qt8TE0BCiroa5MAyGw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.9.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 737427}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.10.0": {"name": "eslint-plugin-import-x", "version": "4.10.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.0", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.3.3", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.28.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "ec73c3cefb554ab0d83a0dad47700d56c349184a", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.0.tgz", "fileCount": 281, "integrity": "sha512-5ej+0WILhX3D6wkcdsyYmPp10SUIK6fmuZ6KS8nf9MD8CJ6/S/3Dl7m21g+MLeaTMsvcEXo3JunNAbgHwXxs/g==", "signatures": [{"sig": "MEUCIC2QDEbTqmiSwRIlDmbqZXJjtcaXZHMnAr6CNrLzu/zAAiEA71kt1J4jDD6uRlEV+plpbc3m7ZUzjAPT1B+n/yYvvkw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040742}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.10.1": {"name": "eslint-plugin-import-x", "version": "4.10.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.0", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.3.3", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.29.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "93a83270f940a4273685c9f5ac2d48eb9543f976", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.1.tgz", "fileCount": 281, "integrity": "sha512-enOdwV6uQOVS0MRfQwWEYqDnyAjLy5q1221+/A+EC7h43jjNOxX7kQ/qbnDqbAOL3N3d+u//iXrYO/70AbdAUg==", "signatures": [{"sig": "MEUCIQDPL6yhorwNnsQoVhCs/qp7i4R4ha+UzXWiZV2pH6QGogIgUYkbTpMXmislVuCzabrLXRsXkNbFhC9sAbDg8XjaKJQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040739}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.10.2": {"name": "eslint-plugin-import-x", "version": "4.10.2", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.4.1", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.29.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "41bee5d2c69776c79221dd09fa56bd2006ac31d4", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.2.tgz", "fileCount": 281, "integrity": "sha512-jO3Y6+zBUyTX5MVbbLSzoz6fe65t+WEBaXStRLM4EBhZWbuSwAH3cLwARtM0Yp4zRtZGp9sL2zzK7G9JkHR8LA==", "signatures": [{"sig": "MEUCIQDJkUsDAWjoTAmbCeOCe1lIz4NdI4EbWb3OnmzvFxgcdAIgdXDl8kIC7dfSz7SdJVKPQ9ylh9a2psueUc36z20FXCo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040739}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "4.10.3": {"name": "eslint-plugin-import-x", "version": "4.10.3", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.2", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.5.0", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.29.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "a1bc0fca513afb4508a012c453839a6fb6d74a2f", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.3.tgz", "fileCount": 281, "integrity": "sha512-Q7yx76tlOfzLGtXDt/sppdYrGNcbr9CrRXqJXTNIpM/7hfX9lRp1noE+PkYgN+xvW47TEeGZ0pgy29hlX9Y7UQ==", "signatures": [{"sig": "MEQCICVARF0ALOEKLFaoQbUK/RVLKL5EmmlEk6+N/1J3Vpj5AiBvf0FXsSp5qMq62Kvw7wcDxnf2KRIOwy0lmf8DidBlCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040758}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.10.4": {"name": "eslint-plugin-import-x", "version": "4.10.4", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.3", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.5.0", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.30.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "23209cea2ebc99b36089529e3cea5ee63087f114", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.4.tgz", "fileCount": 281, "integrity": "sha512-UNjPJmwmFoIHRKV8dB3C8EKyF8TTa8fghoA0tGixSB5H/PFZXkQ/UEafpTCckjHury+ykWjVwdkw+5YpomGuaA==", "signatures": [{"sig": "MEUCIDG+R2YmEId1wHBoHV1G50qW/ZBkaq3DWCmF+/57i2oJAiEAujCj7G7UnsQuNXiTYaqdRiYw1dkua8GPEUi3+VqlUfI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040807}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.10.5": {"name": "eslint-plugin-import-x", "version": "4.10.5", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.4", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.5.0", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.30.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "5189b21efff32ee93a14637b775b2fa30a92311b", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.5.tgz", "fileCount": 281, "integrity": "sha512-cmteCl8P5q1lkuL/4qqQw1uvnQHytpv2fjHFZ2UIqSfkM0RwWm/KLgasXKIqDRjgMnmUJTeyP8+9hDpJJuiZgg==", "signatures": [{"sig": "MEUCIDUR1AEEx/5/VcsHiDe5lg50HGCsKeU6A8l3PET/IqZKAiEAgm/BJqW7OxBrH4z3inqLeIRUgosONhVCIYkocFqwwR8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040955}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.10.6": {"name": "eslint-plugin-import-x", "version": "4.10.6", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "doctrine": "^3.0.0", "minimatch": "^9.0.3 || ^10.0.1", "@pkgr/core": "^0.2.4", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.6.0", "@types/doctrine": "^0.0.9", "@typescript-eslint/utils": "^8.30.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "39b5ff478b3d1d2f2aab3ed93a2f0bda8ab073bd", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.10.6.tgz", "fileCount": 281, "integrity": "sha512-sWIaoezWK7kuPA7u29ULsO8WzlYYC8uivaipsazyHiZDykjNsuPtwRsYZIK2luqc5wppwXOop8iFdW7xffo/Xw==", "signatures": [{"sig": "MEYCIQCM5LtT7Ne90PWmDpkmv7bT8EYi3leAg7s+EOq3xfezlgIhAJg4/v3g9APvg27u5f9XaTmEJw7ozkzg3PNxk3V0eyns", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.10.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1040959}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.11.0": {"name": "eslint-plugin-import-x", "version": "4.11.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "3cea18cb94d9dc12ca5d5529a9de591bd090f407", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.11.0.tgz", "fileCount": 284, "integrity": "sha512-NAaYY49342gj09QGvwnFFl5KcD5aLzjAz97Lo+upnN8MzjEGSIlmL5sxCYGqtIeMjw8fSRDFZIp2xjRLT+yl4Q==", "signatures": [{"sig": "MEUCIQDplSqfyFZGcrqsf/PPmAEsE0V965TIptDN8G6HKANedwIgJ7bANJlSLzsK7M0+ICtwNYyNSJETs80BQde66xffrp0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.11.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1043694}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.11.1": {"name": "eslint-plugin-import-x", "version": "4.11.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "a5a6a2dfd864988b44d2b9bc612d3dde26fd4d00", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.11.1.tgz", "fileCount": 284, "integrity": "sha512-CiqREASJRnhwCB0NujkTdo4jU+cJAnhQrd4aCnWC1o+rYWIWakVbyuzVbnCriUUSLAnn5CoJ2ob36TEgNzejBQ==", "signatures": [{"sig": "MEQCICCGi1zqjbiECS0aqITWSA85i31Bu2B+2uXJzibtAk0AAiBmTlpdt4GAr3iyPhJBFzY2JrB4zS6P01tYYi9V+DxJ5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.11.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1044018}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.12.0": {"name": "eslint-plugin-import-x", "version": "4.12.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "fab18164088fd7b88ed20b94523d180c204d45d8", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.12.0.tgz", "fileCount": 287, "integrity": "sha512-fTo5ttNTo+/fxPinp19hi9SMg5B9cUp222fgyNLfhezsYBa/aY94JVAhalyyxwIImP6paK5SccZP6rWs37qCZA==", "signatures": [{"sig": "MEQCIAinsrsLQ5k8zOY5eWj/dJXA/VERIUSk3O9+ijHg1z0UAiBbiOPZ1u1azHSt8ivTtaq6xCPbHfpsVXsd80P1x9tqHg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1059759}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.12.1": {"name": "eslint-plugin-import-x", "version": "4.12.1", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "c3913b76684c4569951ebe820baa1f363f937d0f", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.12.1.tgz", "fileCount": 287, "integrity": "sha512-k+lAJb/TcBDOrFL+8aToXwcwTc0H40MASN2eNHwJj9VWUwYCp3QJTLTaUmvn92q9SxIFuC4JthdQ0VxtdfFKSg==", "signatures": [{"sig": "MEUCIDCDydURKsV1vbTuJduAJ0TZa0d39X5AMK23gEnO1rLgAiEA29arJGFBjFDk1lzqGumTPdWTuLt0YdVFFSYE5BAmzPA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.12.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1064753}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.12.2": {"name": "eslint-plugin-import-x", "version": "4.12.2", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "get-tsconfig": "^4.10.0", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "cd1b8186345731230e9a3d981a91399bc1cc02f2", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.12.2.tgz", "fileCount": 287, "integrity": "sha512-0jVUgJQipbs0yUfLe7LwYD6p8rIGqCysWZdyJFgkPzDyJgiKpuCaXlywKUAWgJ6u1nLpfrdt21B60OUkupyBrQ==", "signatures": [{"sig": "MEYCIQC357A0pDn3befNb2s2OrUtIuvrqwgoNWSrNYZgMAE1zQIhALQTM3wuGEhNe4AeK9tPuFkMWNAaqJhyyt7Cclcio9Y6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.12.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1064525}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.13.0": {"name": "eslint-plugin-import-x", "version": "4.13.0", "dependencies": {"debug": "^4.4.0", "tslib": "^2.8.1", "semver": "^7.7.1", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.0", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.3", "@typescript-eslint/utils": "^8.31.0", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "52c29c11c70187a6885acfb2bed9408c2bb1e6eb", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.13.0.tgz", "fileCount": 284, "integrity": "sha512-YtbaFHEQYGvR9zPuI525UhwPKEK0sXAvPyHKBUZYadJeIhD6UIuQE60Fje4q5nP17fkhzxjECrFV20nSdLrQtg==", "signatures": [{"sig": "MEUCIQC94bwG+T02DBzVI94GbBv+XiexPHogIuLpTPxtx2+B5QIgHnM3Hslz1KKdzFJ1X2gode8PFLGxraqm//MfWzet4zU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.13.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1038122}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.13.1": {"name": "eslint-plugin-import-x", "version": "4.13.1", "dependencies": {"debug": "^4.4.1", "tslib": "^2.8.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.2", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.4", "@typescript-eslint/utils": "^8.32.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "64ff0e74ca51dadff490beeabee43fc768659aab", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.13.1.tgz", "fileCount": 284, "integrity": "sha512-Ua4HZBmG5TNr18q3Is+nT6mKCzNNpycqtv/+TkIK7E3w4LBlPlZI6vLwmDjXdIZtJPP2Z1Oh5+wksWwlcCjMpA==", "signatures": [{"sig": "MEUCIQCzDt5S0+aEL/x13iprNCGsdPmzx73oiv6uU184Q5HzcAIgRYkEk19s55xLYFp9u3Mt8AQNGCxqwcsrfE4d8znGRSE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.13.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1038122}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.13.2": {"name": "eslint-plugin-import-x", "version": "4.13.2", "dependencies": {"debug": "^4.4.1", "tslib": "^2.8.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.2", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.5", "@typescript-eslint/utils": "^8.32.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "1abc5f67e027dcf043abb1db1ee95e6da675b8b5", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.13.2.tgz", "fileCount": 284, "integrity": "sha512-U9O6nyvfG1cSu2U5dk9ws82cFtWFR+87MOXV7VbQ1PKxDrCqxVRcY2Ka5V5NM7qbm+o6zJeVrr9rDYfCCtceww==", "signatures": [{"sig": "MEUCIQDjJQ9mbHUpfM/6yjzHpoLNYjo/IcQyqc9opHYZDFet3AIgCrZf5ChTqzcrOfsZTd6IvkRkVP1PJfRcD6mAT0vseKI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.13.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1057940}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.13.3": {"name": "eslint-plugin-import-x", "version": "4.13.3", "dependencies": {"debug": "^4.4.1", "tslib": "^2.8.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.2", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.5", "@typescript-eslint/utils": "^8.32.1", "eslint-import-resolver-node": "^0.3.9"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dist": {"shasum": "bcf172817eaa02b782cd0bcdb384091c40dbb747", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.13.3.tgz", "fileCount": 284, "integrity": "sha512-CDewJDEeYQhm94KGCDYiuwU1SdaWc/vh+SziSKkF7kichAqAFnQYtSYUvSwSBbiBjYLxV5uUxocxxQobRI9YXA==", "signatures": [{"sig": "MEYCIQCYKxRh+73uYSh6rGCYBL2yk8UHYV2xRC3kkJUT1hTQwgIhAL2cMnuG+hSEq2NmB963mMXymEujOdi05HNNVB118l2L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.13.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1038345}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.14.0": {"name": "eslint-plugin-import-x", "version": "4.14.0", "dependencies": {"debug": "^4.4.1", "tslib": "^2.8.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.5", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.6", "@typescript-eslint/utils": "^8.32.1"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "2058a0278f4815753eabb21ab7c06acc0625e348", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.14.0.tgz", "fileCount": 287, "integrity": "sha512-9HGMibyAhrdIA/ARd72Uxz8/bBS5xOUx7qrPcyaM+TQNZxBbPk1R8vtXy9JqTnHa6ntGCyGQXsG2lVRQLtcTSQ==", "signatures": [{"sig": "MEYCIQDSNHwMh3qkzxRiZxFYGKOmd366TsZb0aMhb5xzSFgrMwIhAPHXeps985ox68u3bSvGFxZ0kUUfB2syYLR2dkWKgy1P", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.14.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1045919}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.14.1": {"name": "eslint-plugin-import-x", "version": "4.14.1", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.5", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.6", "@typescript-eslint/utils": "^8.32.1"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "bee25ecb44b0e08bdf3b56d2af793c31f49e45a0", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.14.1.tgz", "fileCount": 287, "integrity": "sha512-ffjkksnesWuX4/+bJtNkqdXtYjhbmbrzWt3vBTxn9tjR/Phe0wVNTYKUrwX8vSLDUObuVVx8fJ/UUCb60QvEOw==", "signatures": [{"sig": "MEUCIQDhOLuJJQ2kqvNmeu3rG1uChE88T625nj436a9mdyjMxQIgJsVO/F3FHo7zMr6x3sXVERpjX51MeLfOPEob+CKhP+w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.14.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1045896}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.14.2": {"name": "eslint-plugin-import-x", "version": "4.14.2", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.8", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.6", "@typescript-eslint/utils": "^8.33.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "a7e24d7237a7a5dfaabdd88b747de02fde2b7295", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.14.2.tgz", "fileCount": 287, "integrity": "sha512-lc5ZtgsLzjq2aDN+wGt6FyZFsfNDa3/hDFRM6fnaaqXFPkb0Wb5Gu087yed/K4GZkkHpzo75vbEmIb7mX5OOqA==", "signatures": [{"sig": "MEYCIQCYk6LvUiFt8O72IexbMtXQ8AQ74KQQ8TlKKpZbdbodEwIhAPuHNZWeYnN/uePpHvvnrakW6Fw3z3R8Sr+xCKFvsABE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.14.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1049974}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.15.0": {"name": "eslint-plugin-import-x", "version": "4.15.0", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash": "^0.0.5", "unrs-resolver": "^1.7.8", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.6", "@typescript-eslint/types": "^8.33.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "@typescript-eslint/utils": "^8.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "d6cbf27e47c76a20121f9894c99de56ccbf98644", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.15.0.tgz", "fileCount": 293, "integrity": "sha512-oqCESQlM8r0iRioPHmDqrblH69u11NuglErCnMIY2FcY0UfCCs7qlEuiuqkYKT0puJSQq+fXpDD0MvMTQsAhoQ==", "signatures": [{"sig": "MEUCIQCbRC3gynxOYiufrcCsUvrtgJplH9Kd8DaNGkfArwo0XAIgWEnen/49VUAy24GmIRFpLx83l/I4vWEmlJ9eTVzgxqM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.15.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1058040}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"@typescript-eslint/utils": {"optional": true}, "eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.15.1": {"name": "eslint-plugin-import-x", "version": "4.15.1", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash-x": "^0.1.1", "unrs-resolver": "^1.7.10", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.7", "@typescript-eslint/types": "^8.33.1"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "@typescript-eslint/utils": "^8.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "9fb1ffc0faff2bef825b761bc4d5fd2cc8c9f166", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.15.1.tgz", "fileCount": 293, "integrity": "sha512-JfVpNg1qMkPD66iaSgmMoSYeUCGS8UFSm3GwHV0IbuV3Knar/SyK5qqCct9+AxoMIzaM+KSO7KK5pOeOkC/3GQ==", "signatures": [{"sig": "MEUCIQCbVhmp/oDFt0HDD9GFKL+/ouSK0GCJk5yBUj3tsQ331AIgURzNgWuepg+rpAszOB+rpfco2W3kL+znKgd3lI3XljQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.15.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1058681}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"@typescript-eslint/utils": {"optional": true}, "eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.15.2": {"name": "eslint-plugin-import-x", "version": "4.15.2", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash-x": "^0.1.1", "unrs-resolver": "^1.9.0", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.8", "@typescript-eslint/types": "^8.34.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "@typescript-eslint/utils": "^8.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "9d0728736d6dc88ead54397c931159dc138af206", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.15.2.tgz", "fileCount": 293, "integrity": "sha512-J5gx7sN6DTm0LRT//eP3rVVQ2Yi4hrX0B+DbWxa5er8PZ6JjLo9GUBwogIFvEDdwJaSqZplpQT+haK/cXhb7VQ==", "signatures": [{"sig": "MEUCIDlAUom5IgJeuePqK1R7FLs6/EF6Wf6hGNRAIGqQ55jtAiEA5XyCPHJo+S/zxshm+z6oxjg/1J2ysnGqF5soaLoLa14=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.15.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1059552}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"@typescript-eslint/utils": {"optional": true}, "eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.16.0": {"name": "eslint-plugin-import-x", "version": "4.16.0", "dependencies": {"debug": "^4.4.1", "semver": "^7.7.2", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "stable-hash-x": "^0.1.1", "unrs-resolver": "^1.9.0", "comment-parser": "^1.4.1", "eslint-import-context": "^0.1.8", "@typescript-eslint/types": "^8.34.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "@typescript-eslint/utils": "^8.0.0", "eslint-import-resolver-node": "*"}, "dist": {"shasum": "edd91facd3272aad09489a2a4f866d7f15c4454a", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.16.0.tgz", "fileCount": 296, "integrity": "sha512-g67gvUrgE1VeZ9lFoFM6RfYSh+R3kkxbxDMvNTsz+jxRmj5NA7SHCzhO5O+hDCnSTlLnITMFcl9/hXWudMvX7w==", "signatures": [{"sig": "MEUCIQC+g7LwiD1ZnZRKVOASSE+4Eq7ztgHhJdbNiykZWFvKoQIgG4LDxYMZ0YpiByJ+hb3P6BnZKjKHsijq9lfcWXsH2JU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.16.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1071715}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"@typescript-eslint/utils": {"optional": true}, "eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}, "4.16.1": {"name": "eslint-plugin-import-x", "version": "4.16.1", "dependencies": {"@typescript-eslint/types": "^8.35.0", "comment-parser": "^1.4.1", "debug": "^4.4.1", "eslint-import-context": "^0.1.9", "is-glob": "^4.0.3", "minimatch": "^9.0.3 || ^10.0.1", "semver": "^7.7.2", "stable-hash-x": "^0.2.0", "unrs-resolver": "^1.9.2"}, "peerDependencies": {"@typescript-eslint/utils": "^8.0.0", "eslint": "^8.57.0 || ^9.0.0", "eslint-import-resolver-node": "*"}, "dist": {"integrity": "sha512-v<PERSON><PERSON><PERSON><PERSON>OKaBAIATpFE2uMI4w5IRwdv/FpQ+qZZMR4E+PeOcM4OeoEbqxRMnywdxP19TyB/3h6QBB0EWon7letSQ==", "shasum": "a96ee1ad5ba6816f9a5573a9617935011a24c4df", "tarball": "https://registry.npmjs.org/eslint-plugin-import-x/-/eslint-plugin-import-x-4.16.1.tgz", "fileCount": 296, "unpackedSize": 1070788, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-plugin-import-x@4.16.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF5BNtlyck1Ec2krWqecfEq/0hpuzetD0NKKp8XXn5isAiBnqOrPErdB5laxKE1gpcRV7AhFYq9nW3uMgUey0ip0NQ=="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependenciesMeta": {"@typescript-eslint/utils": {"optional": true}, "eslint-import-resolver-node": {"optional": true}}, "funding": "https://opencollective.com/eslint-plugin-import-x"}}, "modified": "2025-06-27T01:18:08.193Z"}