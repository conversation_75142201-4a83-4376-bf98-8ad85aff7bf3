{"name": "@img/sharp-linuxmusl-arm64", "dist-tags": {"latest": "0.34.3", "next": "0.34.3-rc.1"}, "versions": {"0.33.0-alpha.9": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.0-alpha.9", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "0.0.1"}, "dist": {"shasum": "2fb57552b88eced1742b67356ffe7911816d92fa", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-Hc4Xk4VX2h8XixmHQeoJjDoh20ncRLEJDRgNq7bH45cTg4HfMnbfZODzW3jfMiEAsvztpt+BbHRhyiEPzuQ6qQ==", "signatures": [{"sig": "MEQCIE1nTc+IXFiR6bD8PKRIN93h8W5eg74rr9xAfMU732Q3AiAigWXuXZm2zKLe0E+IsOmcU3d2lkWySLzLeOK16DQvCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 276195}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.0-alpha.10": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.0-alpha.10", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "0.0.3"}, "dist": {"shasum": "82dc3dec08e0baad1c316a65087d59ea6eeef8e0", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-LDv8DC/wjEymNjc77C2BFjAQ6Tjb7i3KQO4zWGDHIGOETV7ylxGB7ORFj9Tm14l8L2gzFFUEIRp9lbH4lcNJig==", "signatures": [{"sig": "MEYCIQC3jaruAhoAHGjwozNN2ql8uVS9/15Oz9aLIo4RWRmEKQIhAOnwMqTIcqXchqV4IsTv8FZv5c6H+lkrgT1lVBflaui9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 276206}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.0-alpha.11": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.0-alpha.11", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "0.0.3"}, "dist": {"shasum": "6385d14eb4317e576d40fb53c8fccacf584da055", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-DnJcaOi+UN3lhhn11iTyT8TIHkLRDdHnEUU/2Gb6IyUSICsyUpu0nXepRhiT9eMalbmIJ8bYj2TqtCz7bU6kwg==", "signatures": [{"sig": "MEUCIAFT8YSZeg6u20NCfLHx0u8nvlVJ2yL/ZAosR2MOiaB2AiEAuExT7nbQbpa/Yb/azZyVevim2wLv16f7C5tjdoV4Skg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 276206}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.0-rc.2": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.0-rc.2", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "db41e11a050f032e3f21ce8c801961d38dfbaad1", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.0-rc.2.tgz", "fileCount": 4, "integrity": "sha512-J9i3jHTdKabc7pvDPSaV8cyfLfOQoZKmuwT/hDYbWwmjTsPETIWEDcuzlaJQykxwZp3JaoYBrlD0vjQ/mTi9Bg==", "signatures": [{"sig": "MEYCIQCovkhDcqwPuv1n24Ai4cM8DRMHtff54+J8uHEtUUeD0QIhALdVFhydoyhRsOGFPR0ywe33x6EQxPRbuo9oNwYVcXyW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "423a9e5e3ec1e6476dbdfd78dda91c6f062bfd67", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.0.tgz", "fileCount": 4, "integrity": "sha512-1QLbbN0zt+32eVrg7bb1lwtvEaZwlhEsY1OrijroMkwAqlHqFj6R33Y47s2XUv7P6Ie1PwCxK/uFnNqMnkd5kg==", "signatures": [{"sig": "MEUCIQDkSGXeUPIXhcL2t+wUYhEC3sTYjMUI8HvbcUE5nUfCjwIge75vyeKjfmUxxxOQvqblZw558poyUXk3f0Cagn54Fn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341733}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.1-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.1-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "edbd754484f8a8ce53395291566c279af9271c57", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.1-rc.0.tgz", "fileCount": 4, "integrity": "sha512-q39H6ayfH8eXc/Nf3wcJ3w3QCLoiwBYJP2hHfpOeL4zJ7fGU69o6pf88B4YnYhTFkoRYA0ERtRABHjAoshKN0g==", "signatures": [{"sig": "MEQCIGYeVCNr82zqsX0ncpcAi2ucwA5txOV8XYveFk/xL8S+AiB13NaH6ZB3kx3kMkGG8XTpFSIiYcj/W+pUhw11JAFxmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.1-rc.2": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.1-rc.2", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "870ef210c1148162110ae3fb35b6ebb4f3f4f4cb", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.1-rc.2.tgz", "fileCount": 4, "integrity": "sha512-S+qKriOxlsEfCwkoi8eOjlLoHdYgqc/p9NqmFvtHxbtjEH6j+nHIHF0t4Z50plqSn28BOucHQh7NHsFkPBUkuQ==", "signatures": [{"sig": "MEYCIQDxh8ZxG+t1pXImKAJkScISM5kdp+eZ/hVE3vqvKsZtTgIhANm8B4S4TAcmXnWCVlqozj9wbhtsrzv/4UaLHj89ysKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.1-rc.3": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.1-rc.3", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "f3e80c1f3f0fcc1301792467f9d10707b5c97f58", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.1-rc.3.tgz", "fileCount": 4, "integrity": "sha512-JcTXUOV2WwNZaLSudRyQDlgbAxtIL3W4l884wj/gmbfYsGpcUaUxGS+I4UpoYM71M570+LTTECldYFtbZvbk3g==", "signatures": [{"sig": "MEUCIG801v7YFyK5Q8OwhAb4q2odAW2PEnGWIlK0PZW+j9wOAiEAt+Bt8bD+iU6XIm5HJY/LZp0ECn8blpqgnIlT0E9CTVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.0"}, "dist": {"shasum": "1b17f9950246108cb22cf98f1822fbe4a5b0dc9e", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.1.tgz", "fileCount": 4, "integrity": "sha512-D3lV6clkqIKUizNS8K6pkuCKNGmWoKlBGh5p0sLO2jQERzbakhu4bVX1Gz+RS4vTZBprKlWaf+/Rdp3ni2jLfA==", "signatures": [{"sig": "MEUCIE32LRx1XTVjznVYadBRDFrLaf1ivLQ9+Px9PMEPjFeqAiEA37CUAnQPaRSu2QD+ebrfyK5GDHM7xAJMYO9wqWT/7+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341733}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.2-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.1"}, "dist": {"shasum": "1663612e1c45d5d50d010137f78c0366aa91b4d0", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-N4iNK0aOsv+lPtS/5qxpvEkhCgw8KfL4E8Zcp2g3KksTh39nx5OO62JVEPKT4Agw1vl5J2M/I3L7aOwlIi328g==", "signatures": [{"sig": "MEQCIAiKBCs8t6lcCUxl/cQZm6GdIW/0VpDfr53/VMP8PVpBAiBV89oHam5t8TVocF0j++UjinScbJg5iCcF5iXq8WGaCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.2-rc.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.2-rc.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.1"}, "dist": {"shasum": "153c4f3760370a7c80e69fe83af8bb0fa316112a", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.2-rc.1.tgz", "fileCount": 4, "integrity": "sha512-IhDGVw46pYRhyCfgLqVCI94VyMrGN0KwmTaDf4290VKXM9c5dPgAvVzEvJ0zA4UuUufAfDmf2wVBuUthnNt1MA==", "signatures": [{"sig": "MEUCIQDP6Mif7m8vE8GFAS42vDyohK0kQbbAKJo2MGkOXQJ8xwIgf7iGy/9Lsbn+qHU9ePf0dWnyRwsJizUrSiWOhX6YUhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.2": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.2", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.1"}, "dist": {"shasum": "10e0ec5a79d1234c6a71df44c9f3b0bef0bc0f15", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.2.tgz", "fileCount": 4, "integrity": "sha512-F+0z8JCu/UnMzg8IYW1TMeiViIWBVg7IWP6nE0p5S5EPQxlLd76c8jYemG21X99UzFwgkRo5yz2DS+zbrnxZeA==", "signatures": [{"sig": "MEUCIQDqKNz3acd7ww5/muKX4b3QrpEA6Xt4XQqkTMCIqiBqkwIgbVWh+lwcCHCXrKspnHbAy3vrAJBzmVZh7Lltn/XR9KY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341733}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.3-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.2"}, "dist": {"shasum": "0e7824d72b07aab4eaaf30c617dc912e8373d664", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-3AAkDdi1C2NsTP+MF/vxN95lbe73sGm0gzsQ6lGrvJoEJlcNyb3iXHIK+Yrr/5O1gtptckDGBZMitPW9oeDT2w==", "signatures": [{"sig": "MEYCIQC7acFNRT+TPnC2w+0JY8cW8Cv6XHv0+N/KpkCUzdfUgAIhAOLJ/sRmqoE7pLgNgMxtRN4AUe96hW7BpDDOBGZETrPV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.3": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.3", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.2"}, "dist": {"shasum": "25b3fbfe9b6fa32d773422d878d8d84f3f6afceb", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.3.tgz", "fileCount": 4, "integrity": "sha512-qnDccehRDXadhM9PM5hLvcPRYqyFCBN31kq+ErBSZtZlsAc1U4Z85xf/RXv1qolkdu+ibw64fUDaRdktxTNP9A==", "signatures": [{"sig": "MEYCIQCZodwfT/WeDw8i36hasPiJlkAr8wA1YcSCRwGifYFUPwIhAMhfUe1RqHetYT10s53bbgQ9FoYGnWC0phj2taoDYQ7s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341733}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.4-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.4-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.2"}, "dist": {"shasum": "41a9d369a59c192cabf583f5f00934d8d081bee5", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.4-rc.0.tgz", "fileCount": 4, "integrity": "sha512-GKquYT7mIpf9BHfxkKcCI2shKwlwp5vb32W04t1JElrjMLTYmRTmkAaXg8q7bacOCEMV27LZEwCL/SUvOERcmg==", "signatures": [{"sig": "MEUCIQDZlIkjY9dbiLFODcrVfjc/AK4FgipAa32hY3WiXdNN6wIgZlkjAx59jAFv5qLLpvrKHre9t3SyAYL550E57U0oXLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341738}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.4": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.4", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.2"}, "dist": {"shasum": "f3fde68fd67b85a32da6f1155818c3b58b8e7ae0", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.4.tgz", "fileCount": 4, "integrity": "sha512-nhr1yC3BlVrKDTl6cO12gTpXMl4ITBUZieehFvMntlCXFzH2bvKG76tBL2Y/OqhupZt81pR7R+Q5YhJxW0rGgQ==", "signatures": [{"sig": "MEQCIHsC7XVH8VxBBeUYegAYd1Fivma8KSZmQNt1cBO1hN0vAiApis9AayM6rcasBN3K3u3rYmevPn9srso76UJaIVfGjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341733}, "engines": {"npm": ">=9.6.5", "musl": ">=1.2.2", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.5-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.5-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.4"}, "dist": {"shasum": "34d06f9f1c2f1165ba4093b0fb7572a1d887f495", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.5-rc.0.tgz", "fileCount": 4, "integrity": "sha512-OtVcyumF7Nu0kcj7zlXyGQyS+RD4AGT+Y4+K7gzDz/lkBYxBkgo4NoxzEnBQG6twhWgdLkAvqg6IxGiQcHbfYQ==", "signatures": [{"sig": "MEUCICBLRKyyrXG+ywGxNFLbXNRu50v7XetiClC9EV4EFrmvAiEAmxEX5B33ivOY8pucA9sxq+JMbux/g+qJSNu63YPmpWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341696}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.5-rc.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.5-rc.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.4"}, "dist": {"shasum": "3453a4aada826874bc4d8706e7d9a379fe10d2bc", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.5-rc.1.tgz", "fileCount": 4, "integrity": "sha512-M3qpNXTvXmwQjtvdUAITw6yH6S+gEA/7ttN3UCOzfJen/CEdVK9mEwmv4ZzJSh+ya1uzXdo9KTecuEjjP1PISg==", "signatures": [{"sig": "MEUCICi5hlx2+TvYgxgVfNebB4BYc3YngintH3zeCL9roXQPAiEAjnHvGKRbQQlfM5MLwbEKhpt/xu7Yj2vjLlltB+Gtnc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341696}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.33.5": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.33.5", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.0.4"}, "dist": {"shasum": "252975b915894fb315af5deea174651e208d3d6b", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.5.tgz", "fileCount": 4, "integrity": "sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==", "signatures": [{"sig": "MEUCIQCcCAPF+Izl6EOZb8DET03FfEvuzczBkOgXwG67oIym8gIgMTOXmWHczt75XQuUtd00KXienFtGR3zdjmdzwUTTFbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341691}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.0-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.0-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0-rc4"}, "dist": {"shasum": "e758712945d546fefb4555c90706197dff8530a1", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.0-rc.0.tgz", "fileCount": 4, "integrity": "sha512-CqYOm0aOg+6+Ep7Dbtl5/V9Ll8u5aGK/e6XLVSZLMsJ+Bfh02NnYVDJZhYZPh1QmvnqUJwCX8egIL4lpiMPuCw==", "signatures": [{"sig": "MEYCIQCaHsUJwt1XkkTdQszSXg0qEYJ4Pu+twhjrBLkPjJN0XgIhAO/j74iMhSsx907UoVzj1Ng9QqntgnbL3eGaXnyU8Lyy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 341708}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.0-rc.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.0-rc.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0-rc5"}, "dist": {"shasum": "2a1ca17516f39c4d775ff6d899789f6d44fde0e8", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-4n6BiTmY+64FHUIgrDwaM72/EQ+A5Iq8RKdwFG1YqYkQMl4LNgKSmPXNAlV7lWzkr668DGgi7S4LRKfg8DbY+Q==", "signatures": [{"sig": "MEUCIQC6Z0714iu70ZoTmaYjsoRQUUYBJ+g+xxVvRQlufDPtHwIgdOujmAbWFn8Zfw13bVdMjdxIzYmU+iGeAcA7a32z2ZM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 341708}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}, "dist": {"shasum": "5d0cd75e9ffab2f02a4c158fcfe9832b9a333bc1", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.0.tgz", "fileCount": 4, "integrity": "sha512-pmsehGlQIOlAQ8lgtDxpGInXXMAV6JrFwoJ0Ib9PpsVYuwFM+Soa9mVZMfsTO+u9dBhCMEn2AP3mRUljgpGYvQ==", "signatures": [{"sig": "MEQCIFtoJ1I7q94F2ZLoRr2sEnQ8xisU7oJlT4Vrr6J1ljwjAiBR87mr7GgwWhBsUniFbAUTgsx2L/xJ1URDsMVOvN5wbg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276163}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}, "dist": {"shasum": "b267e6a3e06f9e4d345cde471e5480c5c39e6969", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.1.tgz", "fileCount": 4, "integrity": "sha512-DfvyxzHxw4WGdPiTF0SOHnm11Xv4aQexvqhRDAoD00MzHekAj9a/jADXeXYCDFH/DzYruwHbXU7uz+H+nWmSOQ==", "signatures": [{"sig": "MEUCIEDNK44LZILyxLtgeJTzC5WtUl4PbesWTQKbt7CkFz/RAiEA8geUIYwhhiELqRiBDgiYZCWzADVjMub5U4lcHXSDLN8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276163}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.2-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}, "dist": {"shasum": "5507ccd1011d925718f4be517cb088c2943eabb2", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-6L8CpW+RKl8rd6/fdQ6PWgH0BXf45xCPmHBLgrTfwFt1TQdhsxVK5TrvJmXQnklxQfqshyYw77LABvI1XBhrTw==", "signatures": [{"sig": "MEUCIQCa89P3nwdlG4UP4G5d+jkWgWplWKjibBmc7gUlwiQJcQIgcHPjCJdKDmTiyJ1aBZKArqRtSNOUBfHUrNwGJfCvfXA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276168}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.2": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.2", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.1.0"}, "dist": {"shasum": "cfac45b2abbc04628f676e123bfe3aeb300266c7", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.2.tgz", "fileCount": 4, "integrity": "sha512-NEE2vQ6wcxYav1/A22OOxoSOGiKnNmDzCYFOZ949xFmrWZOVII1Bp3NqVVpvj+3UeHMFyN5eP/V5hzViQ5CZNA==", "signatures": [{"sig": "MEUCIQDnyJ15b+y+2HGPzCDxnQyLIk3nwFW/0AtxwTVQwC2XmwIgb7ESmboWE+1nci7VX7TphlwlCFGE9e8zdrc2rkt9BvQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276163}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.3-rc.0": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.2.0-rc.2"}, "dist": {"shasum": "da1e5fb0b7076260d7f12f14af81d6c8a0f3eb3b", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-hZ001JXCFxnbBmlaqX7bDpm/dIx+Kp+i9ZdCWViMtU0/Ji38ySQWLbqL62H/nHbI3GYblfXogibdV7jJRln7Yg==", "signatures": [{"sig": "MEUCIQCuNs4vzlK7LDI777ACmxT1XlnGJdwxABir7aloTX5a6wIgCUkTbuSm2N/XNu8luCSbmGYuqmnCB/uJoB0OMJVssms=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276173}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.3-rc.1": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.3-rc.1", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.2.0"}, "dist": {"shasum": "09980343946a20c2b802b23e7e146fc9a2587459", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.3-rc.1.tgz", "fileCount": 4, "integrity": "sha512-2hG1tmcEBOy0W1i3MvjLkPh7OFOZ2xJtRg+NKuJicYFj5D8uVSiFMacxIOm0jB6LaMm4MPjU7GQvhyslL/5JAA==", "signatures": [{"sig": "MEQCIHlEo75OaBxfLCEGzZWlWIQSKAeQjD92KJObBDFRjgJiAiB7tYEnYyPw46+4ge/4d9vEdQAVJUN/Nx8unY5XZEY5lg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 276176}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}, "0.34.3": {"name": "@img/sharp-linuxmusl-arm64", "version": "0.34.3", "optionalDependencies": {"@img/sharp-libvips-linuxmusl-arm64": "1.2.0"}, "dist": {"integrity": "sha512-vAjbHDlr4izEiXM1OTggpCcPg9tn4YriK5vAjowJsHwdBIdx0fYRsURkxLG2RLm9gyBq66gwtWI8Gx0/ov+JKQ==", "shasum": "62053a9d77c7d4632c677619325b741254689dd7", "tarball": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.3.tgz", "fileCount": 4, "unpackedSize": 276171, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDEunJ+ZO9AU7K67DgFzON9DhC9J8Ddoq/gAGTNfrNPpAIgM5Fg7QWD4SDsFxe2BCpSowRxQceFnyeTH1Yl6TBL+lc="}]}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["linux"], "cpu": ["arm64"]}}, "modified": "2025-07-10T07:57:34.060Z"}