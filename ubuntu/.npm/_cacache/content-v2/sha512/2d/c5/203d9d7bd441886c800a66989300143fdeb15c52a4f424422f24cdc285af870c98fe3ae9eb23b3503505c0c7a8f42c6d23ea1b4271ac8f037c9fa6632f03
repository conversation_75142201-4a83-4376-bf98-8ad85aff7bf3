{"name": "babel-plugin-react-compiler", "dist-tags": {"beta": "19.0.0-beta-af1b7da-20250417", "latest": "19.1.0-rc.2", "rc": "19.1.0-rc.2", "experimental": "0.0.0-experimental-acd39a6-20250710"}, "versions": {"0.0.0": {"name": "babel-plugin-react-compiler", "version": "0.0.0", "dist": {"shasum": "1a1f9867fad83f217f0b3fe6f1b94cca0b77b68b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-Kigl0V36a/6hLVH7+CCe1CCtU3mFBqBd829V//VtuG7I/pyq+B2QZJqOefd63snQmdfCryNhO9XW1FbGPBvYDA==", "signatures": [{"sig": "MEUCICoT6EgOknnw9QyEw/jATsNYtE3SEYiPfGh29M7wf7eSAiEA2ZwSrjHabsOYxhLhIjYIYeJm1iKpStWGHjJWisok57Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223}, "deprecated": "This is a bad release: please install from the experimental tag instead"}, "0.0.0-experimental-4690415-20240515": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4690415-20240515", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "prettier": "2.8.8", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0805a3e024a6ac17f5fd4301e43a819dd34d5039", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4690415-20240515.tgz", "fileCount": 3, "integrity": "sha512-unBK0w2vxrFa3CAeeEGXfMTdBHM9vWZpItUXea4L4FDrD4QpqcAVMtR4Zu0GC0PvXRwj6KuPzzBkVmgGGPFxnw==", "signatures": [{"sig": "MEYCIQDdyuM7+rYslzqrNXz5cr20rpG84CzhdGxsZSsDAclBUAIhAO0ZFIDhqwlbeHNWZp7mjS0lriD3Ypn6MSYCw11NfM04", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5441252}}, "0.0.0-experimental-c23de8d-20240515": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-c23de8d-20240515", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "prettier": "2.8.8", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9030a1e93b5cd6d83ea1aa168eddf91beac5217b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-c23de8d-20240515.tgz", "fileCount": 4, "integrity": "sha512-0XN2gmpT55QtAz5n7d5g91y1AuO9tRhWBaLgCRyc4ExHrlr7+LfxW+YTb3mOwxngkkiggwM8HyYsaEK9MqhnlQ==", "signatures": [{"sig": "MEUCIEzdxS15spai6lnuYvMMHlzeKLTqnDd2WScNBSB0ELy+AiEA+VYx22IgVaMZcOAa/XnEBy/zLEM5NxPg096AOxAOyn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5445079}}, "0.0.0-experimental-592953e-20240517": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-592953e-20240517", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "prettier": "2.8.8", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e800fa1550d03573cd5637218dc711f12f642249", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-592953e-20240517.tgz", "fileCount": 4, "integrity": "sha512-OjG1SVaeQZaJrqkMFJatg8W/MTow8Ak5rx2SI0ETQBO1XvOk/XZGMbltNCPdFJLKghBYoBjC+Y3Ap/Xr7B01mA==", "signatures": [{"sig": "MEUCIAIRgtXPdzyb7Gr/UbB8/uGB/eo3oqLeIxLdef0YDLNaAiEA/mzrlqooH/3Kqjpx+ny9mbHgs7rzkbqrcD6HuoNtNbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5446162}}, "0.0.0-experimental-487cb0e-20240529": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-487cb0e-20240529", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ef3c2936035381ce6a7051a843dba32bdd28ed39", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-487cb0e-20240529.tgz", "fileCount": 4, "integrity": "sha512-77FYmA+oiCQnq5fK0jvoR0b78oKkgXrECc8n9U+gyWh+8P0zFJSX0wyI16Z456m/lrcANE56VWbr6+VxzoMBJg==", "signatures": [{"sig": "MEUCIQC5UvMJpy0hhhYLOndMv1l0yRTUK0KmCzqW3iP9BI1qegIgBP0Tx4wtyeKEdSuhW7JJpXFo+vp6RbsvQsoFKIDIltw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5450243}}, "0.0.0-experimental-938cd9a-20240601": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-938cd9a-20240601", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7e922f8c579564a42b2c3b7d1719f6a5bec5d554", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-938cd9a-20240601.tgz", "fileCount": 4, "integrity": "sha512-t+uBHxbfxq2z4j83ZYgOsV0dlSaRgPfhrYB5+CMv6ByXUAv5wm7m7YLFx67fWKrG3eDhq3+KH1OMeFypuDLkUA==", "signatures": [{"sig": "MEQCIAlQQBi+jF2I6cPwPsMIpqEQNGWvVS5dm2wCGic2OvreAiB3A3ZaKZRrJu5jdd7RuENsNcie3CiRif4Ys+PFWrToZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5467624}}, "0.0.0-experimental-696af53-20240625": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-696af53-20240625", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ebf18487ce3fa795a7af78443be0a9f274df8df1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-696af53-20240625.tgz", "fileCount": 4, "integrity": "sha512-OUDKms8qmcm5bX0D+sJWC1YcKcd7AZ2aJ7eY6gkR+Xr7PDfkXLbqAld4Qs9B0ntjVbUMEtW/PjlQrxDtY4raHg==", "signatures": [{"sig": "MEQCIBFs6ZvDEwoFtaLgjuCAgmJLtipn/VgmPzTKNdbp+73/AiAlSapQHzJWm0KsS8+CUZqTteEk0RERRgbP7MfazgWBPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5499389}}, "0.0.0-experimental-f1f288c-20240725": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-f1f288c-20240725", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "70217886e88d7467d8c18da33f51a1c7e55f6b11", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-f1f288c-20240725.tgz", "fileCount": 4, "integrity": "sha512-mtz9vOMRoxodCX38DUJdpKi58gb4LtTVzAL58wkiPBIqEdNC7dbLbb914sqI+SkPzPHUTdou+HWozpmhiY/Ecg==", "signatures": [{"sig": "MEUCIQDzSoAvsM1va6iNZAiwI+JZii2O6/y3tnVBXy81TX3ckwIgfJeXxvSNkyjyjqU4nkR0ohLOwlfkwa6/p+QHNA06bFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5506214}}, "0.0.0-experimental-334f00b-20240725": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-334f00b-20240725", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d5fb88a286bb14c958b62f27fcfded9b436e1574", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-334f00b-20240725.tgz", "fileCount": 4, "integrity": "sha512-ktVKfOtJdHqrLib7IriUe00hnrs585He/n8uzs2yJT9pnH2eyrmMG21aRGBJKxt/P5mdizGLxgyFk0HSMrekhA==", "signatures": [{"sig": "MEQCIB4Dqk/Oy3SHeCOf/2nmNO9BEqZJtps7iJYDfRk7vYKtAiBtznP1hkaJE0SbZfIuqL1OhIjPt3SGqbd3lg2CNsHl8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5505324}}, "0.0.0-experimental-0725353-20240806": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0725353-20240806", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "566ace7b11ddad03bf77bcf0aa14882c8666721c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0725353-20240806.tgz", "fileCount": 4, "integrity": "sha512-bTCvTVowBAjcUSMAdBYeyFvY93FzL/UmX7aoP5f3b3e0D7KLlprWxI0GeHiTsRGGx5KB1ieYOnRFt03Kcg72IQ==", "signatures": [{"sig": "MEUCIQDQLfAI4zTNVPalDIaqSm5SrEb+6YRiZ9zXj5mWRrQcYwIgdEcmD3lR7pGL2Z9+O4IN39PemuGfrd5Ai1NA9TU6efo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5502568}}, "0.0.0-experimental-0dbad55-20240807": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0dbad55-20240807", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5c9f67ea42262476b03acdd7bec944f794cb7f76", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0dbad55-20240807.tgz", "fileCount": 4, "integrity": "sha512-Ruh/VoRsEmJ3JMyoW+85ku73/4crc89ccJVsUHdit/hy6HJ/IPeeNnztwrxccHi9zquTvvRLEYbQBJAzSpba0Q==", "signatures": [{"sig": "MEYCIQCgobyrgcZ4JBTS9MWkKsSSlS/UHcGgrGEKU7SYDBcE0gIhAN3vCKXvy5CDHXlEwhJEszoMV6A+KWu2WXpGHY9aAyuo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5508097}}, "0.0.0-experimental-4b2c386-20240808": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4b2c386-20240808", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b9f57ebd7c5202a3f803a3517af2af91b8b76858", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4b2c386-20240808.tgz", "fileCount": 4, "integrity": "sha512-hLrBfyVW9VuKP91UI/hqM2huaT0vqrP+S+yaM8VPDirE4v3wtK7tjOYK0fjt7HO6E6Qe8pykdES8OwLRTeBvNQ==", "signatures": [{"sig": "MEUCIAqbs+iuQFiqxMM3uw/3oWT6rse52YPuBvTCt8i4AEZNAiEA+ehCKPrL5qg2imZrcl5hKYDGPECCOg+zuKE6o8ixTe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5510882}}, "0.0.0-experimental-da85dca-20240809": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-da85dca-20240809", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f02054680656a92fe3fadb011e6520962829b53c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-da85dca-20240809.tgz", "fileCount": 4, "integrity": "sha512-Q6JysREJx8migZEIdKUChmWK4KpzNGJNVLRGu2mHFCs7SSqv22A8nHjRAJotfXcOrY35o3xhUlBFPIwynEmW1A==", "signatures": [{"sig": "MEUCIQDcMux+gWVFYylfrGl2DyTGyde90lAfSRGPUskM81FfNwIgNK2Y4LbCU4JYv2hpn207+bZPbpAEeS4SdRK3qqJtHMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5513119}}, "0.0.0-experimental-8d2bec7-20240812": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-8d2bec7-20240812", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ac674b09a510b389148882cca8f1f0faa4304089", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-8d2bec7-20240812.tgz", "fileCount": 4, "integrity": "sha512-FiCCckbCC9wmEtD/ZsImW7I/tacDm91XK4RVkExa0aBbRdZ3yU9FJkyfyEtqE6wX7hgZdmzJ5I10xGyv2tygig==", "signatures": [{"sig": "MEUCIAy3Mun79QzCMndAoQbWFlTz1rJ63835a2PsMmf4TRDqAiEAgkjmNIoPtgPPFPG0er6maHP2/odb6xx+Bh8XxF6Kd80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5513482}}, "0.0.0-experimental-10b7b82-20240813": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-10b7b82-20240813", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "94acddd4c522f7e71a16f42bc5f40258f11c9e45", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-10b7b82-20240813.tgz", "fileCount": 4, "integrity": "sha512-fMrhayX599r/Sfmd4JbFvXu4MrahZTP2N0eYdTvUpGB22atkPiwjMGPlVVrRCEr30oYhmku7uww9mM4O2XBkGQ==", "signatures": [{"sig": "MEYCIQDguTEsCm6PsOBTqeOZvz4+42iCyAnpPlnp0nXTWarS3gIhAKBw/qzik6ImZzxRTHVryQsIm3n8JG/hC8QCVouNMERL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5523222}}, "0.0.0-experimental-1cd8995-20240814": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1cd8995-20240814", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d43071424bd5a9bc542770c5fbefb0d57620b1c7", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1cd8995-20240814.tgz", "fileCount": 4, "integrity": "sha512-4DWAliKzk3ACtvOn0pbUStj8DRy//bA9Icze8u/8+hCYNR2xNWupnocP+FpoCC8tDNkFDSAzQyrQZS5uBeyH9A==", "signatures": [{"sig": "MEUCICneeRPrgrgecdqisbap8l6AgnSFIpP+6yNyZyO+ZnDTAiEAogsRt8IVoKz/HM7gRj/Bp2nqYlyHW4Z8hkXxXMtN4RA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5525559}}, "0.0.0-experimental-5f79ba2-20240815": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-5f79ba2-20240815", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b308284a80bcab30424f52a591d5132241df3656", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-5f79ba2-20240815.tgz", "fileCount": 4, "integrity": "sha512-9/pMj32tpmzWyjnPmRqQ25L9MJkffqVPEsoboHaSKypnaB/Xo8haWk54T5WhRf/WfE0kB36w2Nm/STkAHGXJWw==", "signatures": [{"sig": "MEUCIQDrA5L5z0KVyA+SxVuRpmVprZnNAhky4BkAHTCEjbDLxQIgdaoMqsjhhVANiYcaJUVsildH4Itg3udKJp8ncU5bj+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5530722}}, "0.0.0-experimental-52d6685-20240815": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-52d6685-20240815", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "755a46a28bca91672a5922c370cc2dc68de9cdf0", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-52d6685-20240815.tgz", "fileCount": 4, "integrity": "sha512-3oCBPKIMUPDeWSJYy39T/MWbGdNFt6SOtpv/mmHUr6F04dfzlHgKLMpRznBJDiwERn3Aw0y/kZsGYHkrKI5Kmg==", "signatures": [{"sig": "MEUCIQDiW7f7BbVAA3p5xotzbU8TwFFnQUb9/G0Cgiz4pHsvEgIgbxNFrlEJV5hDFr7FTlDwkumPCGx6yrJUrbTCWX49CZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5532136}}, "0.0.0-experimental-1f3db3d-20240818": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1f3db3d-20240818", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "dbf55a4df84ba5bd8c913dd75a9cb5163aa0d95b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1f3db3d-20240818.tgz", "fileCount": 4, "integrity": "sha512-32h65WstrDRIPxoaL/e0xZlD8SzOxLTC6XtCkCTN0loLl7l9neJcrJq5z93ROj85FBD43slYhK28htKN/zkemQ==", "signatures": [{"sig": "MEUCIAp0ABYv1qZoqdUBV5QnddfCJcigjWY6KZC7O0w0g5gDAiEA1uuYLGb6+5fMLI+aD7pOCOIEH2YiWsul0Qip5I0Lky8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5535309}}, "0.0.0-experimental-7d62301-20240819": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7d62301-20240819", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d23ef26d8d660ab4d2e15d0a782ec8755c94ceba", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7d62301-20240819.tgz", "fileCount": 4, "integrity": "sha512-MtERcQLDo4ZlkDmZXfdPJoW8s8P7mGvQH6vwFLHdllOrezDow8Njg+jJqU4G9awKAKd5hv8znxfb2rsvJf78GA==", "signatures": [{"sig": "MEQCIC7VQAJVhbXSFltgG0Xbb7xYm9zGVKBcHF0pjGSMzloTAiBcw8kw7gXIbJucXBF0A7ch4DoeAZFRSvcc8vIzqjSfMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5536710}}, "0.0.0-experimental-7d62301-20240821": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7d62301-20240821", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "882df777ae3df7ff0dc0d6bbed4ff25e576263a7", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7d62301-20240821.tgz", "fileCount": 4, "integrity": "sha512-jkkE/OzJpu5tOa3GwGM3vgaBAjkOGYmKEYyo6NXNaCtt/ITYjs2Tq2+MmrDLx1nsxBDi91rB+MLV8kes/KQ0lQ==", "signatures": [{"sig": "MEUCIAoAHdNQFxu0cD+h0Jo/sjzodBPjkbsydd4S3+pW1YoiAiEAgIyqryevYp/7c/pScZmCzTzCDGB/48oV4kbosOn5EMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5536710}}, "0.0.0-experimental-7d62301-20240822": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7d62301-20240822", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d124dc931343accbdb2b1ebd3e947572c52d4404", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7d62301-20240822.tgz", "fileCount": 4, "integrity": "sha512-i+wxBCobHNIHs3IWyUC3ooB38VAs+VLDOdkvNZSoKKk2+KayDW6S9k7bJQHrKxK70VlEe4XEP+iFXVps3pSUcA==", "signatures": [{"sig": "MEQCIGz8erBrzSvKVuOHq1qqBA5vWi+/LGqLBfIejfSTG4iUAiAKRblUkX95ijCxvzCpWI8LHROQzfz/MvLIvP3vAgis4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5536710}}, "0.0.0-experimental-48eb8f4-20240822": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-48eb8f4-20240822", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "075a2570cd1c6015dc0a958514f344d7fe167dd5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-48eb8f4-20240822.tgz", "fileCount": 4, "integrity": "sha512-yMkqwmqBtn6R8sY5b6I4aEQxzaX8sDHLa2JnU44wmZT6UFKh6GqPxtHw1mPNvxEadIXeyClT+JEZda/cA//F2g==", "signatures": [{"sig": "MEUCIQDtLcB+Kj7c7O3zu0kj9upQ6nqXxD6ksVrnU9fczM38oQIgJEXO1ef4rJEZQ4YZkzPeGM9uuR/Sh4tLPev5QhEfZ8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5544817}}, "0.0.0-experimental-9e9694c-20240826": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-9e9694c-20240826", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "dc67d7290e02bd53ae9ee57d21a23262ebbc007f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-9e9694c-20240826.tgz", "fileCount": 4, "integrity": "sha512-JvR3ixeURr18emkgEAxFAiocF2fbXinRdiEonqMcS+6aCBiRO0itjkfJ9PeLiFhKu+LJ2QG0++MgKURkgp+m6g==", "signatures": [{"sig": "MEUCIQDrjbe4/ZKt4fnF9KArOMrZ6mSoHsGQI5uaiDjlgvexXAIgL7/QjjunMimKzXQstsmjVH5r+kZLRz1sCrP8Vav0t2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5548472}}, "0.0.0-experimental-9e9694c-20240827": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-9e9694c-20240827", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3ce28cc8d249f6d99d1e241b0b90e11bb04317dd", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-9e9694c-20240827.tgz", "fileCount": 4, "integrity": "sha512-QjUkS9KM9nepXi6m/ktXrN9w799h50UAq67Oe4iXTzMfP1dK+ycwrWmho2sLkLZB6W9rNFmIdcbsh3s8n6qnLA==", "signatures": [{"sig": "MEUCIQDdfKgBs79AKjmSC56LkXJ48sqSZ4Kq0RbCeHuUM+nv5wIgNqO8vtVDwKbWl+sVyGnfwI/Z13D5yDKXvcksrSS4qT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5548472}}, "0.0.0-experimental-03b7aef-20240827": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-03b7aef-20240827", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "515f518ca0eead995ac10edcdb9aba7ef50a3a94", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-03b7aef-20240827.tgz", "fileCount": 4, "integrity": "sha512-kRDdxhj0WsL5S/IeOyx/0xbSLghFEh2kcYattEEyMfahnmrqNDo4A4SjUpJa3UA69tK7L++Xrj0igMZG2FSmRw==", "signatures": [{"sig": "MEQCIET7WpaDT6SKLCh7AOM2HrrvV1CiItCM8Gex/NeHqorlAiBI5SZAp9eHOHi5Ku93QqFwaU8SvboXQnLkQMdGsOcaYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5552115}}, "0.0.0-experimental-e68eda9-20240829": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e68eda9-20240829", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "cf2116522ebc90d5a023a48f874e0aa5aa9b4737", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e68eda9-20240829.tgz", "fileCount": 4, "integrity": "sha512-H7e+R9ze2Ftdbh4W7C1oUIdWSn4xQEcRQDM2vVG2LBA8qiyvnnSR5L6+e06/J3fYHjFEkgdL96FsdOM7TNW2AA==", "signatures": [{"sig": "MEUCIB0IPY5GcLXCxOR0xQNrgW961NM2a5S+oL+6AI7ej/rVAiEAsLqlCOGDWuTEtW6nuCWoAcGMmjeJIWz4njjaYLzZwtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5563619}}, "0.0.0-experimental-4e0eccf-20240830": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4e0eccf-20240830", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "579a2d978a4de6ed07db84863a4a1d146dcccb87", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4e0eccf-20240830.tgz", "fileCount": 4, "integrity": "sha512-NrhscwyQweUgDfmWrNigyb6mJM22euUSoV2PHCZ7JPRN+vSpqbQuDS2JYfusCxueVMxG1Qaj2JFBAl9RPFeaLQ==", "signatures": [{"sig": "MEUCIQD9TkjNxhx5W0Y6hnK2HAirrEo0L74QJpggjYC/K9XJ6AIgTDwdeNMwmFWmrn9i6Fgx1WilWBV5ksLtZbkxPuAI8pQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5563559}}, "0.0.0-experimental-4e0eccf-20240903": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4e0eccf-20240903", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f54c3a99e532cfb854b1165363e35c3fe0b99b23", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4e0eccf-20240903.tgz", "fileCount": 4, "integrity": "sha512-4PSoBfsZrxtodjG1aIvU+yRIHEXfmDn980pOeUvf/9yN5GpZOBXiijRHheIVY8MeB7T9KHyV45F9ae4xx5ovwg==", "signatures": [{"sig": "MEUCIF9+cmDiRPCvjZJbQ2qoRtfuBuF8aPrf/Z/7esSv2Em0AiEAx7uc1RdplrKIpuR9YoWoo0oY7cCjFQB3d8aB6svHXJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5563559}}, "0.0.0-experimental-7449567-20240904": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7449567-20240904", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3f9f3156b7df5d1dbcdb5f7fa9536f43c2a9db5a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7449567-20240904.tgz", "fileCount": 4, "integrity": "sha512-IqCWVU3ItO/BOTJA9gM+FNLwe8CGnCky5ZC3EULqMKkG7NVr/9XzUOApj8xtmQIM9WuZzzqRaJMXmW88xF+0Tw==", "signatures": [{"sig": "MEUCIQDC8o/t5QHMKC/3DRQ0AEYBxlNALZH+P9UqgjhJ4S953QIgCaHwOVtvI4BT6TsnXdL5fJPzFVtTdqWmrnXPoOEodBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5566309}}, "0.0.0-experimental-7449567-20240905": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7449567-20240905", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "794920034765672c1464e8a30b1926e7e87bd813", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7449567-20240905.tgz", "fileCount": 4, "integrity": "sha512-ltBywPFOEf1rRnkRQ1TiiPJeqJ1Cte86bo4tpSPsfqGTTsiyUo8OLyOR13EG08QIFTQd6HfGGgjpE9Kv/t5Vcg==", "signatures": [{"sig": "MEUCIQCCU7TvC/XgEWiXfoOp5T5ufeOGtSyjnjcMz20ZUZQAIgIgOF1lPCuiYn5btpwbK9xosmKNolYqaSJlM+F7YBaswgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5566309}}, "0.0.0-experimental-2bc6fec-20240909": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-2bc6fec-20240909", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "71746b2cad8b8789ed7ecb1e86f8d8d13be5cdb9", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-2bc6fec-20240909.tgz", "fileCount": 4, "integrity": "sha512-iBgf2ZUsw3vJRkuNaCHSQ+vAhzKiR77t+XZyejs6FDcpXVCnwtQJ+yoQbSTaUP9ktrv8gdRdvl8IcXWHRBUd4Q==", "signatures": [{"sig": "MEUCIGLt50ehv2aCZlktIwJMxGaP8nvJrhIKOKt8ZIDiSxHrAiEA1Qm4sNPzV13SuxersBffHp74Mek2F1SBkXjx7zid3T8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5547874}}, "0.0.0-experimental-2bc6fec-20240910": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-2bc6fec-20240910", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "58d41653dd167a446beb803818e57325175d2936", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-2bc6fec-20240910.tgz", "fileCount": 4, "integrity": "sha512-ktoe5CVIGdUyos1SprZiobQtx4IdNFsoUKaKf58e6NOlycNBNkDr8V/j1lmqPfwIp/dxQYIZpV04xwzpfFqFKw==", "signatures": [{"sig": "MEUCIFnyC6zv2Sv8tWnp0diwVbCghBqVnWfy8lPZjAJclsoyAiEAvHgY6+fIsc896UXhAYghXo6nbQEXyvbFdQplGdxSMxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5547874}}, "0.0.0-experimental-fe484b5-20240911": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fe484b5-20240911", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "539c0fcaa7841243a6bceee14ee741d620f5b7d1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fe484b5-20240911.tgz", "fileCount": 4, "integrity": "sha512-68t0K1tr9/q7rs2o2RAMvU+ytI4DaqFEEnfhGD++8JCnGQhl2FZKMPi5u9BcWPYPhSCOSuWYabpj4q5owQWwNA==", "signatures": [{"sig": "MEYCIQDYgcQeb/tI8JXCWOf8HqUxMSokXQyKFPap99T5CI8E9QIhAJ0/f5t8Y+a+ANZflewsca2ee2vgRajd+VUIBZPG3ZON", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5550382}}, "0.0.0-experimental-fe484b5-20240912": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fe484b5-20240912", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "213003ff60f50c3deb90a5b41b9370e40bff66d2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fe484b5-20240912.tgz", "fileCount": 4, "integrity": "sha512-iGtEbwQeiLXba8o8ESTjogmQ8rTP6xHi+w3JIxR8HmKAb+SYZ3cljRhpOEsrxZIXuk3L9w9o98BJFIcxaVyFag==", "signatures": [{"sig": "MEUCIQD/XUa7LQ9LXvZ/RdvUZtLiKM/fAgZc9Mm/uzq7ty+2kAIgBrFTecl9M7qJ+2cCWsHBNeYY7aVLWOmekL7Kf/Ky9Z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5550382}}, "0.0.0-experimental-de2cfda-20240912": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-de2cfda-20240912", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e4fc57237b851a63cafd2c3d88d6b877832dfab3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-de2cfda-20240912.tgz", "fileCount": 4, "integrity": "sha512-ASAiKVPBNVWe1NHGEpYESYDs41+RzAv/8ZziAgHO3bYtBNwp0+4SeUkMhji5ueRfo1pYtsodnESwgiVGhzf1ZQ==", "signatures": [{"sig": "MEUCIF5ZGvaQwYTdsP9CtVflH6Tcm51NY+bZcJiEsBG0xu9NAiEAum0of/pWYH/QVbLgyrLW4gdacAqs/qrVo76k+8Cpaag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5582565}}, "0.0.0-experimental-23b8160-20240916": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-23b8160-20240916", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f5af753d13d32b79f40490d9eaec440ab61f553b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-23b8160-20240916.tgz", "fileCount": 4, "integrity": "sha512-Ou/Jo2dxmTEYOsvsYzykhLMsDDcH+ol8zp5ZzudzQAEia08aqa7lzJf9CC0nh2paPmk1tv7uWlflZYB+PMzljw==", "signatures": [{"sig": "MEQCIBkXaXbWOrkdsQob8LexHH1FIomZ/sSPqc18k2vqy3VXAiAdh2VTxuKIBuWUg5bscEvVfVw01MsLmj9g7gP3YCnqRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5586607}}, "0.0.0-experimental-ca8e0be-20240916": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ca8e0be-20240916", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "865b473516db25ee69e34b52e0e2d8576b8e7dea", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ca8e0be-20240916.tgz", "fileCount": 4, "integrity": "sha512-S/fMyIedoXdIVEleWMctmllsjXIDQwGiB8Z5v12L+lGKJ7n28K/8aFzVFcDpICnIIP3tXuAo0rF1bfn0MYb7YQ==", "signatures": [{"sig": "MEQCIAwucOUHDsc7SmAaQuMBaOUigQh0tkiuJkSoAIZaecQwAiBHfRPFWwg04veNZ6usaDbYl8YLrINpjzPv2BnHpL69BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5590435}}, "0.0.0-experimental-24ec0eb-20240918": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-24ec0eb-20240918", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "fb56d4e688d23d334f7700e180428e0968633076", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-24ec0eb-20240918.tgz", "fileCount": 4, "integrity": "sha512-3uFbIPB/q68AUwoPlv4nleIARMdLnzco0FL9f1Iv05kb0/wwOX8sE6rHz+6fyXEScBWf8cogTq1lnlHBc/dyNQ==", "signatures": [{"sig": "MEQCIA0tOkJhojYjsdiRIVT91p6yrkPO0re7l3HxBserW/ZJAiA3GJoW+xi0eN7/EFWofiZruDhyplkYPVUitjrSTKVyPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5602501}}, "0.0.0-experimental-6067d4e-20240919": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6067d4e-20240919", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "458260c1ac52b8bc227dc15b4d866dff16130eb3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6067d4e-20240919.tgz", "fileCount": 4, "integrity": "sha512-3BHXXnd3GzOkHHWMhYLARTUa03PyMzhbAA3ptG+WXujJu0mx1BT3CslcqDlKMh7j508uspT5JCXRZh0ZIN9a0g==", "signatures": [{"sig": "MEUCIEiM2xQtyh6lRr67M3H74SHbSSdngVxDOHzwBDrpPqSMAiEA1nf6LYjBIHWHv3MarTxrn1qT8ClQTWdCNv1ScCCQW14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603658}}, "0.0.0-experimental-6067d4e-20240923": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6067d4e-20240923", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5f17b1e921a866a6a7c209228a4b8c5a6799a3a5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6067d4e-20240923.tgz", "fileCount": 4, "integrity": "sha512-0vYsWhC8W4BUIS5cLiGBJkOpj8bINpbATo9O9scZ7vxL6/+jMIomUkX+k+R0lpG0ztu0x8QVMENwoFCawdZEbQ==", "signatures": [{"sig": "MEQCIBjlQhERwPISXnKAqVVnLbJJ/yqet0b/6A/vbFxiG8L/AiBqhA3XUcD9giXSBIlqxmmwi/It3KQEf87cs8qwtbqqBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603658}}, "0.0.0-experimental-6067d4e-20240924": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6067d4e-20240924", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f469d1469f0dc03c05df1a7154fca99057d51f18", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6067d4e-20240924.tgz", "fileCount": 4, "integrity": "sha512-Xprt5PqHZKqF2H8Di7y+o9j1RTFsNGJ6ntBcRFu8kcChy5sVSVVIKXq+FBezcBhVChzaRrUb+OV/nWZlJH1aJA==", "signatures": [{"sig": "MEYCIQDSXSyEBcMqmojXoXnrgPzDLYde2Uzo5z0Mn8oWmiPU0wIhAJ0eI2aaemBYPvbvU1lMZiU/1D8JUiUyD4CGFUNWjMuh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603658}}, "0.0.0-experimental-6067d4e-20240925": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6067d4e-20240925", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a901c57310e44eacb37190ab5ec496b39a07ab01", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6067d4e-20240925.tgz", "fileCount": 4, "integrity": "sha512-PFMEEvTj2jWIPXh3C0PnDNhfAlQLJjwn/24PrvOzN8KIkQHfPawn95xIj0LnYyz/EnY6hYWNe45iGJoJYSOZ5g==", "signatures": [{"sig": "MEQCIAOig95G4Y0QFJSlAO8Ifqb0bXLwRSYxT00dfFXzo/C5AiBfDYlGUiII+DHbYwuLnQaHuGPIH8tlIKlxw2yyxHgGZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603658}}, "0.0.0-experimental-57db2f6-20240926": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-57db2f6-20240926", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d315f557da9ee0ea02f86b51c3b2d02d156c86e2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-57db2f6-20240926.tgz", "fileCount": 4, "integrity": "sha512-zbDaQPCUcTlPgLXzYMkhA/7ozI3P45vagP3Vc97s/ykEzT0Mi9n3Td4mCTRP0NiWt5iu31/iUDNQyfNy8Qn45A==", "signatures": [{"sig": "MEQCICf8I075EmYCc23GvG8W/fwnWMZSp4hgF157udqu85EdAiBS1OcfhVKP2LaLaoibYFGmmSRD4l5IwkAvNKAdoSyUsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603379}}, "0.0.0-experimental-b12479e-20240926": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b12479e-20240926", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "8.27.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^7.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^7.4.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4326c0d79ac65001f4dd6ab9bd29050a047ada6b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b12479e-20240926.tgz", "fileCount": 4, "integrity": "sha512-QoiS1tZsE7gDdd3EFLpprvM4edUBhNrV98El2G073Onhg2vJ+wub+RpEtztoNTNGrAwogj+CcFU9j1VxxzhiPQ==", "signatures": [{"sig": "MEUCIQCehIF6mrhqBoK9cniVBnJFk4ST26U2TBBiEB5ESo1lBgIgNQQOAEPL0enFtvFr4pZkIe0jsyCIQeBVdgcixxC6/PU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5603379}}, "0.0.0-experimental-3feb7e4-20240930": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3feb7e4-20240930", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "451bfa5c3bbf8dacd4b4baadc3b6165dbf6fe2bb", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3feb7e4-20240930.tgz", "fileCount": 4, "integrity": "sha512-+zfcqLj8SFwmt5lfkMCUfNgjxm4u2A072u9H0W68CEgQkmeBNrpHtTX/9s2BmqPx3XyejHFlV0oVcq8+ke8YSg==", "signatures": [{"sig": "MEYCIQDIiriO0WjnaZh+bx/9XL3qvtMdX8gxKBYxSSP/UmYSPQIhAPF7wIltEPRvp1Zux5+cG+DEHJGDhxncapx4CTSJdaU/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5072411}}, "0.0.0-experimental-b4db8c3-20241001": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b4db8c3-20241001", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "fca90197fb90550a6b69076ee5476f407bdbed65", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b4db8c3-20241001.tgz", "fileCount": 4, "integrity": "sha512-2qiuysJU65kKm4CznEMxpjvGV4FOlreXlpJwfJrOIqVxyQYL156614vzXFzuPObCsiwJrA5agXoAI9EP8zuvBw==", "signatures": [{"sig": "MEQCIFghQW/TTriW55zakNK3d6CLHCtRWQ8Efpm76FaymzqlAiBYVUXfMKpywuhyNQgwXPEm5fb8RyfNli8YurEjmmBM+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5072669}}, "0.0.0-experimental-27e0f40-20241002": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-27e0f40-20241002", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "72326f62e3357e88c3c88666167ce2ba1955c659", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-27e0f40-20241002.tgz", "fileCount": 4, "integrity": "sha512-hMOwSqoI0gxyjgGVhsMqPsVV4bjCqJ2WTUFsQyrk+KAkDqK/o7Th9XRl/xhVApPbE4VzQeiutC60rbBGqNebHQ==", "signatures": [{"sig": "MEYCIQCn5BDcUHqRKHuSfcdAr83pT9xXrIfCDYC6BUCjlPjspwIhAIKsIMKdRm4JQMyfPDhv1Yi45S97JGETDqhXI+0qfGML", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5084034}}, "0.0.0-experimental-734b737-20241003": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-734b737-20241003", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "eb150ec5566ff6331b4b45b562f998559168742a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-734b737-20241003.tgz", "fileCount": 4, "integrity": "sha512-jdcHsQwYAPuB2u/wpyCXCMI2B9n4weLAx8csvjNwYBw9drXYv4GmoxMyboigR9NJqDdcpIgjCBvt9qnIZM7AhA==", "signatures": [{"sig": "MEUCICRInF7XkT7ZQnrDOXg/LjoG69oaHSe29wV/FM3lhxdEAiEAufdscI3mtiNe+aoFVmp1wQxhjvrsHPUYUtyGpopwidU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5086066}}, "0.0.0-experimental-7779988-20241007": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7779988-20241007", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "90d97828df37fd26a3a78266f4b3261996a2f460", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7779988-20241007.tgz", "fileCount": 4, "integrity": "sha512-0rt6obfVntdm8qnYiGDi6DAnPAQnNAFm96iZCAy4ep5424PxntOW6B7em8TssOl6qr/+uldVaaSkMpOPqIfkyQ==", "signatures": [{"sig": "MEUCIQDSNRXbvqVxlcSq4WXNn2uG3YAbdZxotV3m5HUjJcPUWQIgarZA9F9HK5j1QTGme1CLUaWYMt+vZG6oF+wCEcmjGbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5087360}, "deprecated": "This is a bad release: please install 0.0.0-experimental-58c2b1c-20241007 or higher instead", "hasInstallScript": true}, "0.0.0-experimental-58c2b1c-20241007": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-58c2b1c-20241007", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "97ec2082bcccd4a3d9966cba1d428d094c0e2eeb", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-58c2b1c-20241007.tgz", "fileCount": 4, "integrity": "sha512-46X+FyBb7q+EVea7EFa1GNWpEkvE/ZNZr9jqHKfy5dOvusCJC8qHUzVRSMRikky73MNhLk9XziQ16v+mzs1c9g==", "signatures": [{"sig": "MEUCIQDl3cS5v8s+GgugD5rGA4SjqS7CksNwivE36bwYNBf8/gIgDNjdAiM4lSL+M2DZ0HDfx5QxPvbqiaTgyXyfIjP9M70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5087341}}, "0.0.0-experimental-58c2b1c-20241009": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-58c2b1c-20241009", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a840860c5da30cbc25db0671b9c715602539a175", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-58c2b1c-20241009.tgz", "fileCount": 4, "integrity": "sha512-/DSwpfz7c1hK5dpxxlLxQJtvXCF3RjN3ZCaJ43NM4BEvzTpaS0C0jasXVBEUIFumBcdaoirFbfZkyk9htY+6Xw==", "signatures": [{"sig": "MEUCIQDc/vZsxwtHdQtvBODBuZtdjPZnbrQxJoCA3v6lQwbuegIgKqHbZETwgeJ8hUEDRM5aOZSqCVJ7+ApBEpM2KJWJDX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5087341}}, "0.0.0-experimental-e504e66-20241010": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e504e66-20241010", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "aab06a32cfec0e58eca0ab826b39f6fff32f88a6", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e504e66-20241010.tgz", "fileCount": 4, "integrity": "sha512-hjASc3uqaXZGvxNkFaGrD6zzhn0t41tpmqWasxiGuvflCHEYgFoaPNi5wRzbZifo9RuYrwgUMTJRr9kh4cMsDg==", "signatures": [{"sig": "MEQCIG8ASojmyXQ5kzoVhzeFtVD/dW6uZ3ahHXSbN1/b8d7fAiBZFVQfaug62kqd3BWY+lmiXnJ2b8cJJiih3kXW6RIYsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5087372}}, "0.0.0-experimental-ad3b12a-20241011": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ad3b12a-20241011", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ad5ab73be1465addd12d298780e14fe64e3d11b9", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ad3b12a-20241011.tgz", "fileCount": 4, "integrity": "sha512-nKOKInm8musJDa45Q9rCLJ8H0PMw1hSBWjEWoTvm+jRnufOdck2mVG3i8e5r5VRJatP6jdfbfXo/Q9iNUotz8g==", "signatures": [{"sig": "MEYCIQCQ/+MaWIXDpWbTqAYkv0cTDxRw0qtPezfzXaJ5sdRHjgIhAO2mpuAX/oXOVU+tXHQAdkhvjvox9jInuaBXSG8Ut/uP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5088308}}, "0.0.0-experimental-fa06e2c-20241014": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fa06e2c-20241014", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "188f8b0699164fa054dfe417b60521e1c0914e01", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fa06e2c-20241014.tgz", "fileCount": 4, "integrity": "sha512-NZcV9zH8dtPdUB3cLUGuL9CCEBEnMqz5yW/WtK/Gy5W11NKw8TU7c2sGnyOSO4sQ6aOhi9aRZHGqpQWAznOcEw==", "signatures": [{"sig": "MEQCIAYNWIPjt2k1nWT2BfCJbQe6Zs1okvrK8Xs3m5Fcs/F1AiAeIdxflCm822zdDFvZh7xrILVsEPLJLvwVXhQ4tVu95w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5092736}}, "0.0.0-experimental-605e95c-20241015": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-605e95c-20241015", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b10ec836503b305645d411ae28cd91832ebe107f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-605e95c-20241015.tgz", "fileCount": 4, "integrity": "sha512-ZAaL18BPM4+1PhMnIUmmMD8xCZ2uk342tcoQezbUT1b6jLklfMkW3rNwH93fMnmZNlNkWHSyWCaYXzS7mtoXOQ==", "signatures": [{"sig": "MEQCICJwSau1Hv0W9wKsXFM0rIMIItWcMjEGDCj60/SStopHAiB6CT6C4bYxNX5TuadfTb4n/CyV4Bg3N3Asco3VDe51EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5092736}}, "0.0.0-experimental-fa06e2c-20241016": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fa06e2c-20241016", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "133a61b82807b9f8b83569dcbb4398eeff1dc21a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fa06e2c-20241016.tgz", "fileCount": 4, "integrity": "sha512-ASXNEtiyWcSnt38qTuu5972o+XYcz5pdkfvtLnhG1fglaQg9pFmfhC5274EH1QXxOFhgDOSd/foZ+H2o/a1noA==", "signatures": [{"sig": "MEUCIArbHoDA/NZUOMdBejkyNOVRRW+ArcYDFp18Pjmn/inLAiEA7+EJZyYpMeLBZi/eDbqZGS+aiV63QxwCmdpwMdW61tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5092736}}, "0.0.0-experimental-07a2ff2-20241017": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-07a2ff2-20241017", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ad6e80c9b5154b11d2ed0d55bda1df15828f71d7", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-07a2ff2-20241017.tgz", "fileCount": 4, "integrity": "sha512-eQ8jekF0kAu7Tsbh28Ff+gxmLim2FIri4Jhn38/qVt6llfayHE9tFyqWMSIHKR8UESmgZIb7ntCHDTjt+Yg03g==", "signatures": [{"sig": "MEUCIQCoKXFkt8jSAne8UCi5Wmbx8JMgZYuBsLJOOkx4+mFNrAIgQhfIfvtMVzvHee5ohI22aAJ/JxYaxvWHzQdHQNewDu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5092736}}, "19.0.0-beta-9ee70a1-20241017": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-9ee70a1-20241017", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "734036661d70e0d91c5f64414b31220ecc0019d2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-9ee70a1-20241017.tgz", "fileCount": 4, "integrity": "sha512-AkSce5YYHcreFtuvzI9xnP2kwoYkub8Go3yrz7cPbbCE6oIhFxESbPWJVgye7yZckXuzEZYO4JSE8tq/U0oVfA==", "signatures": [{"sig": "MEUCIQDl8L/N+6P5PGlyYEAY+HHhGxkHKPMOiGOzS79dBBPGfQIgR4zciEjPWP6NDA5+Kd05LQBX/J22U6LBROe+lCFQ2lQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102004}}, "0.0.0-experimental-7929cc7-20241018": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7929cc7-20241018", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8af8ea508e438ec02244b786bd17d499f339610e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7929cc7-20241018.tgz", "fileCount": 4, "integrity": "sha512-P3oLmfAqujpmVGYbycdwTh9BWfV+6qPMb8lC3/2buGP5PceujrXZmenLFqJELsjHCAtSmOd8FkpaGcBjooHhhw==", "signatures": [{"sig": "MEQCIEL72IgyvlVf9AavlYY1OMN90boYYZPh/S7IWgp54ywwAiAubAblmi1wAjGWVKzKIIfQla5tcYPsCQsVLD972/XpKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102011}}, "19.0.0-beta-8a03594-20241020": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-8a03594-20241020", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e7c76576f5d5ca1faa248163a34e1d7262b464dc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-8a03594-20241020.tgz", "fileCount": 4, "integrity": "sha512-Wk0748DZzQEmjkEN4SbBujM5al4q5TfRBapA32ax0AID/Yek3emS+eyCvPvb4zPddYJTAF4LaJNLt8uHYfdKAQ==", "signatures": [{"sig": "MEYCIQCFqDfcY55rRGL+Bgs/mYpuUsNKTZR6wk31azcLUq1wCQIhAKgC9hoKDcCF8UScUZCfsFYwsfRcEj/W4o+Bhk60CQwi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102316}}, "0.0.0-experimental-8a03594-20241020": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-8a03594-20241020", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "19dbfa9329c9399cb9d7095e1c1c8dc58d19bb54", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-8a03594-20241020.tgz", "fileCount": 4, "integrity": "sha512-+UrYBajQdL89vy6Cwd1Cn/37gNUnRqoRoAKW1WApQfviuyYNO5rVfocnzdxAK8zOYZlOISR61vbH/i18NG+lig==", "signatures": [{"sig": "MEYCIQDeFvMNsbSdZPSSMpRc0ne75pTWyASYsGz2rZzcwREJ7wIhAI/KbIub9MWhv29hG1KXCf34U7LIV/4roG5E62YZE/1y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102323}}, "0.0.0-experimental-8a03594-20241021": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-8a03594-20241021", "dependencies": {"zod": "^3.22.4", "chalk": "4", "invariant": "^2.2.4", "@babel/types": "^7.19.0", "pretty-format": "^24", "@babel/generator": "7.2.0", "zod-validation-error": "^2.1.0"}, "devDependencies": {"glob": "^7.1.6", "jest": "^29.0.3", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "@babel/traverse": "^7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "92dc40b36d63e2a6458d20f8bc5cf5ad02fd6692", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-8a03594-20241021.tgz", "fileCount": 4, "integrity": "sha512-u4+9KMH7BAKlGEMJQylr+po88/K+S+rsnujHR84744M9RBFbg9rcMjQVwkgLI00Tx+loLa/a3jwQ2ecHM4hvXA==", "signatures": [{"sig": "MEQCIGwqKVG2MTSpRfxTyh0KUylTTkESWT2SxYYY6MPNckHWAiBCD5ukJsv94bavuMJnK8LZy/5uexnUhslTGU/HrPFsBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102323}}, "0.0.0-experimental-16c1bb9-20241023": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-16c1bb9-20241023", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0b5e821d701e74d1f6cbbff971ac06bd77ff7cf9", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-16c1bb9-20241023.tgz", "fileCount": 4, "integrity": "sha512-pDeCoP1sPyEh3o+5im96xUtY6+dzK+AfX0xcXac8fxno35neQkpL2iyF9mYiaamgIFrSldjZfsaoegaIHGbsyg==", "signatures": [{"sig": "MEQCIGrHi2s103IJYJ+3+ZRlIbZNsmHIStxR3Qz+mB9X0hX/AiBWv3qE9a4KK69+iAHJkyWNpcAbQ+WUVST3zVLGFcmH1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102323}}, "0.0.0-experimental-34d04b6-20241024": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-34d04b6-20241024", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a2955bd04492fc5678c38f0141e727502161deae", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-34d04b6-20241024.tgz", "fileCount": 4, "integrity": "sha512-u0PCnjIWiwZ8MtPgatHSmLf4HU4rXS2ZYf8tqL2rMKU/3U8JQQ7sWvyJdDfZsqGVYkohBbpFCxEJ8AIeKs3yOA==", "signatures": [{"sig": "MEYCIQCWUZPJrcOhTKwoEU/QypMtyTLLTgxLWOkde+ITFwa5GQIhAMkTUoh7Oj+qG5CHc7TeVrxppt4b8/4zYklB5IMdftD0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102323}}, "19.0.0-beta-6fc168f-20241025": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-6fc168f-20241025", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b9cb5dfa309cce094d35c753e92cca2730fcfc39", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-6fc168f-20241025.tgz", "fileCount": 4, "integrity": "sha512-wFVeXhF0hkiRe4bEM0jzeTFMlMbcKNTwhXcFvqUIVB6WXf+3vdwOWGWnw7jwvDb2mzvsIZOFt/96itOFt1rwjw==", "signatures": [{"sig": "MEUCIB9dxn4FDN0nKXLDYI9bYtFN1GL2SrmGlxinkIem6CMuAiEA0CGAQGKeOUHVClMo1vBxZdgyE+PWuEkYhN20hbkXNeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102463}}, "0.0.0-experimental-33f03ce-20241028": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-33f03ce-20241028", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b53621d56331eec3bcc5079edb359e921d01b51d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-33f03ce-20241028.tgz", "fileCount": 4, "integrity": "sha512-mhRkCaWu04mPtIoayeoG7/BiPJeXwssTY97AOPE/4s98I4O3QKuJScGspovZTnKG9iA+JzWuOhS1b/z6o+wzuQ==", "signatures": [{"sig": "MEYCIQDRmrA98l8hlcVnF7GU35o62suSJAd9h9GkxfzIJl6l3AIhAPcQ5/dVLZo8Mx2RAdnJRqKEltweL0i98GPXWmCfGp2M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102486}}, "0.0.0-experimental-206ecdd-20241028": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-206ecdd-20241028", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "19.0.0-beta-b498834eab-20240506", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "19.0.0-beta-b498834eab-20240506", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5faabfe73602d3d172052e7a6a4d856cc8dbbbaf", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-206ecdd-20241028.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>B<PERSON>1ZIrjyedEhkYie5oCHpPj4HxS7GVkGydDBDdL7/FVxV7jxFerQD09g5LbxC4eBSpiBU59rv9o+1uYUKEIEQ==", "signatures": [{"sig": "MEYCIQCBQpQuLmbTxoydCKjdc+2pFzk8CLto9Q/OwV4fSSUvJQIhAImIsG4SRczOpwxi8/u3HQvh645Rg/WAd18osc214/nF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5102486}}, "0.0.0-experimental-63b359f-20241029": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63b359f-20241029", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "21ae05d469f4ebeff50d406f2cfe10a6355ff5cf", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63b359f-20241029.tgz", "fileCount": 4, "integrity": "sha512-2H6FBK/wgaM4cPjWAyXU9tGKapI5rwFA6EthhZlpRp/VnrKD+yHhoGh1oQcywWD8HFcTwc+J1ymGZmslBb1jVA==", "signatures": [{"sig": "MEUCIQCjp/EDYSSL/ZI4BMq8DPuzoo02+jgZaRmG3/AJQKH2eQIgIR5QLqBWzALnF2fWIja3fVJYw/DyItfBmJgVhLMRIsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5171280}}, "0.0.0-experimental-63b359f-20241031": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63b359f-20241031", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f01741aea38cccc738b94130dacad8aa80014732", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63b359f-20241031.tgz", "fileCount": 4, "integrity": "sha512-fqhyzL/qGICDouNtpYnC8+IJRSDw2FCfYaiDy33RHNHKBwmLBdNZ5X+Sb11z+O49nhk1EVcHMUvzDQUM8EdFdQ==", "signatures": [{"sig": "MEUCIQDeceQYHfzlARSUVvvIhZv7ET+BLuqte0lRfLO0TSjJHgIgZ1PKbG1EJYwUaIbxJPdIiDSpq6Dfz/pxs1+v1Z6mDtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5171280}}, "19.0.0-beta-63b359f-20241101": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-63b359f-20241101", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0f2fe28c0e32b9fc08bcc534da6a03f0ba2d6f73", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-63b359f-20241101.tgz", "fileCount": 4, "integrity": "sha512-qrmTHJP3O2kGbtL7kuySX3Lmk+5/4ZR1rHr8QhKa0GzKbmpAUGrTeWOg0NCI5t+QUfKKizxIO1+t0HsZW9x4vQ==", "signatures": [{"sig": "MEUCICX6xLcKFxfc+WLgGx8XJEY1S47KgjqFIQoC0zgnfZWKAiEA79t92G6ERYASYcwsZ+em8KgfNNKel9+vOMooUon+3UA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5171273}}, "0.0.0-experimental-63b359f-20241104": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63b359f-20241104", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b80528c41e0c55005c3d41be40a33cb4ce01bbc9", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63b359f-20241104.tgz", "fileCount": 4, "integrity": "sha512-fshuelATs860RUpHQvnJKZCSS3gUi/jeeNsPNpUrhh64AqpCTgNkE47I4ULZBlHwxvAWM0GmaPxHEIvctYvQ2w==", "signatures": [{"sig": "MEYCIQDDFKyNXnoS9c31Agr35kPBawJ90A1xUS14BX0cTOWpzwIhAK4Yj5JQYu4nWdsZzyF6GQc9uOXHX7srC4Qdq/rFKS1S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5171280}}, "0.0.0-experimental-b719367-20241104": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b719367-20241104", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0a5ec74489b5c66d605e77cb8662a9af14aa6ea5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b719367-20241104.tgz", "fileCount": 4, "integrity": "sha512-J9pDAmZr835csyjce2L8e5P1DqPKvgsTn7g8o61GuNN6mVNp9MPr8aBZp/vdy10yHvH7Hkae4c5POlQZ65lFxA==", "signatures": [{"sig": "MEUCIQCat3sbYelOVrsR0DVUZnIOkvrhS3d6S0C31iFSLzIK8AIgYULZYjgxw3pDOTfivQZHFbcgt+oZBD1UaMTn54KGxRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5178692}}, "0.0.0-experimental-fcabbc1-20241106": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fcabbc1-20241106", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5c5f62e0152d83e4c1dfaef4e6b2d063b70c941a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fcabbc1-20241106.tgz", "fileCount": 4, "integrity": "sha512-wimP5cw05IDEcHE048d3DT0R8nRA4GxMps+6FKgoetUQmzaQMV0/nc16uGOAZu35QE9oCAGviucKwkZ8VHMfjw==", "signatures": [{"sig": "MEQCIEaEtZU6kDnB8vIO5bpHmK3cbJoC1nZfgsIeXDhKDkICAiBVNMBDUts1adljthfWbGWJXxPpzeuHyVlBt8UvnSWZ0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5133563}}, "0.0.0-experimental-19cbb8f-20241107": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-19cbb8f-20241107", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5349da31b1abe9b57e7d8e53edddee1b6b41ed63", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-19cbb8f-20241107.tgz", "fileCount": 4, "integrity": "sha512-CqdhUe33pDxdUB70hYBdrdGPbDr68yYyubMhdepMqCLSsc9zcmNmF8txEbGjek7TrN6HodfgRUBs8c7DZ2upkg==", "signatures": [{"sig": "MEUCIE8nI2rLxJP00jQrk4cebxmsAOwJgAxM0DobKcXl1V8rAiEA6qX9JsGe3Qy5x2Wil1BZuBQRsBp0LrcLPUJU+ZDPybk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134930}}, "19.0.0-beta-a7bf2bd-20241110": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-a7bf2bd-20241110", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9e7abf2d9b6d0908cca7df010695678b830b36ae", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-a7bf2bd-20241110.tgz", "fileCount": 4, "integrity": "sha512-WdxXtLxsV4gh/GlEK4fuFDGkcED0Wb9UJEBB6Uc1SFqRFEmJNFKboW+Z4NUS5gYrPImqrjh4IwHAmgS6ZBg4Cg==", "signatures": [{"sig": "MEYCIQCP4PCfc8dQRTi5mF+sPkJ1nY+PtdBY1H1muqIFrztVxgIhAP/FB/juMD2MbWzMSGP30gxdro3LUNTjwwtK/2oRu4+L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134923}}, "0.0.0-experimental-a7bf2bd-20241110": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a7bf2bd-20241110", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c7ff6a0c0b3be32c0a3e6f6b1805e42a8eb22180", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a7bf2bd-20241110.tgz", "fileCount": 4, "integrity": "sha512-d6IxuSpRvV+8QyaQmm666XLfJC0rbTJpm37z/TCr4ItrmGdTDHkKfNJlCKUDJEZR+n/PBsgNhlamqXehLbOy2Q==", "signatures": [{"sig": "MEUCIQDHYFUVoYDg5m+ylJV8wGlshYRqfmktGoOGLM+rTrULPwIgGDBGcBXBOPKkgF7rAwqpEgQP2+xZNIi07Y3tW7K/DEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134930}}, "0.0.0-experimental-678214a-20241111": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-678214a-20241111", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8d042780e132df14b3e395a39664c90aecfea6e8", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-678214a-20241111.tgz", "fileCount": 4, "integrity": "sha512-8jr2h8Zzo0Pjmp53Xfziax8Gt15UqtlDg1x3ZqaNyh8jdrIvLN4vVA7/dHmROEnKf3vcf5luflEYcUSuNK4OAA==", "signatures": [{"sig": "MEUCIQCJ1i8CmjT2NLi4CKElytw1dE+R8+ZK/urnCLnzlklmDwIgICciWvZpIrqf1gcDw7uazi0NTbSpoAez2MMtmV4CsUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134930}}, "0.0.0-experimental-19c7e06-20241113": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-19c7e06-20241113", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ddcee0c9c7b1701c15ba4bff42d86039117692c2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-19c7e06-20241113.tgz", "fileCount": 4, "integrity": "sha512-0pYT7/PeJ5FpKJQOF1NrMNE8a6tL3ncCoRvdRW8w84RDPe+klLGkyksqcYz3J6dwpF9+d7ECzJRIeaQkmviYfQ==", "signatures": [{"sig": "MEQCIQDqSDiF1DqxGATe3w3+Lp7Uh832efZdBSbekVNjiyo+4wIfIlukDLZ/oj8js3B9Z3i0Y12OBckMob4G6x45tN9s6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134930}}, "0.0.0-experimental-702922b-20241115": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-702922b-20241115", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ad87af2341ba284dfc0f72199c08c80ff57c5977", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-702922b-20241115.tgz", "fileCount": 3, "integrity": "sha512-yW9qr9h0Sb5Q3oGKI8OYtiag/EEo/40hYFjyrsHB9mPr5KxQnLvsZtCXGiAuzsCztovtHrwu7nm5/9/SCkhlJw==", "signatures": [{"sig": "MEUCIQD3ulffl58sFYcUj2NffeCxQ3TOkMdK19rM3YtCKMYhSAIgS4O9fto0h0DQp3hVCsSJMzFvuRDsk6ajzIvQDytlTCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5068730}}, "19.0.0-beta-0dec889-20241115": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-0dec889-20241115", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e2ab74f777e46a3e53f69864b0aa24d5af0ab8e3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-0dec889-20241115.tgz", "fileCount": 3, "integrity": "sha512-sB912UbBt/vYl+6w40VO+LzWVnAt1Ty/XCDKs2qT5Fopljm+WpJNHVVkNESsX0IjU3sM5T5KvN31uuPpraUi7A==", "signatures": [{"sig": "MEQCIACMy3mmiQ4O08Z5th8l9rQf57qCFyJYZMd6NEwr/Jj+AiABNiWw1tm0l78OwTOm5p5SoCMFAtqC2Z5BRd09Dp7WBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5069925}}, "0.0.0-experimental-ce0575a-20241118": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ce0575a-20241118", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-0bc30748-20241028", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-0bc30748-20241028", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b1fa33049c2276562c09993edb6255a1821d2b81", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ce0575a-20241118.tgz", "fileCount": 3, "integrity": "sha512-giVB5Rk+5q1T2RjQWUEE5EUt3eOHDcLI6JT5wgz2Ydqnoo7b0HZ6KKV7BlaONjhBfBpMkZrTUv/kqwR2EXlqsw==", "signatures": [{"sig": "MEQCIFw+0HuZ5uZlr1725+Eiriskw2qisHRfh4L/lhDTGq5OAiBIS9agN1TqDcD7F6CtkVmN+6U9rbtBcR3vr4IBwJr+kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5070314}}, "0.0.0-experimental-dc8bd44-20241119": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-dc8bd44-20241119", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b54d594d3b2243e8d3bc77e189ad3e098326ee3d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-dc8bd44-20241119.tgz", "fileCount": 3, "integrity": "sha512-xmvZbu36kn/tTWaCZJfB6a2zwwgayLSvCEXjZEAk9ej0Gt1q6+RNsGkOgxEP7AtuKS9LGY9Wyin3abx0JHaLoQ==", "signatures": [{"sig": "MEUCIQCoadp7pWEY/hq9+K6bUqlN9YyVgCFldo77i4B9Lrt9GgIgM1uJYM8gj61CHQpzfnNkUCbt/RKeyqIHhvpDZa9Ryz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5071248}}, "0.0.0-experimental-dc8bd44-20241120": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-dc8bd44-20241120", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "571ed97dd06e5abd32b24f5e778d27cf4f21fc01", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-dc8bd44-20241120.tgz", "fileCount": 3, "integrity": "sha512-9kCpwDJtmg9RYU10d9WmOjOFaMIBCMyQ4KCJ86MpDZj8dEqi1oSKPWtne2pXumxj9KAWeZK3w8zx15DkAVhhBA==", "signatures": [{"sig": "MEUCIFugvPT7kPKMmxxvcF3AVpI0IApY7QzlNnNOjSMwOQheAiEAqLjBC0kTjU3Ti1GT7RWUcPhQwB59lF7q93OFt6cKBDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5071248}}, "0.0.0-experimental-dc8bd44-20241121": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-dc8bd44-20241121", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f583b8b41b2a405588122daeca13d4e951c68140", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-dc8bd44-20241121.tgz", "fileCount": 3, "integrity": "sha512-8aAhTSOGgeD0e3UwHuIL7YRBBjGgiYxZ00Kr48CTiGnq++U/X3WCZ3s1k1wwN475/haFYNSq+mGayOBRlcybAw==", "signatures": [{"sig": "MEUCIQCioZ5p1fYiHqeiDl6a5uCGc/6CpxucWJoEB4WuK/HiawIgG642Lb+nNtqXHUYHYrCa8ER1HCBgJpW7qNeYAO7/C0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5071248}}, "19.0.0-beta-df7b47d-20241124": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-df7b47d-20241124", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "1da10ca50123079458f957956db1070cf22624bb", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-df7b47d-20241124.tgz", "fileCount": 3, "integrity": "sha512-93iSASR20HNsotcOTQ+KPL0zpgfRFVWL86AtXpmHp995HuMVnC9femd8Winr3GxkPEh8lEOyaw3nqY4q2HUm5w==", "signatures": [{"sig": "MEQCIFdCKBbRCPAxzNpQTg8lnGdAgudP1L76IDY8vCwAV+ISAiAg23rN1vAX5ifKAT0TfnJk2jaS/QNfHLhAmR8lTJPBMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5078184}}, "0.0.0-experimental-df7b47d-20241124": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-df7b47d-20241124", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "eb74aae0f433217d9ff012f170c689eac11e2192", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-df7b47d-20241124.tgz", "fileCount": 3, "integrity": "sha512-N/8wJ9kQgQcudapIj6C+M4trt2OaK7w8yJHL0BRYp31z8VG3sRTQsyOnzG/tNQbviG35TfMSDwrMQUOLJlnIVA==", "signatures": [{"sig": "MEYCIQCGseefXjcOVE1pbh8veoGTPwSaY20mxEkjFMJOi6Cr5QIhANcT7AzCRhi93APuLZ/7+2C6vqFgxp6bkjkTo2sHMjxs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5078191}}, "0.0.0-experimental-df7b47d-20241202": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-df7b47d-20241202", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5196b0790835daf3b1e92f209d70d5d769239404", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-df7b47d-20241202.tgz", "fileCount": 3, "integrity": "sha512-q8PXaMas2d3OgcXZHKDFiZosITg5mANI6s3a0PkjOQsIpZwaYjl5JXyQAvRJJV8hEo8oZ41QpSTd5ojINbphUw==", "signatures": [{"sig": "MEQCIDAlbM0mIPPWYnAiX6jNcml/G9MR6aYD/2DIQ7rfisp9AiBSdNSdzg+TSN8OIRWxeB5MdSAnXoj3bWqcQY/5ywN5Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5078191}}, "0.0.0-experimental-3ab621d-20241203": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3ab621d-20241203", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c2930200bf32b09be013415edab944d21c3648a9", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3ab621d-20241203.tgz", "fileCount": 3, "integrity": "sha512-kjL1XAttHeew5vbu97/Z+ObwUwb0pcyZrl9LzQmUOYFhNIhLw8NbgukDNUZ8QMjEBcjVpeDfz6EU8NVfCsDx8Q==", "signatures": [{"sig": "MEUCIQC36s0UP8d8qPIyump/cwdcGdezUnBp1nIfV9swXsP6nAIgAtPX6yl1fGtQoNq+hG7+FZTw2Q4e7vJW3vGm4QD5h0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079069}}, "0.0.0-experimental-37ed2a7-20241204": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-37ed2a7-20241204", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7f4c872e5fe3261087a037afc39d226d456839b3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-37ed2a7-20241204.tgz", "fileCount": 3, "integrity": "sha512-jFz7znvduS/sQqp+kMgxmtNz4k9vLvvNOSEdYXn+psSv2mjkEgk5GDliXGnSTajEoOYNqy3nSut8qhS0aI4Z3A==", "signatures": [{"sig": "MEUCIQCRLtlyI6TYVRa8fKCV7cGm6VHh7CM1Ll/SiVrLIQq+WgIgY8cwlYT6+StFnr3jsqGmT5h4RXIFSqdgh459NIsBSl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079506}}, "0.0.0-experimental-54240be-20241204": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-54240be-20241204", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e16f1685f4860a302d88d842e0c95ceb38c76d19", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-54240be-20241204.tgz", "fileCount": 3, "integrity": "sha512-oKLNtUoOmmh7xRYSbvPknNa/igZaNHNFcpggROdH+WHrdUsjEGE6zVqtsjv+U6qlsu+CLLo9oW8JTq0KLv+JTg==", "signatures": [{"sig": "MEUCIQCY4PJOqcz/jTxUHJJz0bO//bPE8s70C/vzM/aSVGbgTAIgBx8cKXy9qlDhA2LaQTCL89hUt+ibcXYWjfdrKSBdvU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079506}}, "0.0.0-experimental-50915ff-20241206": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-50915ff-20241206", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "457f98f2d41c9503745ed3f26c4ff517fbf0496a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-50915ff-20241206.tgz", "fileCount": 3, "integrity": "sha512-WB87NLnMz1+i/RM0s7+MxbUAxYmvVQHdaKlP+YwxEwYpifAjLV04590ZXaSz4qx32/MflYbhutn1Y683cpNUOA==", "signatures": [{"sig": "MEUCIAoSF12jaREbL84hrX+rg0XkgAWVet6idA7HlZaIQFKVAiEA7uW3Y+1F2WVGqQd+FkczzF4vyTZml+mkAKvIMEBJWqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079506}}, "19.0.0-beta-37ed2a7-20241206": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-37ed2a7-20241206", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "06c72781834e8cb0793de815bdf99afb5c71d6cc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-37ed2a7-20241206.tgz", "fileCount": 3, "integrity": "sha512-nnkrHpeDKM8A5laq9tmFvvGbbDQ7laGfQLp50cvCkCXmWrPcZdCtaQpNh8UJS/yLREJnv2R4JDL5ADfxyAn+yQ==", "signatures": [{"sig": "MEUCIQCYgDf01kzgH8X9xNLYjg/HCDLcLA4O2LaGN4QWICvZtAIgTjv6ObAn6+OgQNx+sypbTV/iSc3OMXGGw9fE2TRgVQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079499}}, "0.0.0-experimental-54240be-20241206": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-54240be-20241206", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "840841abcdeda2b67f14f996ef9557788c3299d8", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-54240be-20241206.tgz", "fileCount": 3, "integrity": "sha512-c+rRp4NfcyP9EPmsozWoBrPPp5X4jrIIT2tsomzsLgQVt2qL2nQ8e3BiISoi05S/hZoNLijAC+T07irQ2qD6mw==", "signatures": [{"sig": "MEQCIHDtm/YmscHqyLtu04cL4dg+OtSLw5HdjBzS0Gblc6hVAiBE5REFTgPhU0z10pwMhUEq2mpoXCHiIcGxkFR8YvlREQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079506}}, "0.0.0-experimental-16e4ce7-20241209": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-16e4ce7-20241209", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9fe704b6e20da7c326ab0aad64b2988cf7b137f1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-16e4ce7-20241209.tgz", "fileCount": 3, "integrity": "sha512-AJGwHi6nfNbXpQO4dBMVZRTpdVn1qtXs590PwJUo0N2AIKmkZOiGipfS3bEa/P4IgpUrpzP1F1WHQNSFn80NJQ==", "signatures": [{"sig": "MEUCIQCF7eG+BVldXes/YLQodyf0zNMUhK4QSYvJNcN+R4fe1AIgLuKJ7My+jtCZaKKax9PUDfIRL8haXASl9l5vHEu6meM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079849}}, "0.0.0-experimental-ba7b8e5-20241210": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ba7b8e5-20241210", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ea2e41124461ea91f354fd0b33c79d478de9fabb", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ba7b8e5-20241210.tgz", "fileCount": 3, "integrity": "sha512-NLgee8YgT8bRoLuayYa6Jrj3P5TrKw/RLzKkYTYmTcR+Y61bnJWP+h+xw/zWjlya4vcC7Z4v5F4vAIAhEF/thA==", "signatures": [{"sig": "MEUCIQCPo8mpufKceeq+dxLl7hiaCL4pRuCo3x0Tx2C8nxRUbgIgLw0SvNdBbLVUR6dMQ2bpqTBPTnG/xCIHx5bvktKGySA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079813}}, "0.0.0-experimental-179bb06-20241211": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-179bb06-20241211", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "eb21b2fabc26c9aeb4a3c3eb6d10d2b8e14cac2a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-179bb06-20241211.tgz", "fileCount": 3, "integrity": "sha512-5hAcV4yxvSM1Ewul2x+wui2b5I0Cp1E3w5jZB9im5qQ3leKtwb5+KiITuZgrVWxjIAJ0v8u6SicM/8i1/a97Zw==", "signatures": [{"sig": "MEQCIG+5GRJXALwyPVmg4qwgXWCnKvDntgnWmb6TjXtjQ2RPAiAOht3Y36CZgtv4Rv8LIvDd75SA+f56viaNKWwadxN1rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079813}}, "0.0.0-experimental-ba7b8e5-20241213": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ba7b8e5-20241213", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3bba3950a58c4dea38974bb50beb47a778d24b36", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ba7b8e5-20241213.tgz", "fileCount": 3, "integrity": "sha512-rlikcZmXljMAKb64j2W3+UuWpxOpyPPUW768fXA8f3QksCoaRdYMUnKnAEjETJ4AAIJQAUOLzbhuPmmvQVyyKA==", "signatures": [{"sig": "MEUCIBVJoi1cNYhmWT3hXrzHxhZWu5anRsRjvFAmpyzIKy3OAiEAuGJnySztkA6V/3zi/6e9rMTbGxa4e8gqH9iC+71DLo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5079813}}, "19.0.0-beta-201e55d-20241215": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-201e55d-20241215", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "2cc73c632f20e7dabb2de2c677ebe92b95e5e197", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-201e55d-20241215.tgz", "fileCount": 3, "integrity": "sha512-c7YAJNlA8kSoIFx2Buq/CeGmC3MbZYznD3H7Q3cdaRyKtwDSSxbM3VJxEhv7lEo64g48aWSBSiCI3XcRw0y/Jw==", "signatures": [{"sig": "MEYCIQCkRxB0sl3mRiQGDrtt76uO0GpbOeWynwphK5ZGXNkkwgIhAP1tDmsZuM06KJCD+K3r451Bs5UWpUAy1p1nQLnPDIDn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5080515}}, "0.0.0-experimental-1e8c174-20241215": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1e8c174-20241215", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "glob": "^7.1.6", "jest": "^29.0.3", "chalk": "4", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0973751ca5ad06ed474b2a933d7394dd8210f5c5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1e8c174-20241215.tgz", "fileCount": 3, "integrity": "sha512-pZRmAn4+YjHEgiQ3ldWFBUhRaIa/VJHEoHUI2Ab7L6rAukpoOCSoveSAXHGj+T+qfbiryIHWDWl2fGHc3j14YA==", "signatures": [{"sig": "MEYCIQD8gasIKfa0G7XhI20HP3ikla6tzEs3tghuGKs+utq2AwIhANF7TUGZfdILqFL5ZWOqYQ0kRD8ziP7gSYyf3k/gyPP3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5080522}}, "0.0.0-experimental-22c6e49-20241217": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-22c6e49-20241217", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a3890514e93aaae1d2ccc3d10ebab2c10eb7922d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-22c6e49-20241217.tgz", "fileCount": 3, "integrity": "sha512-9HqVoSY0q8sOQGMIjX880OpkjrdwW6/83DQ7jTEO2OgHInTiSOkiGKvuRI2l9/uZ1coeCoJ3ii14MuZjSQyNDQ==", "signatures": [{"sig": "MEUCIQCFY3LzjO4ceeL9F0hs1WyHnv4WZyV+TSWUROrkoiEJgAIgJ7Np/Bext59V2taynx6yfh0xeYUnIsbp4f6maSuDxM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038096}}, "0.0.0-experimental-22c6e49-20241218": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-22c6e49-20241218", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9a5b4b3d3488ae5e1cbb875c013aa6c55b842c4c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-22c6e49-20241218.tgz", "fileCount": 3, "integrity": "sha512-yaNTyvxNR/YJ1BF2Q4zG1HZuvmJsNoObcYf8WfrwOS8hWzuDo1S7+jtADvUaJwlb/ANSZ+xM+HxX4242Q+b+tQ==", "signatures": [{"sig": "MEUCICWyZU2eEYFSBzepaPP8mL48LI+9bbQYDM82Qo2MLOzrAiEAsi7WeJ99vQx1WNIsZTK2dRpkYiH2bGofx3icpC7sWRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038096}}, "0.0.0-experimental-22c6e49-20241219": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-22c6e49-20241219", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9359623a12a57025fd66d648123aa9319a69411c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-22c6e49-20241219.tgz", "fileCount": 3, "integrity": "sha512-jxp6eD6SQqSA7HrhVewq6NoO6tW8Wlgo+gpMf5ovuC0UR086tC9SOHV2k+1Uae/jKiywDfQX7UEyWaU6SQ69PA==", "signatures": [{"sig": "MEQCIHvw29pysB4EkSQ91nr+vO4wL9UkS756jq/qCqEWhY3cAiBgp4v+bAUNpZnUIk6VQG+vFyjcczXirBtUr9mAI/vB/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038096}}, "0.0.0-experimental-22c6e49-20241220": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-22c6e49-20241220", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "783602ad102efa7dc1bd5e1255a46752ab8e6067", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-22c6e49-20241220.tgz", "fileCount": 3, "integrity": "sha512-Wu2gjDFgNGTQTlxjlwSshZ0wHqnC55sI/LQ7SnbImKpY6z8LR5kmjJq89MUEoEiVmSdrcqUgfmhap+79qlbc+A==", "signatures": [{"sig": "MEYCIQCOnYO5bPq0YbcQoKJRcyESukFd1wS9QJDuZ/ixX8SJeQIhAKx7QSr0VkJfuCc3udumTXXPwkyq0UFc4Ps7tO+6SPKr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5038096}}, "19.0.0-beta-b2e8e9c-20241220": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-b2e8e9c-20241220", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "088691bc21d1917c53ad43bf069f818d423e641b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-b2e8e9c-20241220.tgz", "fileCount": 3, "integrity": "sha512-aigv5VrOTLUOCeq/t1fuZvvs9Ze1GUfxnleWfiGoZcR0Lo34w3JcGgNiJBoxqlWfFiVnZhzaja9Aa1p6PWAtPg==", "signatures": [{"sig": "MEQCIG2GpVqe6u2+OLfbhU5fdMW8SAddxDL0s1g9wfh9g/IuAiAU8PzWd28Q+crrCTyKghxgZGThWHgH2yYK8ipuHEfiig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059771}}, "0.0.0-experimental-b2e8e9c-20241220": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b2e8e9c-20241220", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "09302724eac4be697e5abab18e51b1374211ee60", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b2e8e9c-20241220.tgz", "fileCount": 3, "integrity": "sha512-xjFGfjG0bRxOmTxNPUXhpT1dVwY/AydRbDtJfHmdC7iDte66KIwW+QNAMpDfp85TQN1nCT+h24Vz2UzE8bAbVA==", "signatures": [{"sig": "MEUCIQDXncoeD4vITfzfBixfONe3L1e4UpT2nJ2zzVp4/3OopAIgA81Ubzh5TcEXQ3LC1m8KkF8P5kz2xXAm2BUiUV4aT7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-b2e8e9c-20241223": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b2e8e9c-20241223", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e89825ed3205b871071ea67da4144b0a165c8758", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b2e8e9c-20241223.tgz", "fileCount": 3, "integrity": "sha512-fNgAPUooj6OGhcAmvX0+Z6Q+kptpjqYfo2gWtCFDfBkXLCyG8HWTk2E1XPMgs8+UdPtFRAFrcEltfNDR3hseUw==", "signatures": [{"sig": "MEUCIQCq34lr+vWMWtZBOOIra7fnyCGooF59aM1QPjAhzq4XSAIgeXd7Zld/QwMnQrooKmisy2mB7TS9GCftNdj6QBmSfQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-2689395-20241226": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-2689395-20241226", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9e238385874e3f8f33134ade7dd25bffb27f6674", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-2689395-20241226.tgz", "fileCount": 3, "integrity": "sha512-NqCOh09O7HDvVZv49hQFmTwzaHbhJHQnOMUeYBhrqnlEx6vd3okAHeq/W3uqv8dEcpJJYbh1est95Hy0/OQhxw==", "signatures": [{"sig": "MEUCIHQzlwpv8jYRA+Ibt+427ZxFHp9CBr9Bha/gy8/I/bsRAiEAtdL/go2giXCcy3H28BAFL4kIu97fKFOAhPm75So3mD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "19.0.0-beta-55955c9-20241229": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-55955c9-20241229", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a1df76c3e3217aca88833a01dfac4bbcc7c43309", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-55955c9-20241229.tgz", "fileCount": 3, "integrity": "sha512-APpa9fRiG5UN5kxnB/vznaSBKbXwAWZs6QshN3MLntzWa4cUhOxzUSd7Ohmr5sLQaM0ZHjjOg07pw1ZoR7+Oog==", "signatures": [{"sig": "MEUCICEvfjAZt/HMQS+jar+/k0PL57fuAdk54pqursuGXo57AiEArTaAjNB65gRfRB6jBHpIIUhAVoxbGr3CvfZu49NQ5kQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059771}}, "0.0.0-experimental-cd3852a-20241229": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-cd3852a-20241229", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f7c7ff181ae18b6a705f395288f80700c0498a23", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-cd3852a-20241229.tgz", "fileCount": 3, "integrity": "sha512-6qZBK55M1qrGXVj1joqPjiNnaYd2tBTnHHjirvJaqdrfPPzgT0vFT/LpMJEtcg/V1uy8tm0lR8MaLJAWeiWA7w==", "signatures": [{"sig": "MEUCIBttpN4evIn6INrnIQTMALwP99NM4n8czoGlmqGSqCXIAiEA4Okl3ssWJuKgtkNkOwTLb3zUrs0WWxfJXodofQuNB/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-38d0758-20241229": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-38d0758-20241229", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0eb728aef255614955679597aad0d7217ee938ff", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-38d0758-20241229.tgz", "fileCount": 3, "integrity": "sha512-wR2JKfcXmI89IUHQiHQanFhqsVat1oB/RreI8m4rX+FHvJKUFWev2AJ1X0cluf18Vzbf4Y7HwrD9fQGNVHZEtA==", "signatures": [{"sig": "MEUCIQCH3zLi6XsVi5tCW2/o7rDnBVcnR2TAZVpKCIRsipwnhAIgaaXkEgjm0lYW55a843ndTDjGEl5PyAS83ATBQ+d1Qhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-cd3852a-20241231": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-cd3852a-20241231", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "550efdaf3b17ab2262935c29e222b95490a5cf74", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-cd3852a-20241231.tgz", "fileCount": 3, "integrity": "sha512-TLY6aD7KFp2z2Wdm/7IKKuF+JneOE+r50A7RWBv8o8cPYKqjUGpiypfBTuwyNJE9SKnwXiWgBItgQWDXa2LdZQ==", "signatures": [{"sig": "MEUCIFoRToxQVcKqJWWyVqfMI7UI9qbHptPRvM2TA3XNFw1cAiEAgR3dboLcXPt9cKVi7TyU2MtKQtNYDnZbCJ/d+M/Gskw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-cd3852a-20250102": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-cd3852a-20250102", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "87a4984aabe563d8d2ba3b2d694b3ed29f6db9b8", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-cd3852a-20250102.tgz", "fileCount": 3, "integrity": "sha512-DD117i+uimB9q3k/u801OuPZhLwVNV/OVI4ouXoICx5mkJdg+psTzame1cx2uqaJAx5hB0pKnBz8+qUxYgkwwQ==", "signatures": [{"sig": "MEUCIDGORDGrrr+3DLlOgk+qZ82tmngDFgwNQqrytD0SIhcEAiEA7lq1PSZftepUwEyg4gcuBETjVkLHHlXham/YKjzbAlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5059778}}, "0.0.0-experimental-63e3235-20250102": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63e3235-20250102", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "adece4a45b63d06701460fdcde6df32bfc6baafb", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63e3235-20250102.tgz", "fileCount": 4, "integrity": "sha512-HeHMBKEnKr7UmTkKxIVfwnCbxvKexB9d/wFLEXnUxLOMeRsQumxX+3ySpwlRzjxjqxIPz0nG2daTIPc1UsxNGg==", "signatures": [{"sig": "MEUCIEwcxNeTkALBw49fKsa1d1Y2ayoNqVAA8FY+/v68SC4HAiEAjo+L2uBVpbq+PtcddQ5MzPF+99GzPvlouPthysSPD94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15703035}}, "19.0.0-beta-63e3235-20250105": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-63e3235-20250105", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "25d5c6313722adc82582b21e169dd984e94001c2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-63e3235-20250105.tgz", "fileCount": 4, "integrity": "sha512-38qEX4e1nNOZ9K7rVAF4VVijcjTHBbRHd+ftYpfim2Oabitd1NGjvrL0bnwFymDTBB4MqswqIHTYjYfuy1OeTQ==", "signatures": [{"sig": "MEUCIQCEk3tTgyiz4Lv8xB8FC0xXXUBXvwtLocpwztux3KTxiwIgekcXnc2VaWrxF0JQ2enhQ8StBlvAfu9iDhhXt1dJ57E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15703028}}, "0.0.0-experimental-63cca73-20250106": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63cca73-20250106", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "28c70c7e25dddafe5f574a9ec4b0911efc73dc65", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63cca73-20250106.tgz", "fileCount": 4, "integrity": "sha512-XYdCAnSJbMGLlrbxI9jCKsM+pIVJnbeHkNJxqIT8jTzw1aHXO6Udv/WCsIQvGdwpKsl4gcDdtRYTTgpoeeOSJw==", "signatures": [{"sig": "MEUCIQDHJI5IYjFqQwIItCtOl4pYqYNx0wvbtGGPvI+lzMg0rQIgN6YekYAHB9rM7iHVPHC6nAFRufif3HVtAykOClDbAfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15619161}}, "0.0.0-experimental-63cca73-20250107": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63cca73-20250107", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d7868f50157494e4c2fec2683ec23f26c471911a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63cca73-20250107.tgz", "fileCount": 4, "integrity": "sha512-b5mkI2c7IMsJCjKy/nbT4klHMVPSBgelESas7jSmrs9wioksJCqGSiBVmtPyhuGB99JUKpud2ZMUmrRWCBCWMg==", "signatures": [{"sig": "MEQCIEybce5U7nuZPHwkuTv/qcD0GbEoQzI2aKhCYTJDyZj3AiAXrYDq3wU6Sov42VLSU5ym9a+QUpZ0BHBdHSz51hgtpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15619161}}, "0.0.0-experimental-1e6d215-20250108": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1e6d215-20250108", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "02a988ca7654c22927b94f1f8a0a3a343efb59cc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1e6d215-20250108.tgz", "fileCount": 4, "integrity": "sha512-OBUTojwxoZnIun8teBt1C9t9tz//yhQPY5KyCtcQAoTfIDGEWwT8gN2tcB8kRiU5QFK75+o9BBymk/weqKg4rg==", "signatures": [{"sig": "MEUCIQCMwEAmbH3nkIMtq2nItupOGTTeD1L3Q5ZYhIJVKyRIlgIgTMdv2xfrGnoqwTrUXb/nobXtyId3Ci1YdDmcA9/2N44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15619161}}, "0.0.0-experimental-1e6d215-20250109": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1e6d215-20250109", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "585acd76cbcfecc6488f572f383017c6294f4406", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1e6d215-20250109.tgz", "fileCount": 4, "integrity": "sha512-RBCzOdNXw6riq6Qnc1mvZEx1TDEjjcv4jBYJylL/d/6eaWA5rFx5msgl9ql7mWr8r5ya/L/CgiJ33mBkI59Yjg==", "signatures": [{"sig": "MEQCID2Ab9hbgq08At1EEUNpwk8lFrjRRH0fVNvu1SsG2mMIAiAAvftcfW8RJZk5/SMXqvM67Wtpvs/ueIUA663SXXT52Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15619161}}, "0.0.0-experimental-e552027-20250109": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e552027-20250109", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "5a5abe7dee4867349c63a61c065db2d4cb6df7ce", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e552027-20250109.tgz", "fileCount": 4, "integrity": "sha512-X5m5/X6rCZYGDI7rrlEXtgOzbD7t2u2wfliP+aJOXx5UK4f0/aCRjp3X/yppMOivamhCwFGL3fq864I3Kvln4A==", "signatures": [{"sig": "MEUCIC+DuZ5zeShXRAGgNqSNxHY4HO5B0Rnr+PgzeUOWA4zUAiEA+tnggXjLDcDDp/VWa0NEhBf7yY3rAVv5/7FwC6ols4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15621835}}, "19.0.0-beta-e552027-20250112": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-e552027-20250112", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f06f0436420bd09df5abf37337ecd8fb43b0d847", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-e552027-20250112.tgz", "fileCount": 4, "integrity": "sha512-pUTT0mAZ4XLewC6bvqVeX015nVRLVultcSQlkzGdC10G6YV6K2h4E7cwGlLAuLKWTj3Z08mTO9uTnPP/opUBsg==", "signatures": [{"sig": "MEQCIGhrMWG5EgzZzyUwn09h6r7yDOPrExjFfpwcH19us8P+AiBsXDnMWZgrgHiQb8r61PoG172DOaIOAYtjz/BHzo08lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15621828}}, "0.0.0-experimental-e552027-20250113": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e552027-20250113", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "47428295834e64a334b79be62efa3e390fbf43d5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e552027-20250113.tgz", "fileCount": 4, "integrity": "sha512-Q8Fh3L4d9UeRHo0ckoJYxC6Q9/wYF9A242sSskH00tIAwtpTasmAQTJXq+hv17hJvFaEJgw6cm9IowKGqbFGHw==", "signatures": [{"sig": "MEQCIC9a7IjzofO18Ggp8ZGViCw6ifYhj+39BlJUy2ACV0MZAiAza5dgRKhcQGVKPx/eCs+8GPm6OhWUIjJMLalVx1Y1Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15621835}}, "0.0.0-experimental-1a59184-20250113": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1a59184-20250113", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "064f9de5a07919be0a49ba5a2ee600f8871b5aff", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1a59184-20250113.tgz", "fileCount": 4, "integrity": "sha512-YdLAJdOPXF4NHFoLJEXuw4FwPhNY6EIZgF9DBwUO8OdhT6/SxrotPHgf5lXr4mGKrt9YgoSjFCIMvkLnJZgv/g==", "signatures": [{"sig": "MEQCIAKrLDBbJ4LYBcAwSIpxvPlf23IGIPXS6g7eXg4LDkFVAiBfKCfVtuZk/culvlfw8Eb8PMQrRU/AlcoElTuhoLBgKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15622114}}, "0.0.0-experimental-1a59184-20250115": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1a59184-20250115", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "406598d27ed809a639b85303842abc4f27af7e10", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1a59184-20250115.tgz", "fileCount": 4, "integrity": "sha512-RQHrnpqqOzM8/Si3UrGKpwkbqqIYXJC8vaoRV1Lz6gz1RLjsdXxS/fNJvbM+zv+0ZCMuGRjhW4rEn7IfKl/SHA==", "signatures": [{"sig": "MEUCIQCIMadyRs6zSJUXlW2bhb5qBNS9DWhdeISTYdAQMbG0MwIgOgYDmaZLv7Y8S2MHhKEGo3kJdig2VJw9Z/Gsvzy72jQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15622114}}, "0.0.0-experimental-1a59184-20250116": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1a59184-20250116", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "fef0735b19bf644d2ff8f0e15698b36a7b35656e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1a59184-20250116.tgz", "fileCount": 4, "integrity": "sha512-NPylELcGDMyTWMMGCWzXr7H4MoxQFGppd7a3Zw2VbOV9142Nbnsvi7NiZHSb1M9FAIxkLAJ6qDiDijKvAeWAlw==", "signatures": [{"sig": "MEUCIQD8zFJsc3AhOCX2jqDbxvZFqyS6Fm3t1VNOfneLJAyVnQIgfHELwcG9B6CvIFJONeXBbFz6Dx0DTCl01UPwVIFjwtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15622114}}, "0.0.0-experimental-fa43e63-20250117": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-fa43e63-20250117", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c06a8b37df7b1c465333d8070f66ef97137106fc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-fa43e63-20250117.tgz", "fileCount": 4, "integrity": "sha512-hwqrqtRi8MUY7iQRn4FF9380A5CcD7XqLAK7utl7H5FtWySTGAw4HzgJvCf5blMdurH/MH8TeXvZnQgZD7B5kA==", "signatures": [{"sig": "MEYCIQDGkAyplYsNPMtor2+FNc/vBcU9pj937sWxplRBYDWH1wIhAJ2mY3xbfbaJlEq5jfbhm7+UqtkioYKglcwwhd+NUTa7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15622114}}, "19.0.0-beta-decd7b8-20250118": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-decd7b8-20250118", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "449fe9ca47e2551f30d88e40b0f8510243520d7f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-decd7b8-20250118.tgz", "fileCount": 4, "integrity": "sha512-kymIdd5TmHnq1r2U2n26WI3ukadllV0o19kyrso4kByFE4KPOOXxE3uHibhwW77RPPptPx6ZqNfsZn3L/tm/Ew==", "signatures": [{"sig": "MEQCIHnPu5p8vdH9a3EQh97uKnjsqG9d33POfKr5n7jgwopHAiBo1gEdJ0TNBIUO3ou7vmZgLPLKMAYCocFOvz3hxgXKUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15631186}}, "0.0.0-experimental-decd7b8-20250118": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-decd7b8-20250118", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3fc9e60e64e5e7451ffd71602642ac8cd62b32bd", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-decd7b8-20250118.tgz", "fileCount": 4, "integrity": "sha512-FeX2yJ88x+GY+5WaR4a0k4KdGqlqh82DcJIMV5IvKEr6MCPg89NTBlM7XVGEfP5Mo7peLbAEDJXf2kVs9MudNQ==", "signatures": [{"sig": "MEUCIQD3odXTE2ThXmJfm0GP0K6hNisQlDah6aX764olqgVkgQIgSYd2UJGWdwzXHAkEItIObAqbpHhwYWs8wU9dYSmn56k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15631193}}, "0.0.0-experimental-dd78f5b-20250122": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-dd78f5b-20250122", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "85599aab047a68456e1a4833b3b5d53a160eb9a3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-dd78f5b-20250122.tgz", "fileCount": 4, "integrity": "sha512-PzbzIYwRUdSCyL+yiQ8eFLYjllsZG7a2ieAA0iMZd1Ii+yqoVms6ftrxUtwFjpHCkL46jwjqIjQqgoKjo14cDg==", "signatures": [{"sig": "MEUCIC0m4UJ9QjIQ3xNR3pReQLj2sYB4vupk06LiOgfw4cdeAiEAyD1Rk0lLR+LV7a/e0oCynng0pkPkqMw/CUNUZ4IMi28=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15631193}}, "0.0.0-experimental-b74faa7-20250122": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b74faa7-20250122", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "78b1438bb89a5da14dd46f230cd8c4dd361ab82e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b74faa7-20250122.tgz", "fileCount": 4, "integrity": "sha512-kYMKoQRL22OAZgmKBj9T8i7AxSoIh8okBHje9QxL7/DbPIq9sqCbq4UP91uAOXZOfro/atDyuPFhwFunvD6AKQ==", "signatures": [{"sig": "MEUCIFtJ5dOiZbtsy9Cs4z1bVzt3yF8W2cZOYY0AfgKDU525AiEAlEaF4lxEVfK7OcKRj3tow7GpcOY9lh2vpRT1OmFHYso=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "0.0.0-experimental-b74faa7-20250124": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b74faa7-20250124", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "59bd216d394053b7f2ecfbd97d0e58a52f3608d2", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b74faa7-20250124.tgz", "fileCount": 4, "integrity": "sha512-87CSoYvfpee1XO1JzEPOUIkWg4dC0RnU319ZvplSjNqGnyr3nI2Mju2EmhXu49jRflbZNYxLrWOVDKjWiAqXzw==", "signatures": [{"sig": "MEQCIGyjvZtfG/cnRMWaug+jp8hxhUjhe0jpEuj2mpfgmZtNAiA8xju7vdzQvClKzVxLzJuU3OEMKB0Yb4SvCACI6beLQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "19.0.0-beta-27714ef-20250124": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-27714ef-20250124", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d9e75a45cf2855adbc8f32c4c9467e7df464148a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-27714ef-20250124.tgz", "fileCount": 4, "integrity": "sha512-1AnF/dlvbIJzAjw5jEbDx7csHx772VP9AtrEg2CgUUD6MIu24j5ShxmBZ0tpfdb2pZtLVwcsoGylfZTzUoCr9w==", "signatures": [{"sig": "MEYCIQD0AEtvAyaiv9HyqSaVT+pxKmSc4sxvwDOPAmnzD9oZSAIhAK9r9J+3YPD8rX8GVGNms0jtvcNzE5BgBGgasvY2PVdr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633253}}, "0.0.0-experimental-27714ef-20250124": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-27714ef-20250124", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "2396dce461251f5230dbc9c1e0588de5ec8ad4cd", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-27714ef-20250124.tgz", "fileCount": 4, "integrity": "sha512-75WlEtR/3vh/w8sZXN+vk2YAarH4/RFTnRGWe+s8D9C3egBoqxaeA7bz/uUNBapt0OlVwRWaa5+DHzui9D5ZHA==", "signatures": [{"sig": "MEUCIQCCRPDlUBgoW4esjLy76JHvfrik6xebqNzLof11AwAOXgIgFO0vRmdZsTuqElkIJ/0fcCoyMXfykeEh6uGcnjWpV5M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "0.0.0-experimental-e4f4f9a-20250127": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e4f4f9a-20250127", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "78965be34f1df9af7c4090c548522ecb812dbbe4", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e4f4f9a-20250127.tgz", "fileCount": 4, "integrity": "sha512-vh60hnx6bzxE2LcSgqI7c+RqS1HqKgdr8e4HylH06+zP0xf46LrLK9YEJ2Rs/1BOB2iciVnNTWYFp2SGQPgMOg==", "signatures": [{"sig": "MEQCIH8Pj3/dS3IuM0447A7ENDuu/aMDnNvtJmJVv6szw6vZAiBsIEnWhA1/+01ivLP3hbm8mv8GKtY8hvxtid6wFivAvg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "0.0.0-experimental-e4f4f9a-20250128": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e4f4f9a-20250128", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b461ebbdf9d89da1eddf9a0d63503f7fa90a0487", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e4f4f9a-20250128.tgz", "fileCount": 4, "integrity": "sha512-AJqqMTEmds7tQNTaMpUjz0A1+CCq/vOqbLDggYoLf7v8/wxJXxbMVv+qcaYkjyA3W3Wp8rrEPeD7jiZZ7eKS8g==", "signatures": [{"sig": "MEUCIQD7cgZbpOdiK0D45iZhMzy45uw/cKbdfksipR1FL3twKwIgMHID1qIb1Nfyqfy5uJ97uTv3CszB5rmS80OCoVvLCmw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "0.0.0-experimental-e4f4f9a-20250130": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e4f4f9a-20250130", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "533c01b7edd3822cbeaa2e0eb1819b6d0d195987", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e4f4f9a-20250130.tgz", "fileCount": 4, "integrity": "sha512-/aXQgsUUF35gwDw4TzLISlaqK3U823zCQWep8YNMjKDkEqObFfvydniPJCnckHbpC5bsbuxBz4QQ/AEuImsrkw==", "signatures": [{"sig": "MEYCIQDk26KH9J/uXNj41WT+UzIWub501b6DXjdfwOP7qLZdHgIhAP3xpHDlGXm1K5X6Ij69SlOznsAh8+2zzd5MLIm9nC9+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15633260}}, "19.0.0-beta-714736e-20250131": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-714736e-20250131", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8efad3433e56701e19015713741f493e228c700f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-714736e-20250131.tgz", "fileCount": 4, "integrity": "sha512-frj2l6fRWVi26iw9WthFKyFyE4u5ZSHH3KdKiscOOwpz210seTtwnp0QbJmi8Zoa5HK7Fk2fH40JffN2y8GvLg==", "signatures": [{"sig": "MEQCIBR9yi+sBc391EIYt1EUqcG4Ylranv0B9aCu+ulygFmwAiAShK7XSZRu93zMON/OBT4ppyjxrn/L3/EQeK2jSmmO0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15636646}}, "0.0.0-experimental-714736e-20250203": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-714736e-20250203", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d4b2be9704c3ecf9ab5668871c9a901298be8c98", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-714736e-20250203.tgz", "fileCount": 4, "integrity": "sha512-+pj8NK4VsV3PRHrENWUVR9/fGg2b0kGJT8rQo5v5GfA3NdSOgHklMnY27bU+WG/Tpjm9+mOmS8OwTYKmi1yv8g==", "signatures": [{"sig": "MEUCIQCZcPUVnV7jpb0FUL2OyT7sV7JQFEZVwFsTkIxhdCBO9AIgKf6PNIqBxUAhAptwCSxfNg1oiCOO4/8vi1vAL9efSzE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15636653}}, "0.0.0-experimental-a08709b-20250203": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a08709b-20250203", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d252d219ff96944e280d6f35bd5dac38255f211a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a08709b-20250203.tgz", "fileCount": 4, "integrity": "sha512-wgJm4MZ+H3+/3L5hM11X5Gcy99U3hhIS6JwRiTy29Mx1CksGiuJJTpR4QnQJ6NJ4WTJNg2FcgWcZ7HCAmiCaKQ==", "signatures": [{"sig": "MEYCIQD6wngSJexgI8eVuWPBHfp9Ud2p0ziHpWXDZ6aKtZAM5AIhAMMlCH+d88EaM5+df/XkPb9CdSBEbd5f99+LVXnitL80", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15636745}}, "0.0.0-experimental-a08709b-20250205": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a08709b-20250205", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0e15514f5020e62ae7d5ca398301ee8399510bc1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a08709b-20250205.tgz", "fileCount": 4, "integrity": "sha512-00kY3+64mR8obwaGhkD+90jOGVgs2jtiCUcc/sNPrq70znsY6LIDJnCGs6XxB2rgYwghb1r3J+K7uVc64H3V7w==", "signatures": [{"sig": "MEYCIQCg57PbIMvt5OOA7G7eCdAdixUt0yJ5Fg0TnHLywvnmBQIhALZ5D7JX7yCtGOisvN5gheG2sFHKRz0dlt3q7cpRjoiS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15636745}}, "0.0.0-experimental-a08709b-20250207": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a08709b-20250207", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c909cca8c4f5a826c9d24f60eefd0c110c8e02f1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a08709b-20250207.tgz", "fileCount": 4, "integrity": "sha512-PAwpgcnCf2z6+gOdJoGfhI0Y2k2QzvpriyLifC9pQL0Ir9KhSMxLX3N/GlPodBiu8drxl4dz68pOL7CWMbbBdg==", "signatures": [{"sig": "MEUCIQCdP/xYTHX5oLlalficjncq75+ZbyQW4Tm0idDmQQFWrgIgP4/VYTHUrMjVhF6PADvUxFkcSdPuRkBMqXg+cgAFlog=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15636745}}, "19.0.0-beta-30d8a17-20250209": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-30d8a17-20250209", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4e2aff9a6e9735bcd6324170289c2afb40df782e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-30d8a17-20250209.tgz", "fileCount": 4, "integrity": "sha512-0pQHlz5nmBiEQ8ZWWVLeaBzz/FkToAdXEXBBnd21uSrDtIzhSe+s3VMvqMsv6vYHNTr+0KmsvVfEqXQp0W0kzg==", "signatures": [{"sig": "MEYCIQCC0PCwIx8AVv1YYcIJieU58ia8fS2pLTC/JLdwNvOeYQIhALrx9cRuP9PP6mfilJU+C4JDScrH5sRSXpVoGRqBRFTV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640136}}, "0.0.0-experimental-30d8a17-20250209": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-30d8a17-20250209", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a31baf5f490f780ab3fcc8d8cb278bf96b6de11c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-30d8a17-20250209.tgz", "fileCount": 4, "integrity": "sha512-ROdnmvciad8IWvNPUuC6B8BOI3KNaS9JVGlGV49S7C883YWcM9QHOPXKCLRg3rqPvgSEhCIUz9NHRfDdfQHTrw==", "signatures": [{"sig": "MEUCIQCY/FfT9l29EvzbQsTw/9gcFDUHaq8vaDjvCV+Mx87CkwIgZ8wCdqBzES0GF0wQI8b3gn5aLUC15SMm/t65b7jNy3w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-30d8a17-20250210": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-30d8a17-20250210", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3c567db0cdaba3794318beb01c184ae686f1db9a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-30d8a17-20250210.tgz", "fileCount": 4, "integrity": "sha512-zh1LaEWUsvv8Aec8H7O53TBrg3hi+RXM1RjtkZ7b/czczufLrHE6JI608weEgzJEhe56lWOcbFqsL2iNAeDksg==", "signatures": [{"sig": "MEYCIQDb4nGcGxT4BaCZBCjrt0dkjH+ZZ8KpurDaeLA3/uI4WAIhALRhN0NMKNY9zPvIPsfM4gouwY/Omx4H/Mz9JQA4QysA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-30d8a17-20250212": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-30d8a17-20250212", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c30d9bdd6335c3c6edc8ba3ebec1c896cc1386dc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-30d8a17-20250212.tgz", "fileCount": 4, "integrity": "sha512-Ue+M1R7BbtPsH49hPjtR7Q1E/g3MuggS+HOnvAo4aHcvHEBXfQJBWIVefQFnb6pjkNCMue/hBjpttgiNv6K3Zg==", "signatures": [{"sig": "MEUCICrr0PvQ7+yUGtaaKxdErb6k3h2qTEE3RpDQwVE627qLAiEA0hIh5YRDo1kHzSzrAf2THike6C6T1JFq/BNsoBNpf68=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-21e868a-20250213": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-21e868a-20250213", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "71e39927535722ea5f1e1084bdba773d1e2b5080", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-21e868a-20250213.tgz", "fileCount": 4, "integrity": "sha512-mbRoxchce6O+Fkl8MplP7AsjnOnT2QK50bBS9H2aqnn6qhduIwTrozqJ1+wTA2nvLxO5BNwu75ITE9fFThc4Yw==", "signatures": [{"sig": "MEUCIF3inq8fQaQwmhmi87sRws9uNgD+I6XqjWvcunHmzZ4ZAiEAoo45KJeM6aQQf0SDXnkeRius13ppjaAr/xx5uaXT/Is=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-21e868a-20250214": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-21e868a-20250214", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a9d9788cff6fd06dfee0c86378cdcc258401a874", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-21e868a-20250214.tgz", "fileCount": 4, "integrity": "sha512-pYwHz7i6cCZW5d5hzzLewTEuARtJJuuj9NYQEt6cF+nFyXL7jQmhnpbKrKhTSCdzeU/musH8Fcuf5X5OWvpw+A==", "signatures": [{"sig": "MEYCIQCGqgF+soNlUhAAADSghP9ZPzFfnjJCBmv717hEe19sEAIhAOkPHBxD9aO6pn680td5QdWxgnCfnz6PcF2Y9aoSdq0l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "19.0.0-beta-21e868a-20250216": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-21e868a-20250216", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9250412799024125c87165e367e73c92861cd3c5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-21e868a-20250216.tgz", "fileCount": 4, "integrity": "sha512-WDOBsm9t9P0RADm8CSlav5OqWvs+3mZFvrBo/qf3vuNtdz78OG5TFxOy7De8ePR3rA6qg1Qmcjjae6nR1pOpCA==", "signatures": [{"sig": "MEUCIQCDPZqep3YBsMJ+yVmFcD+awlQPG7OnEuvuDReF2cRnvQIgPaBGNp8/TDafSTqP6v8rW3YwiMtHmUId/G+fWonVzlE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640136}}, "0.0.0-experimental-21e868a-20250216": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-21e868a-20250216", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4021639d70764774b461ff5fa430f50e2ca43492", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-21e868a-20250216.tgz", "fileCount": 4, "integrity": "sha512-1PEBWi1pv5BpzWIhusvMjlqdlGBMcRYNvb4MghyrsxA6KAx4S3JkiVnfuXqD3at+Xu0ANqRzadAzYevb5xuPcw==", "signatures": [{"sig": "MEUCIEJtzJq6r95yWgbSpwc2q/5fl/b8kWqKXmmP2glRvBGqAiEAl/SnWn1ayAhfk0Fq7PLLHONvOrTFFp0wOsS39vIBEdQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-21e868a-20250218": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-21e868a-20250218", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "36d51831ae62a15d74c9e6c83f1366749a642738", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-21e868a-20250218.tgz", "fileCount": 4, "integrity": "sha512-K1yKvrvJq8IhKv/9saSiKHaGdrVUARlwisuk3etVzs1Kn+NFs1AqRvtq/+ZKtcJAmYN8Tfe3aoReLVMpblYr8g==", "signatures": [{"sig": "MEUCIQCSI86XSN8OpnsZJizTm9W93Jy58E51oaTfrzTj1A4UvAIgZ3Ns40Ly59dVCzwMed9TC6pfDCJHBr6GXlX90ML6hUk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15640143}}, "0.0.0-experimental-05e5570-20250218": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-05e5570-20250218", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "bfafb88717eb43d83ab037a200c59766377dbd35", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-05e5570-20250218.tgz", "fileCount": 4, "integrity": "sha512-aTVKUDjr1DMsUUh/bg0Gt/DWzct6pQI2pBxxFhLnei/3ASedk6GyjDZ/S+Wz2jzlmvg+gpi22YT8Aa4zTnCGZA==", "signatures": [{"sig": "MEUCIQDq1H3er1HGAYkGX1f1qPMF609o+fcqU8q23WKOIJtuQwIgNptVusvXmjP/IhxKk9mOaD4B96djsqqGqb/Azr2nla8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614666}}, "0.0.0-experimental-89163ad-20250219": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-89163ad-20250219", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "dad62d47eecffee1cf32e2ec9bf587f9366af54f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-89163ad-20250219.tgz", "fileCount": 4, "integrity": "sha512-rxYdToNGJ8PtEYG+7EUaqntLIRCymB8+UPATf2TmyYs3+t1XCCI2HRwWLcY8DlHkQK3cTCHnZS8MzG5OJSM8NQ==", "signatures": [{"sig": "MEQCIFo/IAir5BQBRNpDLW8litWftgVEVGeMRP2Uy81f4xKdAiBF4aW8FjHWEHPjCvjBHzLOkMId8uK/F5FhmbQwEakqXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614289}}, "0.0.0-experimental-e1e972c-20250221": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e1e972c-20250221", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "004fc9305b077ae9ffc66aa2fad3bc6cfcc0d83d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e1e972c-20250221.tgz", "fileCount": 4, "integrity": "sha512-ky9l/2DnkgD3pqErkk1ft531fF1WRvXKcltXxuJ7Ep1VbQvNtb8jKNG/+SKCybMDOO/QEYI9BlIT+rtPVUh8Bw==", "signatures": [{"sig": "MEQCID8+i5bJn/Dm6flgfGziZmdJSdANK/Pbt/PDdcIjMPLkAiA1jeFFhpJ/W5USc2UAITaklmvR6DUD0zdPdfbMKqQOwg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614289}}, "19.0.0-beta-e1e972c-20250221": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-e1e972c-20250221", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e178d9d9daf86fff8ec12addacf91b6338740e13", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-e1e972c-20250221.tgz", "fileCount": 4, "integrity": "sha512-m3Y8KdwBwKj9l6bf1XPO2xm0WWzv/cYJPurkwP5j8SADGor6l9CdQVksrcOGzU/4Rylfa+tXW6+xaR3vAKs7Hg==", "signatures": [{"sig": "MEYCIQD6OiCBITjSnFA/T5i9yehhTW60KlYJpwz7/m8mgddoWgIhAPO09ROxWfYo6mEFMUDPqojtsLT/1libIzGzcafmDrxZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614282}}, "0.0.0-experimental-e1e972c-20250224": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e1e972c-20250224", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "62b3ee03977f7d584bfdc1bb9c14c961d646f821", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e1e972c-20250224.tgz", "fileCount": 4, "integrity": "sha512-UgK94HX3VCKfP8ysw5WI815D2IbmRZLGZMdtMwwqxLOjc7f0ZvS9xwZyD1kTKDeHid5yu5rhAlSr5ohQm8H0Gg==", "signatures": [{"sig": "MEUCIQDEDsngHNWUCpLcenJKT4QfpPSSiyjKw+bF8lz7lWnLHgIgOW3aXosheJqG4d6vzDrX8KRCDQiT47CqttBB8el1LxE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614289}}, "0.0.0-experimental-e1e972c-20250225": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e1e972c-20250225", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c24215fe9a3bd61f295c877de732d2dcabf3dff1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e1e972c-20250225.tgz", "fileCount": 4, "integrity": "sha512-+BCeaqCGVWvLsswaUMr7oNPsSzG7aTqt26DmfL4z/Hb9uIyUM7FwTpxUzdPCbokcZd3D3hO47oFY4ZslGjHpLQ==", "signatures": [{"sig": "MEUCIH7F7TAxn73PQmi22rv1S5zGLNTU6sK1KCvdqjStw7CrAiEA3p0VKNcbnUMcQySL/TcT+BL/NFk+67DGkdk09aDZg04=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614289}}, "0.0.0-experimental-40c6c23-20250225": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40c6c23-20250225", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4411b6d56c70433418c4742586611e6523bea350", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40c6c23-20250225.tgz", "fileCount": 4, "integrity": "sha512-k/owjDoTQFFRmA0K1gnMGfcUHMOynrvkzA/3fFcuOKT8q/stY9pBZ1uj+PX8PCmRNCsT7G7Uq56lDpevw44chA==", "signatures": [{"sig": "MEYCIQCtHj4xnKUedARhFtjiI/pF3IK/dnmaQokh/oTruB0fEgIhAPWun8L+Ioh1lEPaG265wsHQXm+MJq1UUVYahHFMMS0b", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614636}}, "0.0.0-experimental-40c6c23-20250228": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40c6c23-20250228", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "914d0c05db7e5564076992299af42c303282ef22", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40c6c23-20250228.tgz", "fileCount": 4, "integrity": "sha512-KWXNSGyyvA9aFvlLDZqCpvrcX5dGgxd7YxO6vd4Zi3/wQX4Om6FyrZKJ9Z0t7+FKoOiJM83w+6TuFfoFhyCxEg==", "signatures": [{"sig": "MEUCIACR97Un4cdKuFAEMXwyqvTiwZvHFxYBgbbOgM7Pmko1AiEAlvY0Y8ifyUpw69natVsjZ2P9lqD5hHBn+Gxhjjj4oBE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614636}}, "19.0.0-beta-40c6c23-20250301": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-40c6c23-20250301", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4ff749a1b619eee2730b3d8b183a5f6b9d9b28aa", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-40c6c23-20250301.tgz", "fileCount": 4, "integrity": "sha512-himtjPafvMbA7PYnV2L+jprpB3h4rhx/n5s4L3gC654FOUsmsv5n4p8d6ufvK2zqUQs4kTOjgT2b4wnuDU32CA==", "signatures": [{"sig": "MEYCIQC8FjBT5odYDMDngzgIEkwhwzGVceWybVlJlg0c71M57wIhAMKwPAC23rheZG+qScJIvWa31IXqjCMrZI1iVBFbxsXk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614629}}, "0.0.0-experimental-40c6c23-20250301": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40c6c23-20250301", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7d799a03d4b17f1a9ac93beb88feac6005f25f33", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40c6c23-20250301.tgz", "fileCount": 4, "integrity": "sha512-147sVWqI16qQabX5n9r7sLqlFFamAn7hpQQXC4kE6G7glsfnrpqo6Lps+ig1J3q7EAedt03XoEXioA0/DsbE0w==", "signatures": [{"sig": "MEYCIQC+WHlVabmy9m73IORulmCMmzA2CyLigKrOqDZCL26IZAIhAJrZQorfEAappjf0qVM8hLn+ZLvGw96DH5+Z83FAuYOq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15614636}}, "0.0.0-experimental-6285b86-20250303": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6285b86-20250303", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "61fa69cec9b9b7f9859aaa91fa5774b2e94b0817", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6285b86-20250303.tgz", "fileCount": 4, "integrity": "sha512-Do0PvY8LecqY/yG8ClaapjShd3fAFVoF6zzVe2f8L1tC2WD92xWoMFqB8grO/v4t2B6O3cORfeAMWpaQc0taKg==", "signatures": [{"sig": "MEUCIQD5NpH5V/8RhWfSE7pjCljCjEz8nSZZY5ZRjSOEvHH1JQIgbTe7RhonsyJ1cNVgHlzR6rLbOpvOy2/bVra9AjsdqkI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15618321}}, "0.0.0-experimental-6285b86-20250305": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6285b86-20250305", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "491bf382789fb54e251b51fe24dd59c7b1eee445", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6285b86-20250305.tgz", "fileCount": 4, "integrity": "sha512-4iO77LARTs5hs0+U1lp87BOdgebbLqQXW69CyAbOjje3DBtIFTF0qi3J2IcrIvXC5i9CN+H4BL0/+mp6IEQQBQ==", "signatures": [{"sig": "MEQCIFdRz4h2bFVK0KrwAm51we9bBF38750YuNzwtmuzDlW0AiAXbOzWlptarrL+4lYN/TYPhDs4bo80Axkz+DzA1XqVoA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15618321}}, "0.0.0-experimental-6285b86-20250306": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-6285b86-20250306", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "1703db3444eef2b9ca90240a4c1bf30c058b8e68", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-6285b86-20250306.tgz", "fileCount": 4, "integrity": "sha512-wU9Ig3GNXHITzz/XksTdTYgl/iywPjwuIGiDEAVqHix/atJIeDGpXOEd+fueNgfgA6lOuo2TPx4+/sn+uhA5dg==", "signatures": [{"sig": "MEUCIF1oKT4fDfKYbliZanbztt/jyjYZRxCalIuykxCbfXQCAiEAzT+z6l7nCc5p/LJGzFZmVFoKYiBUiL0kEqXJaXtTebw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15618321}}, "0.0.0-experimental-bae2d03-20250306": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-bae2d03-20250306", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c382cbf3adb12902970354150c13247d2dfb5765", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-bae2d03-20250306.tgz", "fileCount": 4, "integrity": "sha512-Wx2DU+zCcedRkAOQbPwBCMT0dwdckusg1AXKEB4bHh6MNv9BIvDAOoD4iA9ZRbNA4rhmI+5jHuurB3cAuVg/8w==", "signatures": [{"sig": "MEUCIQD36VbhNglhNYNYglj3iZQD7psIdL0wNoBBoF5WWNlfGgIgT2H8D0UfzQGn9Z6XbFULR5p58iag8MDqn98HiBh3dkM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15618591}}, "19.0.0-beta-bafa41b-20250307": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-bafa41b-20250307", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "294033e54b734a0f067beec001192aa071d7139d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-bafa41b-20250307.tgz", "fileCount": 4, "integrity": "sha512-nh3pEhufqf3mu5l10RgzBL1cQY4zGG94bYsdOQlQsefUExs+DZfUH9RDs1g08bCVfFscKOXpGlOkcHVuAGQqug==", "signatures": [{"sig": "MEYCIQCupJno2TbkwSW0uyDZSdh4cRDcQBz+WqpkS/rcH/8rmwIhALkdRNqXw8DAOMn8sz0VOD63PlQ3mP+d9CG2aobIm4V1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5179303}}, "0.0.0-experimental-bafa41b-20250307": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-bafa41b-20250307", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9fbcb2d29e72b7d75ac2b7053e236de23f2c6cdf", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-bafa41b-20250307.tgz", "fileCount": 4, "integrity": "sha512-KBAlJ0SmQUR0hhPidBRvF2cWx8iqoQv1bpmPeVX7ssDf9VcHavRzYmQEYUfDVBfRw0DvHspRSLkOv8pZkLnlTA==", "signatures": [{"sig": "MEQCIHqQdhfa5rGW8Sa2AlU3CfdvS7H/Vfk7QkFhVyKDNIsQAiAr+Y05PCznHoCFfoyUbWi6dsV9rteVnFOEOU/432C5Zw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5179310}}, "0.0.0-experimental-ecdd742-20250311": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ecdd742-20250311", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "363e684cbb2818ebcdada8afdcdc2cb5a6f2887e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ecdd742-20250311.tgz", "fileCount": 4, "integrity": "sha512-xDHP8R9ZQRfBAR+jHb8vFBCkHgnP1WCB78PLJNoSQnAZrg+gXxb9Bhpbea8Jv5B1dIq7ocz5Fx/YoRwTexWg3Q==", "signatures": [{"sig": "MEUCIFxEdq/w9OwsVqIV3XyLsmwDq3JgdtGP2szpDX+VczpqAiEAvHrx2TVV5Bs5Tn0q3zstGoHyAJsCUdj5fohmabVv8uQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5179310}}, "0.0.0-experimental-ecdd742-20250312": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ecdd742-20250312", "dependencies": {"@babel/types": "^7.19.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "6e5bd1c0d9f83cee510b295fcc79b5962868145b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ecdd742-20250312.tgz", "fileCount": 4, "integrity": "sha512-oo6QohsK38tXIi3sfUnwWp2d+E/r1WJu+DYZxtgB0wmME6NUZv3Rdp6FNzQLrIRIuuqg4aOi/AM5oVBsmX7zWA==", "signatures": [{"sig": "MEUCIG6lULookHJOqdHGqra/8uq0QZEpXO+5H5TkerepH+ByAiEA83EcZoFpuHNDfILhsJDGaXLvLZBZiK+BiqK22ICpTEY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5179310}}, "0.0.0-experimental-d664a4c-20250313": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-d664a4c-20250313", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4566934433d6e7a9c907ff4a7fed2826bfb5db93", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-d664a4c-20250313.tgz", "fileCount": 4, "integrity": "sha512-LnNOnvUD4daQ3P/mF/NhwqdGdhLLqH3YykHQbmZpKDmf7dlobhQQ9gbEOP5pWH9BhLUGtCa+EupBp/5TabP9TQ==", "signatures": [{"sig": "MEUCIQCg18S/lRo0rxE1/jOnbZ6/gsVlDfGFwQ2fgrNK16chMAIgbnbXZlvjWC+bM84hMBK0ec64BKhnXJB8MP3IvsQW0KA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5222857}}, "0.0.0-experimental-3229e95-20250314": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3229e95-20250314", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "752e17965e644983f67afafd352c0adbc00ec244", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3229e95-20250314.tgz", "fileCount": 4, "integrity": "sha512-HUk2tIGz44eLti1X/TsQG8KxrPAqcnBjU5d2gHUAqAnvH6zDnfcNmn+bK2wQ8XAGO1zTtrwYTxhJPwRdmnB0Ug==", "signatures": [{"sig": "MEYCIQDXchSSc0xttJJvsdDI8TELLHvV0/DCWmvfN9efcPfCEAIhALYh1HzZfUvyBw9q+TYe6Iprcd6Ww6p+W28jyb3/x4AF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5232227}}, "19.0.0-beta-3229e95-20250315": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-3229e95-20250315", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0c209f58adb49ac6c75897c31ac4fbd340562467", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-3229e95-20250315.tgz", "fileCount": 4, "integrity": "sha512-r29/CW4gPXX2kgIdgiCmI/MJzeOhI9iM+FCYmg88bOj5esNhKrSPwp2YcOufLhZRGVX2VuX8ung9mFDsotQusg==", "signatures": [{"sig": "MEYCIQCvhx1XmMSWKqJKF8cAeeZzS/CKykTu2wfKk4xxbPg6swIhAMM6U+XAJzHMOoPnde2cp7WcIT0LC4FeJ2lcrqUtDkOl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5232220}}, "0.0.0-experimental-3229e95-20250317": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3229e95-20250317", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7b5d51f10c7c56518140772de25d45d73b877327", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3229e95-20250317.tgz", "fileCount": 4, "integrity": "sha512-OvRQjmDvpbGtUuiY/jk3y6M18scqFj60LVSHXdVCb9EhWI0yP5QBGeCHR1FtsJ5N4FQOUzaV4tkx8yrd7B5eJg==", "signatures": [{"sig": "MEYCIQDfXUnFiEeeglV351feZd2e+M/xcqvsvRxpBlmtcRpl7gIhAJfLVsaLItgOmIQzGdR6w6QW8ETnH9vvPIq/JuAatqKm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5232227}}, "0.0.0-experimental-7f3c872-20250318": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7f3c872-20250318", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "70a434084d237dfc8d6812deb1a9e3199d7a261b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7f3c872-20250318.tgz", "fileCount": 4, "integrity": "sha512-HmuCTJT4aB+2C6gqGgGyw+rXkD4/ifhi+QlrsGlLWk14EtbEYPmznsf5/dqJNE+VRG4fskgFvUdDeFsJJ9gibQ==", "signatures": [{"sig": "MEUCIQDzBbkbI7UIhw0uvQ4Y1X8nZ1AXH7uLVWbAEgwUg9vTLwIgciwllgqgKTnZ+AN+I72II+9uChugBL98AQ4/zd46Lfw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5232522}}, "0.0.0-experimental-794db7b-20250319": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-794db7b-20250319", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4446eb31f98db3a6255d6ebd284d0746019f1180", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-794db7b-20250319.tgz", "fileCount": 4, "integrity": "sha512-S87A1k+zyqUHHREnpOduFFP3e5OZbjQOMOjRl5A7MRETfO20hyWZHDTjTwEHoVQcKQ4txRVfuOTfwCX9qVMPLQ==", "signatures": [{"sig": "MEUCIQD3sfieRoEqIbl/3qYexg7FssZ/ZgBzMDOJs1I7lHu8yAIgDCMI479vci+4+lb0162SO+MB0e42ZcgvIQgteKzyJ0o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5236028}}, "0.0.0-experimental-afa1d5a-20250319": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-afa1d5a-20250319", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ce8500f3ab80ddb6e5796a918a3a1d1210065150", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-afa1d5a-20250319.tgz", "fileCount": 4, "integrity": "sha512-qPb7KscVMs8BXfIJo31nM2GZE4GlaOjkYHoosCKiwY7dgbTutqIt6RxE6BHHhsaAq3Curr79SxJ32psGLRqGSA==", "signatures": [{"sig": "MEQCIGfsktxpM5MuEqAl971knxqa1eMRzlVqT7AkeetBczBkAiAtHnjDanuqK4uLxt1onvs/56lSNRAGO8ngKX9+SjRw0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5236028}}, "0.0.0-experimental-3452ff1-20250321": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3452ff1-20250321", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "dd130154417dc60bc1701f9bc832f36390b16f39", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3452ff1-20250321.tgz", "fileCount": 4, "integrity": "sha512-sPh2Gu8/xz7TadkdAf0cs9U1zNgCbj5iSeGg/IonbcJdi5zs/EPvuWckw7HIKw5qKzn+50HbLxSEto/ZnLl0GQ==", "signatures": [{"sig": "MEUCIFNDSMebJX3R8qS9A9u4OIviTjgD1gC1HPcm20VB8qqzAiEA2525TaUIqIyge8Y/t60sf0DXny8s7egZN+RrUE6kFVY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5241949}}, "19.0.0-beta-aeaed83-20250323": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-aeaed83-20250323", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d2d40c280e61adeb32a6260e265bbd1fc68bcc7a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-aeaed83-20250323.tgz", "fileCount": 4, "integrity": "sha512-7WKelqaC8mz3SIy9YRAySkY9veHKE80HruUmXUOhk0ZcBvDBgAlltv06lMIiGyHB7TDzAHS1fANzOXCPcfSErQ==", "signatures": [{"sig": "MEQCIGYaEI0ppzuyXPwrz4RJsHjqeW0z0TMduJpLUcNpcjWCAiBDNufj55t9jun1rrAf3Po9frch1/6VL1dhx4CgUG5lug==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5254102}}, "0.0.0-experimental-3cea1f4-20250324": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-3cea1f4-20250324", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "200aa9d705b83551026723bc2af5774ad45fc951", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-3cea1f4-20250324.tgz", "fileCount": 4, "integrity": "sha512-qWrKayJHf1C3KlxhrW75S4SSA5aQF9dH/372JrrIR+k98RpjXahFHCQE39oF91zKIfnWrgj41iTEjoWF99l/+g==", "signatures": [{"sig": "MEUCICF9qWLAyaAMPCGPM7Z1+CHf5/a9io4CCLa6zroKwhFUAiEA5SNp1/DUNSk34d5sGqVT0sAiIZ5UoRbWPUbAxz8pPho=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5256935}}, "0.0.0-experimental-a9d296d-20250325": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a9d296d-20250325", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e8daca92c755f818001c1d581e08c37fa23d7a8d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a9d296d-20250325.tgz", "fileCount": 4, "integrity": "sha512-T6J9T4DGiw9RjO/BU1Y+88E6Mms21LY9J2EUC8ytqb+9hdLYyB0IUQ1kTkO/Rk2vkJJzemyRCD20z+wm7jtQHw==", "signatures": [{"sig": "MEQCICBnD5YcVlU+ed2jtFbwFmUqAo2bEW5ONZ3+xImV3JtUAiBfmyfHQVhis1eUcAjLek0vtPzvVzfM9CDBF6Ympn4RIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5258394}}, "0.0.0-experimental-a9d296d-20250326": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a9d296d-20250326", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a00af4e65c470133ce1ad4d05838791d93a45d55", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a9d296d-20250326.tgz", "fileCount": 4, "integrity": "sha512-qXRnVVBWMqSwII9xTrcxD4j9P+mCD+rHaKJZt44BuHOZ3z/CuYmBzvEl9nUDOaEFyaYC2kJvaKeCjurDot7Kgw==", "signatures": [{"sig": "MEYCIQD/XsztE+94vWgxedZBT/LZE9tdmzVEUHFg04ywGeLv6QIhAM5dAFUeZ3S8SNuyyrouKtzc43fXy+MvTitg1T6Y0yYI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5258394}}, "0.0.0-experimental-84c28d9-20250326": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-84c28d9-20250326", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b78a90189fd2fa26af169f40aeadc770a1dffb51", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-84c28d9-20250326.tgz", "fileCount": 4, "integrity": "sha512-2JTkz3XmRUNgbcb718hfXZKB6C2fSaqMJmsC8fyFR6yZ6XexPyKS35QZvv1CfFReQ76h1hphWcMK6wVw2Pifeg==", "signatures": [{"sig": "MEYCIQDpU5Ll0U5ngdaoHKMIsZBrBEfEY4U/KzKvx1udlNZCFQIhAL4d0IuvsaaPsAgHFHeWVuxX+GHuJWNU+Xfkth2ej6T9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5259347}}, "0.0.0-experimental-b6140c1-20250328": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b6140c1-20250328", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3090348ddec14a296cc3375c1fd1eb6138caad12", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b6140c1-20250328.tgz", "fileCount": 4, "integrity": "sha512-ArMJ/l/FbC7kdZ6Jv5puy+T/qZbLXhG0B5+sjEq9zQj3U+BiSp9/kNpqe/zPgY1igTWNKiBlaeS5Y2zkk+YL0w==", "signatures": [{"sig": "MEQCIGKhWULUz7VicZjPA8B93cVcfthfOZ0OGo1rgdMeOriHAiALyqpsVXDbiQUAlZIF1W4lbnVHWOTq+4lAjXrbk1+hww==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "19.0.0-beta-e993439-20250328": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-e993439-20250328", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "524fb091728b5aa8a05b842c185fad05922c118c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-e993439-20250328.tgz", "fileCount": 4, "integrity": "sha512-eq0lxXDicCNfhtIhm2L2nW2FyDcPMfuJTQG641ZWMWxEVqwmtUlAkWXC4o5C3vykhWMTsXmiJe7/hxXVUbV8ZA==", "signatures": [{"sig": "MEUCIEy5Tl0UAxMe9pm509VYSVGccax2LP77mPFKj3tRCsIeAiEAzZPEFlh3d0cGhkdkrDThO3mkZeII6BOfxI5N8+5oSBA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261292}}, "0.0.0-experimental-e993439-20250328": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250328", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ca43215bca0066fec5306b0f9c069be94aeb7dc3", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250328.tgz", "fileCount": 4, "integrity": "sha512-IZBR7saBXbtNuSKA7P4zYC1rptcrHrbDspA9Nm+FtLeN/WhgXXhMn0AtNAMWHUNtdhXJHE+ebdfteUT1b9o7Eg==", "signatures": [{"sig": "MEUCIQDxNEIALdcNZqWZsdbgMvRFmC8XE3p8s27ExHZIsalf3QIgEUKZS5V33s9cAGi/4KbNziEhLzWdGbO7wPt8tXnof6k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-e993439-20250401": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250401", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "4ab9b259191330a354b0ed979d6e9112712a3633", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250401.tgz", "fileCount": 4, "integrity": "sha512-jS3zFwZxAdQwIe/o4ow4n5g6vwxJt2mq7HlNCszbH0gHH7aySPS6j2e0YKavWvI/vc06iIdEyrep40MJ6RS2IA==", "signatures": [{"sig": "MEYCIQDFMZUPn5+xgv9wzLbBN9+06Su3gY2m85qX95l63VRmKAIhANm9fP5Ux3pQnS11iVM9ZFlNsmQoN+Vh+HQYISIqla3/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-e993439-20250402": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250402", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "eaabd200d65750bfd799f6a293034b0dccb97cb8", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250402.tgz", "fileCount": 4, "integrity": "sha512-iMGKoBDiLDaq912JfTZz8EZbo8TU4aCY3DW5OFy/8+Vbqjq7412kUjputh9o9J4MS0KBuPE6Bc4qZLgedYeEgA==", "signatures": [{"sig": "MEQCIH8gnX1U9GUhDoCRBMSIAel8c02cj/kUPXIjmdq2EJuRAiBp7Zrqt3whvaTY8As6I36MA+HBVBmvjEX59uRden+Hsg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-e993439-20250403": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250403", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b71b77c3dc8ead66a7025318a5c90be769d602af", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250403.tgz", "fileCount": 4, "integrity": "sha512-ZsyC4xayyVrmF/ChFUDKNOfknlTMRNFpnEd+5EmWprUHwYjJkaBQzZeTrtIJLWBB/zOBoBwTvXEly2rLkAaFiA==", "signatures": [{"sig": "MEUCIB0irzFTOZqbk+Wiq0WXwIAMHrEJX3ad6zoPTsw+e1omAiEA08L1/MWEIF/nsiQdHpOA3xT6PO9F1ENlWvoWKhZyMr8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "19.0.0-beta-e993439-20250405": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-e993439-20250405", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0618231d06054c3d3394d9735109f0b344797f8a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-e993439-20250405.tgz", "fileCount": 4, "integrity": "sha512-bPAx2GvDZbhdCbliGQICGgeaCmJGDZt+DuRtrWbW83NLTIkCwvV4chvW0fR2mowtleTdgIc+4Ibc2TgNahgpfA==", "signatures": [{"sig": "MEYCIQDMM/oKuoPCO5JvQji45RiTcXT2yaNuHFsA816cT1mnFQIhANPay+kDI4NaUB9KN3K10TGKNadZXd3uuZeW6/4heVmZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261292}}, "0.0.0-experimental-e993439-20250407": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250407", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "379777a40b9b23bd059a50d8f4befccd09caf3d5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250407.tgz", "fileCount": 4, "integrity": "sha512-NGA5Gklh1ICBSVxZjgIR5L+IS6MQzxZvSl2ITD0RmTaATqEwxmdZFY9fNOWT5+29Y6cynmTlw2iU8iF+DXy5Zw==", "signatures": [{"sig": "MEYCIQC+CKpZnKkQU5xYk6SvJCruIVoIAxDw0DLBNf/qT/40SQIhALWJ0Yt6NSJbB13GHZoEAO4IsIDoBSjCfzSWn/6onB5f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-e993439-20250409": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-e993439-20250409", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "34a6c5713bfcd58a650da99abf725386c91adf8e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-e993439-20250409.tgz", "fileCount": 4, "integrity": "sha512-Xr0B9vnC6YG2ek1iQDEQWkT75gsXtiEcPvH70deFmiL3/nWXPVn9M0KtZTDrmloZ2c3if7rJ8rMb6zKTxolV1Q==", "signatures": [{"sig": "MEQCIGXnaPWlss10d8xhvlVyRINFVahRKTRKnoCz88LYnbSgAiBRVIV1lbvLDtGQIp4PcVGwywoIephs7EwekX7OnUQuHg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-ebf51a3-20250409": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ebf51a3-20250409", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "28539cf4fadf92a5e1bb21bd9c8942663ee2986a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ebf51a3-20250409.tgz", "fileCount": 4, "integrity": "sha512-Yz4796Sjnowp5KJpsyIbvTvdWAHZYUpg7GnOBUQD73PtAMJQxJdfkjyyANKLDVyttrT1MGG4uRtSdP1XqEwh4g==", "signatures": [{"sig": "MEYCIQCnyTMnfVpjjrKVBEL+g89EOmDwoNE52CB8EAt1YR2lQwIhAOOwdKMmjJDNVBcY04MdrYW6fiv2FdMFLihmoj89IWbT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-ebf51a3-20250411": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ebf51a3-20250411", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c95d354e6411ef7485474bb9cd65b97638ce928f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ebf51a3-20250411.tgz", "fileCount": 4, "integrity": "sha512-rPb+hPkE4Pbju9dOBOfmbgZOAtcotkTDb1W960rIS01MLi+x4cCT42b5qhIfoBSKVEz8dmMh2m08bB7C/qV2vw==", "signatures": [{"sig": "MEQCIDb6hEMiAeMwk2eC37DANbLCXP7+EUD1tKiBq2mWgBZ+AiBGgw7nTBqP832GDcdW5/iyKkNrWy/ZjnCdr6ZnvWw6yg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "19.0.0-beta-ebf51a3-20250411": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-ebf51a3-20250411", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "2b7b3202c10bac8ee42240f371dd9c52dc9ce968", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-ebf51a3-20250411.tgz", "fileCount": 4, "integrity": "sha512-q84bNR9JG1crykAlJUt5Ud0/5BUyMFuQww/mrwIQDFBaxsikqBDj3f/FNDsVd2iR26A1HvXKWPEIfgJDv8/V2g==", "signatures": [{"sig": "MEQCIFjK1nBSYKnUzWkJCZl8XpIEuPCiKRML5wzF0EiTpXf6AiAnHfrVDPR6bX0UD6fQ8gp4MlKw54uynjE5ombdnZIrsw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261292}}, "0.0.0-experimental-5122bdc-20250415": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-5122bdc-20250415", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e101e41c18dfa278e3cfd7396f5a15e6a8b7a506", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-5122bdc-20250415.tgz", "fileCount": 4, "integrity": "sha512-Yd65/5xSx7VEVHKwffRydXQDJ+FtvhpddD4jNDDqJbU8TUrKdmwJ7dTP7UrmSwQkFi9lx8hZI379yeib5ZYtsw==", "signatures": [{"sig": "MEUCIBBDSuLp6Rp3rmRZyesu6KDulUpPSA6URTLUg9HDC8r3AiEA6OwpXg2ub0p9AUqU6eOQBB6M/gZ229I7B2IHv0iZ28c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-b5179ab-20250415": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b5179ab-20250415", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "9e7de75828c7c6ae33383ef127d4409d70db5dae", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b5179ab-20250415.tgz", "fileCount": 4, "integrity": "sha512-HHH0zJ+gAENgjhJLjj1zsg+kzqMRdtkUroe/PYmINf4gb1I78i65ISptuufbWtmjYfcZ6hO/9n4ZQNr65Hrdcg==", "signatures": [{"sig": "MEQCIEewqti0Fop+wVoJiNnfkcSce2cBrk6L8JaGdR8DixwzAiBC0/rQxtag6nltBCCbrwJZtMyhuYzPlmKWyYMNWAomcA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-605ae83-20250416": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-605ae83-20250416", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "03899aa12196f87b015bcd2529becddcbe990aa1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-605ae83-20250416.tgz", "fileCount": 4, "integrity": "sha512-pZD6FFb0cfOGeRik18LNUeFQ8jD0UsSidSn0SybIgReiFZmz21ILkgqL2ZA2dqHrMSMOFtFEFkCXZBEP3qNZMw==", "signatures": [{"sig": "MEUCIQCJaF3as8MDe7sD1Dp/zJChA9lht/bg4viJzh8d5Ch+5wIgPXBK9eRZCBYXrhcr4p+JCJVxPUbw/KuMAPH6XUm0DYs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261299}}, "0.0.0-experimental-af1b7da-20250417": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-af1b7da-20250417", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "95fd62a839db361b3bb4685271d1f63cf592391c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-af1b7da-20250417.tgz", "fileCount": 4, "integrity": "sha512-w7JL/3HJQN+YDaYHFPiBg0qGIpl86E+G20EG4YVzmr+604R/GNGP634xVDW4TELXtc2eqgpYvraaajHDfZ/i6g==", "signatures": [{"sig": "MEYCIQDdb07SOup1ehIyv9867kGlNexIkqOa+Ppnj29Bc6HqGAIhAOoegpIgfjTBh6M5gehYUQfAIovzxn9BUoE+Vnz9S0Cs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261806}}, "19.0.0-beta-af1b7da-20250417": {"name": "babel-plugin-react-compiler", "version": "19.0.0-beta-af1b7da-20250417", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b87eb8c306f3d16bf20dfbba76a097fa82555566", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.0.0-beta-af1b7da-20250417.tgz", "fileCount": 4, "integrity": "sha512-UyTCRmzpxa4H1EqJk8fWeUOzHdEA12NQZ5DrF5hyhCs+Y6f7B4pg1fkul49sRn9GPPGFgkrH4IxOtnQJ7tNXIA==", "signatures": [{"sig": "MEUCIQCr148c+cfq5oH/3EwDM9xXcR6RHnLgQG97lluhC5x3BwIgC+NLKZsvbLdErmInBu5QD2Kps0uSXxHTIQjSx5oUJfE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261799}}, "19.1.0-rc.1-rc-af1b7da-20250421": {"name": "babel-plugin-react-compiler", "version": "19.1.0-rc.1-rc-af1b7da-20250421", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d52096e058c63293ced8b57d3f9b0395d1894ede", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.1.0-rc.1-rc-af1b7da-20250421.tgz", "fileCount": 4, "integrity": "sha512-E3kaokBhWDLf7ZD8fuYjYn0ZJHYZ+3EHtAWCdX2hl4lpu1z9S/Xr99sxhx2bTCVB41oIesz9FtM8f4INsrZaOw==", "signatures": [{"sig": "MEUCIACkj6e/Lv3xKl/01FNkPDfMUIJjNhRsPZL73J2r7sOcAiEAxFBQHwqgHtV9zWN6+wzHZzo+rEqUj+nJO3wcwiB3bRk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261802}, "deprecated": "Wrong version name was published"}, "19.1.0-rc.1-76db730-20250421": {"name": "babel-plugin-react-compiler", "version": "19.1.0-rc.1-76db730-20250421", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7dc657a335a18610dcc216a98bc7c4bcebd453ea", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.1.0-rc.1-76db730-20250421.tgz", "fileCount": 4, "integrity": "sha512-a8P7VkeloCljTrp7h2p9d3dpGUhzvGcwkQ69V5GYx5yZ6pQKEu+43llfv6HJrldXXq1tMaSceAkruQzmWQLdvQ==", "signatures": [{"sig": "MEUCIQC/u/lvn8KQ8uJ9TGMbV7XiGbfwPU+pkDNLuBdwVpHrvgIgN+PD3if3SPDG9Y8X2hvyzZj2gB2e+0ulnllcUDL+Mrc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261799}, "deprecated": "Wrong version name was published"}, "19.1.0-rc.1": {"name": "babel-plugin-react-compiler", "version": "19.1.0-rc.1", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "99d131be61017e40abbaedd98321069bf8b7e54a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.1.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-M4fpG+Hfq5gWzsJeeMErdRokzg0fdJ8IAk+JDhfB/WLT+U3WwJWR8edphypJrk447/JEvYu6DBFwsTn10bMW4Q==", "signatures": [{"sig": "MEUCIFaoWM7vsy0PDL87LrnvW5g1fWQJ9VbJ9qpB9uBR/kEjAiEA2cxhUVrpt6TIFCNDr0vRjso+zS55DFzWPlZD2ImXYfY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261782}}, "0.0.0-experimental-1113db2-20250421": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1113db2-20250421", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "58b79260417e5e2681dddcc5a1ac7ea3f8ee8743", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1113db2-20250421.tgz", "fileCount": 4, "integrity": "sha512-ujGTVp1UFtNdEzKsgZ6eSi+cvsMY3WnMy0S9AIY4+2EU8cWhEXTL2YtdHlYztbk6NtBCFC9OZfqMX9x4I3wgrQ==", "signatures": [{"sig": "MEUCIQDPNxC1fyOJ/uhUqfAg5TgOCjInjdgGgDtp5Rl1BiG+BQIgXzYNIUhWz6c8uHL7vgnb/Q4GIdbYRIuk6A/4NZ/SlJM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261806}}, "0.0.0-experimental-1113db2-20250423": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-1113db2-20250423", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b7a7bae5ce6d33aa8e43bef52e39cf1927179b3f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-1113db2-20250423.tgz", "fileCount": 4, "integrity": "sha512-MWb2RnoPmAlh/eIb/ozBlHluMM64LxGlhDzlT1hXjHwA5915JyjVlBhnwRdhQnfkhUudItiEpNtPBXEwcpC+iA==", "signatures": [{"sig": "MEYCIQCWJcftlGIglw46FhmhPa+bCzABQycym3pKqv6aqvuLlAIhAO3FyydjormfTxNQ5Y+vUmeHyEHZT0oTo2Y5V2fQ7TjW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5261806}}, "0.0.0-experimental-f69c469-20250424": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-f69c469-20250424", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ae2d33eee26f44048d4286797fe33001ae77bc68", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-f69c469-20250424.tgz", "fileCount": 4, "integrity": "sha512-soRquYT5T+QGpI9iOIlFTE41pHbH0QgjyQV2VVe9/84cARNR/RiRNUrJ2h1IvbJPWoWc/K3zAhnniX9Rdvk03g==", "signatures": [{"sig": "MEUCIA4HSezIjeLwXvzakqFAJgIuiu0UrzGw1k0wkjhuh1wRAiEA+ZREsucg4QtHhmrUq1K56bUnOv4rfa/CHKi1OuWSMwc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5264434}}, "0.0.0-experimental-2cd43d4-20250425": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-2cd43d4-20250425", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "41a6a2c320080f624aadabb1fb80049ecd0e15b0", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-2cd43d4-20250425.tgz", "fileCount": 4, "integrity": "sha512-NyP1GuWhu/C9lOXnJ3EkIQEmyspzHtKhYSdP/3N95sxUlMnwjioo5OvrlB2thSdUaGdYbBTE6dpeUtuv3YJU4Q==", "signatures": [{"sig": "MEUCIQCGU+34PC+bpmhbPxDo7PoWtqSdX+vhPhTOncfa+2AIjAIgHU20j1F506cDfV4TdZuh+1PV8/CioTHqU5WqxA26n/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5264434}}, "0.0.0-experimental-20087f9-20250426": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-20087f9-20250426", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "877ad7debc2dc7cc885f8d794177677d0e5f2353", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-20087f9-20250426.tgz", "fileCount": 4, "integrity": "sha512-7JCtIRQgn8yuL2Q7kWz2Yxnmhs6CzNbfCiNIbwABjXz+Sy4VIv7RQKq2H0DKuitNPxWJeyZO42WXqzIqAeH42g==", "signatures": [{"sig": "MEYCIQD+voElu+YaJwrTNe1F29HiG0AMlPznmRMGzAqkPW3JCwIhAP2ZMe5G6nCmvXvH/fftLRIQVrlIgnywDJ5iDwnjbeID", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5271573}}, "0.0.0-experimental-20087f9-20250429": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-20087f9-20250429", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "cb16b5f2c32e3d254239987909f6629857463301", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-20087f9-20250429.tgz", "fileCount": 4, "integrity": "sha512-pAPYwBzkOaiG9GZdftUwlzVbPiAiyREA47XB+7T64kpfieyMeR6BzO8BDkEDAyAHFj5MvKag5zlgn4UEgl2GfQ==", "signatures": [{"sig": "MEYCIQDb14hpyQcdMS3qrNjWWbNw4GM6WX1nrMvETH/7dnbiAgIhAP+/RCpveCJPFQSD1AMp2XyuGhOGuAtEukKaQ33cxi/a", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5271573}}, "0.0.0-experimental-20087f9-20250430": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-20087f9-20250430", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8ab79d2c04ad36ecd98c9562d9af28de8b9a55a8", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-20087f9-20250430.tgz", "fileCount": 4, "integrity": "sha512-vb7HAgFwLaJ6xzq0+f61ij2r+0NraxNvhaVnH2jqk8k5ALurDaMEJq8h3kkB+H2jO+4Vy+53M/FQCaN6hYDQzA==", "signatures": [{"sig": "MEUCIQCKYLhJhb9OkSZhkzIl5M0euMmJWRpvjv1P79A4bHeu3AIgBsiXi/pBdNAH1DJ1upUqhgLKZ3qnzbRwxmM718tn2DM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5271573}}, "0.0.0-experimental-63a5f0f-20250501": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-63a5f0f-20250501", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3c9aeda84c51a102be5a1dce51239ed3d2834308", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-63a5f0f-20250501.tgz", "fileCount": 4, "integrity": "sha512-A4VQQo1nPqhibHnl6DCmSPMCaSdR3VwGWaYb9gjtds/4jGMiAK6u8rRj6Z0Ok2wrt3B1+p+/jSf8To74xXJpag==", "signatures": [{"sig": "MEUCIQDzT8TVCtVNfRin4zXnT2zS6WaSwREtCwKcB1SjBQzbEAIgb+uFy4VZTPDxYVaMWedWvU2Lte5ZpSa8avTZEkAVFBQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3482957}}, "0.0.0-experimental-7bde208-20250505": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-7bde208-20250505", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "62aa247bfffc3006aa111296e2d2d4d804252a95", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-7bde208-20250505.tgz", "fileCount": 4, "integrity": "sha512-ppQUiZsnURfu+bPaqwrnThet79G2ocVOxOpxUQuflqky0kWBpLB9ON3RcLGuCP2jyrFGTv3ZR2TSmchL07FYxw==", "signatures": [{"sig": "MEQCIE+UCTm4g+GxkR1GtbkJjJxHyhjuMXcpmRbBC9BJ8dSLAiARlwQ/53TiiHFimvczISKD5rACxttJbF4XZOcN89D4NQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3493653}}, "0.0.0-experimental-bc310bf-20250506": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-bc310bf-20250506", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c0264f853a0627fa4fe1c6c6cd1a97d9159cd2d5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-bc310bf-20250506.tgz", "fileCount": 4, "integrity": "sha512-QAbLwVR0SBml8q/tzo1aqZNLH0OS6ICROkxesz7wUWAco+ylDFrg1SlKJLbtfPs0PD/EMZugK9BLSqJc/sOfbw==", "signatures": [{"sig": "MEQCIFs/aCFx/XklCEVDLDtjetLwBVrzHtt24Ppn+mXMRmjLAiBJv6s4tHSfeuCMtnJnB8aBdoNGmIqF2F8ooAHTWWNPVg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3493653}}, "0.0.0-experimental-bc310bf-20250507": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-bc310bf-20250507", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "3e3ce32d4c6f1da6a82c5fdc34bc7f8bd274198c", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-bc310bf-20250507.tgz", "fileCount": 4, "integrity": "sha512-nvuD1g+AYzeWfQ+iMIjOu8Q5l6sEkMrm9B4oJG6fyQMX5TClWVIpImqVHykcNVLjVxfsWXuew+Cr4DAc56Xtpw==", "signatures": [{"sig": "MEUCIC7O6lPyQ58ii15qPFsRWvjLQxhaAr6SVWM7a9eUHL/OAiEA7SC+kGcMbKW2fVbTKDQ3TbcUS9f2nImSVBYkNG+KQuk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3493653}}, "0.0.0-experimental-c134c19-20250508": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-c134c19-20250508", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e2f6c65b952549526d02adfd47fdb15bc8582b46", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-c134c19-20250508.tgz", "fileCount": 4, "integrity": "sha512-c<PERSON><PERSON>ckUosTp4oLWUAjlwLhCZ47uBtVExGTjbWEW77OSsXzc0ckh0eYule+JSM1yOVq7jBsi5mVg2WK15Xg0riAw==", "signatures": [{"sig": "MEUCIELXXBCABCWBJUPxV1apmK55xmcYQ20kzB/B4pLLFiGmAiEAgk+BTasoHtxHyOFo1UOaQE1QRCCYu85WAFK4cypurEA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3494977}}, "0.0.0-experimental-ec81157-20250508": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ec81157-20250508", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "1db81425c1b6033b1f37db0062de4f62ccd87596", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ec81157-20250508.tgz", "fileCount": 4, "integrity": "sha512-q4eRRvVkSRMp/MMpVNJTAryJYJZbAsgs8S21FDwJ3Y6HjYyGYYoy25ccC22hLGcDqt8jFeB8NJ9SkdvLXSLIJQ==", "signatures": [{"sig": "MEQCICUpKjSCWIiDqb46hg2PtW5UFyyaOTJM4ZvEfOyGEkl5AiBDpnWFpfsiOYw+2ky0uh1Q7oSbHBsmFR5M+9NaR7Rhcw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3496420}}, "0.0.0-experimental-ccdb475-20250509": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ccdb475-20250509", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "86257e0ae1498006cb1878ba610052bc548fe2f1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ccdb475-20250509.tgz", "fileCount": 4, "integrity": "sha512-UhRDWu2brDork5WV9X8U55yYd40wGwVaNLTCyDwrxXr0Efxw96u0d49JDvGzDJjAtXPvctmMn0sh3bFyN7O6yw==", "signatures": [{"sig": "MEQCIDN4hlIzBLNP2HN/1bGZIe0sJynElfr0MFBP/ZCoFrx2AiBbiIkDXxJHfQQjCAC2jZ+egGXg1L1bZ/5JFoq1ngcdSQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498762}}, "0.0.0-experimental-ccdb475-20250513": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-ccdb475-20250513", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "50d1030a321cc7780eae9ce61a44684d81a6b413", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-ccdb475-20250513.tgz", "fileCount": 4, "integrity": "sha512-C<PERSON>ycfikXKgFz4C5yti3qYIXz81d3ZDPf6hvzuQuTkuyfcpVrXCEqHawotCMxy7UeF75Z50OknqofFjvwHi3S0Q==", "signatures": [{"sig": "MEUCIQDj3xRhF1TZgTAMDvLnDuKknczGLkpDC+dkRMLZ6m3ZfQIgYZYfvNHJKosxgiuueKION3T4O/J1Lv02Yp4eYTJj1j4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498762}}, "0.0.0-experimental-52ea7f9-20250514": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-52ea7f9-20250514", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "bbde89636c23d0c4300433eb826bebe771a7f52f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-52ea7f9-20250514.tgz", "fileCount": 4, "integrity": "sha512-Q2Y1uEyE8LY/XGI4Bm/zJ5EIUhS3zoqyNJWHg2DFMc5tPacCEKvAdSAPMTXsc7Vfdi5JmqH7ZN9pxITaYowZDA==", "signatures": [{"sig": "MEUCIB892A4f5oiBJPwZFSaLogZLo99s86HeQgE0C4CYLG5SAiEAkTjs58rl8xOa1NTW8bIrXKZZyBTWdSeN3njGK3LqmHs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498762}}, "19.1.0-rc.2": {"name": "babel-plugin-react-compiler", "version": "19.1.0-rc.2", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ef77f4316a2086d81f95ad8edbe081e2840d87a4", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-19.1.0-rc.2.tgz", "fileCount": 4, "integrity": "sha512-kSNA//p5fMO6ypG8EkEVPIqAjwIXm5tMjfD1XRPL/sRjYSbJ6UsvORfaeolNWnZ9n310aM0xJP7peW26BuCVzA==", "signatures": [{"sig": "MEQCIGmhJIsBrCiCeB5CFOL/N6nKTmhIewvgPIfrnmxBPUTHAiAqLmkydNVQaHERw3jvAarKNtmo3p56JeYkPA7ITB5s5Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498738}}, "0.0.0-experimental-b1267e9-20250515": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b1267e9-20250515", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "bfe097a414cfefc092a70781cce996fd7da60c6f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b1267e9-20250515.tgz", "fileCount": 4, "integrity": "sha512-oF67ElUYIDPxNlU427kYdjyRJc1Dascvu+Rj+9du3o8H2q4Npasu4NyBLefI6q52jQPbjmaBixiBElW7y/uZqg==", "signatures": [{"sig": "MEUCIH1O3zTyIzSLl0/+TX70Qa4O8sRaqLm/lb6Ooq1m1oSjAiEAhVtiKIAqZLwmXmTtp/7polkKl49bQSaJ6HpnfxRgz8k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498762}}, "0.0.0-experimental-b1267e9-20250517": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b1267e9-20250517", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "fd1431785e04bb8ec911289f42128e8427eacc14", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b1267e9-20250517.tgz", "fileCount": 4, "integrity": "sha512-8G7qBVfJqIsxDfKqPz+Sxko2EjLi1fsrk0kPAOYRHTnKw16KHIAAR36vXBoJhyyGdZwDzKXK934u09Y6PQfJJA==", "signatures": [{"sig": "MEUCIQCuoEkiirpKeW+RqA5M7CMPmmqNI/lzHC4ToNOd0aabgwIgHpYMfKFt9yWaK3NOvrMuvhz1mibgxztPVEFJtxUpw9M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498762}}, "0.0.0-experimental-9627644-20250520": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-9627644-20250520", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d63fe4e764548d4f224fb86307a8a6944d970c47", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-9627644-20250520.tgz", "fileCount": 4, "integrity": "sha512-EH/AFGKbD/Xxy63xAe4SUgSvx4goqMz27OcgGlgfn0FxxdaNCV65+1D6csAe0a79XmA+IFzdCy+j+7XQshUhyQ==", "signatures": [{"sig": "MEUCIFtYIUD0DnFQ+ofI7PocP+OJeQLUdhjPrR1RxiBP61hGAiEA9JcH4BFoEPZKCNB8DKJVCwQ05qaqYOYOkyVgvESp/Z0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498796}}, "0.0.0-experimental-9e9cd66-20250520": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-9e9cd66-20250520", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "012d4ddfe3460a8e69498725e94694baf231d57e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-9e9cd66-20250520.tgz", "fileCount": 4, "integrity": "sha512-CTAxLza97ZnDjx678rQJbLjIcwrricxXv14wNZ65BCgSrHsidvP6zWUuWYRNkce3g0XJgyniOmnCxp5tCje5jw==", "signatures": [{"sig": "MEUCICREM3u8NAv4MLtpBQkbDct9UHy/4O0+s6tyT+g/2gLhAiEAt1H/gslQSRSediYBDUls9WMtl6VLzmY65UkmQFW+Uwk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3498796}}, "0.0.0-experimental-f980e29-20250522": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-f980e29-20250522", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f2b1b375b6ac6578d79c7a5e6b20eea2b8411170", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-f980e29-20250522.tgz", "fileCount": 4, "integrity": "sha512-H1ZXNJ3HJhO5X16lj0BXNWgj/rRx0DQAztXzkBCoVO2BYrwNHqeecaa8zQkkNkxc9+BXKYw2LwSnkz5dCEfYdQ==", "signatures": [{"sig": "MEQCIGPk1p+QVyIe9a2U7E0F8QwauZcn/gAY0tPumTzUVF/KAiAXwNX26oNgp/0NzP0PM2dx204TI1AlLhj/gcS42Nxjpg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3502635}}, "0.0.0-experimental-8f8b1db-20250523": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-8f8b1db-20250523", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f093e00165731d19eb8c0a85d44f68c590e51eae", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-8f8b1db-20250523.tgz", "fileCount": 4, "integrity": "sha512-0ESQ3EQIoFm6ZNjmvfJFbqyEpbGT6TwinwTutgjS7b8PH7hQ3Hd8B43OwbAwb7bgpt/VsfSnqPsRp25HzwlU1Q==", "signatures": [{"sig": "MEUCIDiDB2pQ3/joT8aQLAtX7Qj0gAat4BaqFyI5+wk9gcOvAiEA2+gTv4d0Yx81tWujqaJi6m54265ApDdD2sHvDPZzyh4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3511729}}, "0.0.0-experimental-a550a97-20250527": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a550a97-20250527", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "f02bb3bce95d041e1ad2027ff76042b118b7e3c0", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a550a97-20250527.tgz", "fileCount": 4, "integrity": "sha512-8ABdsuLAPgr9mlJy6cB39+SnMIzdJCfyyA0yqBGe42mlFZmrkpJESWdU9mywLRNl8caOJfUvcaqmvo+THAzeOg==", "signatures": [{"sig": "MEUCIBuOIbT8JgCp2O9pnR3Rh/8qriRCNc6mSJAbckDgd5LJAiEA6CTTBf0eDKwGJu/H0sZdWdSGvni7g1bOGKhbODQGJFE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512789}}, "0.0.0-experimental-a550a97-20250528": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a550a97-20250528", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "1bbf2ab11bab3857b8d43b946cfbe43e834c2028", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a550a97-20250528.tgz", "fileCount": 4, "integrity": "sha512-m+RhBPnwltD3a5hHGPIR/7c5IGGEPFR1qtMVgKSjFbJdt1EgGmP9lR5CP5rEJVNHW78Fh0HJMisn5uTJJrfIdA==", "signatures": [{"sig": "MEQCIH0FsTMEgDoPQDX736G0pAq3aclUD1tiH8lBnjziqOImAiBUHYon+2twaVxG+ekbS6XKRk0Y+on6dYmnRqq2T7SwWw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512789}}, "0.0.0-experimental-a550a97-20250529": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-a550a97-20250529", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "22b12f71c2cdbcb47a90863988e3705955fe8b4e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-a550a97-20250529.tgz", "fileCount": 4, "integrity": "sha512-wezdBjBsv7lCyr/NUq82VmgUX1QxJ4IWM3WP4JXwmQ0wuYpnzIhryEL8iX6onJtev9OVf0ZmfQSNbjwTqBcQyw==", "signatures": [{"sig": "MEQCIGxRo4BTSRLUG5P//IYvDZwsNuuXgTgwtyNvYrAAYaW2AiAIPw+tOoLairAncNSYsumAgajAwin5QTe3NtM++AocZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512789}}, "0.0.0-experimental-b730932-20250602": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-b730932-20250602", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7c69a7fffd8759a60b5c71b066a43dd7c7f431ef", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-b730932-20250602.tgz", "fileCount": 4, "integrity": "sha512-sRpAt1P0TZcIytvG7qplzDCs9hWxRYikynSGMktnAi/VxISsOIwvxwPyapI+jFv98A83HovbJ2NinxylFMCmPw==", "signatures": [{"sig": "MEQCIClD0HAc92m8rvSsSHVHbz/f3kwbI81ff2UIwmOEdFY5AiA9y/8nAKyM36kw6FjpZEIs6bKNmrBpW4icH6NakN4m1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512888}}, "0.0.0-experimental-0fa1c14-20250603": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0fa1c14-20250603", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "bd0333d11f3828ad0ad9089ee6e60b26195ddb87", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0fa1c14-20250603.tgz", "fileCount": 4, "integrity": "sha512-WNZgDPbGQYjVY0Bp7Ip/XuVTHTxAzjEB+SNYLwE332ieERmIIOCicjbA4MkM8p/s5pHA92wak5WhHtLwK3zS9w==", "signatures": [{"sig": "MEYCIQDHECEiLzxus3ArEl4nMPAaSdIpt6InKaY1NHMlGUZ8egIhAKP1BMmlVgEhVsi2luFBhWgPKjagvkTgO1kehDXthJj5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512888}}, "0.0.0-experimental-0fa1c14-20250604": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0fa1c14-20250604", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8cacc9e7ab4446675dfbd9d5b061449d3b9cb861", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0fa1c14-20250604.tgz", "fileCount": 4, "integrity": "sha512-JO7Fn4PqkpPTTrczoTv3JwZH+Sv7GLX+UMYQuEWE/nTP3IaA//0v+7Gm0d6k8Alx1Q1Ux3dy1Gx4LGyV/he21A==", "signatures": [{"sig": "MEYCIQDRF2o+WGV0WHlBSs2zBNXTIeccYfjdAWGyJiyg8kQKEwIhALdTaWrWmmQ8J04GdlKwILbme2TIL4QFoUKYdGG7GeIq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512888}}, "0.0.0-experimental-0fa1c14-20250605": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0fa1c14-20250605", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "33147a334e70f3d7c9891dcb4b392365cb689987", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0fa1c14-20250605.tgz", "fileCount": 4, "integrity": "sha512-CFa5CxZt6gUtZahZwuZb3vYHFvgVMo3HjN6iUsuZ+OAKX3XhgRUdF1qJJh1Rj/goyEE7Gn3I/PRky2yfkUMQOQ==", "signatures": [{"sig": "MEUCIFGdgHpFehw+tScpwBhrauqQnjGARWGYMjPUtKekbS0OAiEAhAEE8jtqiVNIoihAFNCWJCKHVOUGtRPTRQope6H3hKE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512888}}, "0.0.0-experimental-0fa1c14-20250606": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0fa1c14-20250606", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "cb3d1300e31eeb561c684b1f29165af3c451843e", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0fa1c14-20250606.tgz", "fileCount": 4, "integrity": "sha512-zn6cmYNiu4C2uTkFbpMpD7AUEu4Dqej08znfiLHLMTDiTRlpFBlhBbNTgUflAoGDeBJrjqWR2+wdGrpY4bBFcg==", "signatures": [{"sig": "MEYCIQCQyM1DRNDVddoIS5f818tEDac0bVBPAVLLUgo6JiM2mAIhAKbvRFllnVzBM6cXDm1qqXC8MF+0rKcey/LbYAtZODMc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3512888}}, "0.0.0-experimental-87dbe21-20250609": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-87dbe21-20250609", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0615cf7dd42cd00686c9683f6d239a8937b0f624", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-87dbe21-20250609.tgz", "fileCount": 4, "integrity": "sha512-IfK/opx3WipqxD1R4w5nsOierEqjix/27Shy7wRHOR1bJK9cVUcS6LZOTt75CoBFGtzLTOp5Od+TsX3dJ7GAKw==", "signatures": [{"sig": "MEQCIQD0z1tmRI/EeX65r5Wvhul8xd4UuOqN0259e6HalAY4rwIfTW+GD85T+Z7pt82TjXVbi+tkTsTdJAltGeAbWqLDFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514042}}, "0.0.0-experimental-87dbe21-20250610": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-87dbe21-20250610", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "de8b819a7aca60ad84f7ffce7b72d01275a4491f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-87dbe21-20250610.tgz", "fileCount": 4, "integrity": "sha512-4QXqE3bdOgDY69fXqGOMcR/W8IDSME/myvDdNTSsdExrJ7BDkrFNsSU9z+iXf8+ufi9CuNHeReMne9MTeMByVA==", "signatures": [{"sig": "MEUCIDj8N29DL5gKsVFCYX/FC2dU5kIxKGBns8GsmJbByPGoAiEAqYJJnSeuF1H5A4+pxjVQvM81f9ReTxlbvHqJwhFJs/Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514042}}, "0.0.0-experimental-87dbe21-20250611": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-87dbe21-20250611", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ff3e4f1bf7545f310b5405304291b7c2101cff55", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-87dbe21-20250611.tgz", "fileCount": 4, "integrity": "sha512-oUFDpcuwFy3KIzosOrQ5X8cFbExVx1VvX9OVaF8Z1UL2ePOVEutr8AgZ7lZxjjhWWoyMlW1LxI/Hs/DxPMdONA==", "signatures": [{"sig": "MEQCIEpwnBrqi+hy5qGA8SA//SLvcmiKqt8xkNGsUCRZX4lPAiB83av9D3+ITWR/OzXUpt/cWXTf20T3Ee2JEYJPbtuArA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514042}}, "0.0.0-experimental-4b12060-20250611": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4b12060-20250611", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "2ed6a44ae4a9b45341c7db8316e9dda3eb26bedc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4b12060-20250611.tgz", "fileCount": 4, "integrity": "sha512-KZoKh/QEXot6EKzC/N63PS1cPp2NFaVjmgEe8iSYwRsgisB89KUlIlHqyb8E88bSLMv38oY8L7hcqdAmo52nog==", "signatures": [{"sig": "MEUCIQCnhv9I1OF94QDMplt2XjXjmVLDsc6oBCTvDV50WM/oiAIgH48EaOCb+LuaQgwR4RUTQXblJHr5H+PTfpsp5zDk5is=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514042}}, "0.0.0-experimental-4b12060-20250613": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-4b12060-20250613", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c10a1724aa73bbd54abbcd07b5c1a3dc5be4c03f", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-4b12060-20250613.tgz", "fileCount": 4, "integrity": "sha512-MmkykAg226J2B2easo7kQNwSa0Twd0a+mDNrgJ+P0ThZsYOR13miiH7huHhdTKwLrZfAieMh+TB6EL0mAb7ocA==", "signatures": [{"sig": "MEUCIFxmf/hZkgf5scKbfdWYDak7UK2pASieRzoJoMPwLYjeAiEAv2cpUsK5xs67FQAbnUbJShRQUeVBkI7yH8kZyQ2aYTg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514042}}, "0.0.0-experimental-14f9687-20250616": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-14f9687-20250616", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "12ebe16faed35bbc3af4e3d5e97222ab4e5ffa80", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-14f9687-20250616.tgz", "fileCount": 4, "integrity": "sha512-Y2JULgauK7mwazNKiaCI588NmGYB9mPkkg7yqVl9zPSS1yxB2UqoGNx4Ax3k/jqfRbjqpEScoj8vf3487yJeGg==", "signatures": [{"sig": "MEYCIQCHGMYlvFYE9ibIafKkqtbsBntViquJOmNTBGLyJTpnjgIhAKYelwv4Qwm71NN0IaibrokgPovmVwAX4JyxOZsurPER", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514208}}, "0.0.0-experimental-14f9687-20250617": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-14f9687-20250617", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8e42cb57991c0c5422adf1d119cd07c8e36d4339", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-14f9687-20250617.tgz", "fileCount": 4, "integrity": "sha512-LyT7372QqDriHSKLjKLSeMLCt1I1doyXb1GQq2vzV0JI1GRSfUF/FlKc7eMHCXzJE13Nmw4BvDIaccmSxZ1okA==", "signatures": [{"sig": "MEQCIBnReZoc2S0vfG+I86m+cpE/qtZD89OU0c80+1NTTzlzAiAWheoPBB2bbMXPUFkAQKai4eM5uZiUx2u56rhxFNhu1w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3514208}}, "0.0.0-experimental-40396aa-20250619": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40396aa-20250619", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "b0f16b037bcf24fcd70d28a0b537b6fb6c8e1ac4", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40396aa-20250619.tgz", "fileCount": 4, "integrity": "sha512-HIR44tRKKn+TA1MA9qd/hH1mDMVzqQdhRmjbSJfuA2Vfga4mmQlx6Jk81rKG/wjMlHPXM1rvzTTfi0DL+AcVhA==", "signatures": [{"sig": "MEYCIQCFi9wNhiEduQKOCvFgg7H+0t0kqRDNEnVf2qyCJyWNEwIhAJRz/830EvgiLOkN75qvievK/JRPZTYwlSGAdh/VD3tg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3625672}}, "0.0.0-experimental-40396aa-20250620": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40396aa-20250620", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "337f792c00b4808e114848546ef775920df160fc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40396aa-20250620.tgz", "fileCount": 4, "integrity": "sha512-0v5Ujt6dtS3SUZzc3l+w2kNQpfqbg41cM/3o29M7EYHNgOIqNyh3r5zKut0FsqlWvrEwmq9GysMOD1iCTn3gxg==", "signatures": [{"sig": "MEUCIGfXTm2XI+DzvMz2dSnv3x3oQbqXwGsTPbxNqo+WF+EZAiEA4c8CEioxP1kc25y6lnDUA9RlCI+2vbKZ2qV8FVA5FXA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3625672}}, "0.0.0-experimental-40396aa-20250623": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40396aa-20250623", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "a1cb72fc61b777e0a6c437cb7122c597d0b53545", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40396aa-20250623.tgz", "fileCount": 4, "integrity": "sha512-GDw+IdUR0lyaHuQieUOTcOoNaeoQnsdoFIZcUHHZnzJZPM9rE3tTW0V6QIZZvxH4p267rdKP/ndAMzIhowpN4w==", "signatures": [{"sig": "MEQCIC7OcY/u/EmxSb1gXbjxXlRsQvSNIJCxnHOTVioehIayAiA0sBOKHL0ZlaomuGmdUo6m2UFdYT+U/JViL9Cvahf77Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3625672}}, "0.0.0-experimental-40396aa-20250624": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-40396aa-20250624", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c12108e7e4145676472157bb9e447dc56a6db678", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-40396aa-20250624.tgz", "fileCount": 4, "integrity": "sha512-fzwvmgdSGHLmEBntE1alCa4IqB7UVHrLNmnwaBF+WOMBwUWJolVPZ3GdjJ/sZWomyx/2UMUjciRb/WCjJgNLYQ==", "signatures": [{"sig": "MEYCIQDwEaCZROQu7gOoW3et+/GwtazHKuM5eDV2SbyMwulWdAIhAJqRPbImea07DOmchbIiLwnwraZE1tNu0YvZeAytVnXo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3625672}}, "0.0.0-experimental-32d3781-20250625": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-32d3781-20250625", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "7e09c00fd38d324e0e76e31edeb89f288eeb1afc", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-32d3781-20250625.tgz", "fileCount": 4, "integrity": "sha512-wTPsJzYqHKxuY7NYJhdK04gFUT0AHBTYCBuWiL++uRcUMlGmlR8lD/M5zpx7JhNYjM0YNAcLSyl0ie8ahVKq+A==", "signatures": [{"sig": "MEYCIQCKHcbhEBo2xXWmNP4hCwBNuI5kLs4qO2rXUUxiTlUF/QIhAIfKNam7WcNt+gAr80W8NrR/inCnniZ249uKwXGs10s7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623264}}, "0.0.0-experimental-02d78bb-20250626": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250626", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "ad07a83b7b8d62daeb5d9a1748763b50209221ee", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250626.tgz", "fileCount": 4, "integrity": "sha512-wdoa0NNxaCl6LV7VX3GGkyuamxdh63YDlSKH3WfxCubNTj1/CXk0L5MOHVYFGTxK7pzcscVmM4Hi4BKhyaE0/A==", "signatures": [{"sig": "MEUCIG0VDJcgT1hyclZ+u9BrwEQ7eY/g+YL7/mDPdzqBwsV+AiEAogs62gV4c//fUASCcm8h324LThvv2Yp0bhHthMpKNLs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250627": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250627", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "8fca6dfb80d1fe6b1db61fe0efea44177ad6f8fe", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250627.tgz", "fileCount": 4, "integrity": "sha512-8oAj7DgonkKCHivnTNcsuezkxxQON+j5akCMXsFeiulhVwT+oX0Q6/M41rFPcJLtUFoKS9xCwVnn4B6gc4rK2A==", "signatures": [{"sig": "MEYCIQDQaZsWPtSh1sP3kyHmma5nsqX8AgvESnI4EXn+D1er+wIhAMB5gm5rNOxUUMFSSm7BjrHFmcU91PUa2+tJ4F5L+lrS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250630": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250630", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "1c3f94d4a11eea306dd0dc32b7026d5aa4c85f61", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250630.tgz", "fileCount": 4, "integrity": "sha512-RkTrNRY0axdujdUt3r0ETRKTLMAbJEmgnRZ3BmKNYiG3USualzP1ymxnFo44wv1zSailZKgwWT4VubmnRE/yCQ==", "signatures": [{"sig": "MEYCIQDqKfQTX2n9x0MRgNBW13eK4ZgT9bLHB52gFX7CArCyiQIhAImCp8Fu8vj+A9gLnHg8okk2/ITuUaYMMIgVtivbQV+v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250702": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250702", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "18eb3491bb6114445b8bcbb5138791af4f93812b", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250702.tgz", "fileCount": 4, "integrity": "sha512-ASfNVeqlW2liWR+MLqDQHcY2YL0gd5tnnjS6kHhAGKGuxo0EV8XH+SATOtwNwwR9Cfzk9a8tdbRH0j0mIyNGyw==", "signatures": [{"sig": "MEUCIQCChoummfnTIl8EltSIGUPQ6WZIM0dbrLC0DnDN7TLSKQIgAJDok1nIZC9+Cn7W7AtEKMm7PZnomULfwZD3xe43m9c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250704": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250704", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "e7f8103d9f3b1eff4680fc72ae58f5c8b3a1b6e5", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250704.tgz", "fileCount": 4, "integrity": "sha512-FRVhV/odMCO2gAelkcxFBDCLnOZslz7CZaeu7YbQ5qjMLGWLzFSTwPJ7dEL/yPjhcdb3tfkVHADOwBEJ66rJNg==", "signatures": [{"sig": "MEUCIHM1IFG9FHL1OUDfI2LquVCuCNLKjIGeQ+ojtsUfzPp8AiEAhZrnHE/LJnVqMw2sH2MCA9CAtAA1XJrpQVda3G1PffA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250707": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250707", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "0c8c118ebccd885a7aea11a2b71a741737f7481d", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250707.tgz", "fileCount": 4, "integrity": "sha512-byRPtltLA2Juwk7ckRalobw5NTFO5KSb70Vn5Xgl8puBTcNXSrt/jIud/uThMpkq+GFHyJahgPPhWOVVGVLDmA==", "signatures": [{"sig": "MEUCIGiWGAhH1fdXo9v6eZtfaydR11ZfzkGFCQCsv6qVBaYTAiEAs0aSVnoGpcXZcKlQc2OVIQ8bK4pnKJI08acFZ5vWX84=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-02d78bb-20250708": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-02d78bb-20250708", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "20561f688ecb3d7542e15d378d1de51eecfbd9e1", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-02d78bb-20250708.tgz", "fileCount": 4, "integrity": "sha512-wLBsSJBGZqFL6MnASCNOvML08Yfvte8HDE0ruz5BZEpZ+ShbM0Gx/YCWBo69kKVfUOplGbxxecl2glzNC0RpVQ==", "signatures": [{"sig": "MEUCICOYs+YIePTj98dxaLoBBobXNSXTiP7za4OdjayvivFmAiEA0p61rViPNoEKJ6OuEKxqT40e779TTtOcICykClXDWRE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3623417}}, "0.0.0-experimental-0566679-20250709": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-0566679-20250709", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "c1d93b2cac34febcc597d95d33dd538542deb65a", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-0566679-20250709.tgz", "fileCount": 4, "integrity": "sha512-hmaX7GAsWRy4dSEX8eE0KJqLPJKEhHxj9dULwsVM1H2K0wB9PZ+Quj+RZ3G9hLeUmK882nwe1yb+P9hDbMM5Lg==", "signatures": [{"sig": "MEUCIBBnp9cp/DZ192Zt+RowPL/6Rv/1xXMp98i+EYQnkryQAiEAmYUnQcM8aZ+Lv+nfRFp1tjlOjyEiy76GdH/5qCG7Oqc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3626493}}, "0.0.0-experimental-acd39a6-20250709": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-acd39a6-20250709", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"zod": "^3.22.4", "jest": "^29.0.3", "react": "0.0.0-experimental-4beb1fd8-20241118", "eslint": "^8.57.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "invariant": "^2.2.4", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "babel-jest": "^29.0.3", "@babel/core": "^7.2.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@babel/parser": "^7.2.0", "pretty-format": "^24", "@babel/traverse": "^7.2.0", "@babel/generator": "7.2.0", "@types/invariant": "^2.2.35", "babel-plugin-fbt": "^1.0.0", "@babel/preset-react": "^7.18.6", "zod-validation-error": "^2.1.0", "@testing-library/react": "^13.4.0", "jest-environment-jsdom": "^29.0.3", "@babel/preset-typescript": "^7.18.6", "babel-plugin-fbt-runtime": "^1.0.0", "@typescript-eslint/parser": "^8.7.0", "@tsconfig/node18-strictest": "^1.0.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@typescript-eslint/eslint-plugin": "^8.7.0", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6"}, "dist": {"shasum": "d3ad9561bec886c0d3658d5f34b89b2491eaca99", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-acd39a6-20250709.tgz", "fileCount": 4, "integrity": "sha512-gfffB+GZwts8tHQ37jvmnZKtOyB4Sy4GLSzVkyBfdLGcDvixVQ6d9HDDy4yZC3Ba6E0Uc42DVKODmMuaJmG0ig==", "signatures": [{"sig": "MEUCIQCJDBFEvkgCbQyN+5gTClUkBUiiX1CMPytuW+6PG5MH7gIge5VuE1Jb9NsQ4g5bMJ4PuSKJAzPzb6ZWdbT98XNhHAM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3488634}}, "0.0.0-experimental-acd39a6-20250710": {"name": "babel-plugin-react-compiler", "version": "0.0.0-experimental-acd39a6-20250710", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/generator": "7.2.0", "@babel/parser": "^7.2.0", "@babel/plugin-syntax-typescript": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/traverse": "^7.2.0", "@testing-library/react": "^13.4.0", "@tsconfig/node18-strictest": "^1.0.0", "@types/glob": "^8.1.0", "@types/invariant": "^2.2.35", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "babel-jest": "^29.0.3", "babel-plugin-fbt": "^1.0.0", "babel-plugin-fbt-runtime": "^1.0.0", "eslint": "^8.57.1", "invariant": "^2.2.4", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "pretty-format": "^24", "react": "0.0.0-experimental-4beb1fd8-20241118", "react-dom": "0.0.0-experimental-4beb1fd8-20241118", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "zod": "^3.22.4", "zod-validation-error": "^2.1.0"}, "dist": {"integrity": "sha512-IJrsKZQ8RtvDCh2ErXN/5Y76XH60g6MLjpWaW0Q3a07jJA1R2KuTM8JfaeHt+r2kGEuhiL1NJqh1d3mX7nrBIA==", "shasum": "24208baf3dc75944198aa06049012a6f25e92e88", "tarball": "https://registry.npmjs.org/babel-plugin-react-compiler/-/babel-plugin-react-compiler-0.0.0-experimental-acd39a6-20250710.tgz", "fileCount": 4, "unpackedSize": 3488634, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCl7mpmV4GFUAJR5J726ofqwI5TUKCQBBDquCWituaFtQIhAOKzndM7I9iKAcUJG521EM+MlrBdInDWG9LLvAb+3i6c"}]}}}, "modified": "2025-07-11T16:17:49.392Z"}