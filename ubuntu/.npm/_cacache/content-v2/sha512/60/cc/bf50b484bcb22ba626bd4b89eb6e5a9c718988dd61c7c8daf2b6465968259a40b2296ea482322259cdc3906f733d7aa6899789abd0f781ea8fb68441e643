{"name": "@img/sharp-darwin-arm64", "dist-tags": {"latest": "0.34.3", "next": "0.34.3-rc.1"}, "versions": {"0.33.0-alpha.9": {"name": "@img/sharp-darwin-arm64", "version": "0.33.0-alpha.9", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "0.0.1"}, "dist": {"shasum": "155b0dc99d4006ee715caa021e65b1a043b05498", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-snNXPkmZzFv/A5j4GRtyxZJx+uEFtM4/L7gjcsCilKZsN3ATlRWELr/+dwEj+LTRk+IA2xI6LLHATuN2ci2XMA==", "signatures": [{"sig": "MEYCIQCvwzBEYZHN6siBBiJwWvjP5f6LoE7coGT+lg2z9iWJpwIhANijkIL9Lj6ZPJGYL9dgtMRE56EAdbu5TYfV+6AYmtS9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255987}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.0-alpha.10": {"name": "@img/sharp-darwin-arm64", "version": "0.33.0-alpha.10", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "0.0.3"}, "dist": {"shasum": "f9b76b177659b78039d8dcf927b83910eed836f0", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-Wa8D1gMI/ufqPJ/RJttyhBpaK0t2qnk2qu7TY+MbssE6MLOWbu5Er8Vam6uVH0gPJPlryjcutJvyy7kAxjLMZg==", "signatures": [{"sig": "MEUCIQDcTHqV9KAU4ylOvLsqqr5hgrlPE+1YW700qJqHJ2lU/gIgCZie7OJyKauvQgEDBWSqrPr8axamKkXrehaiZ/8xbLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255902}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.0-alpha.11": {"name": "@img/sharp-darwin-arm64", "version": "0.33.0-alpha.11", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "0.0.3"}, "dist": {"shasum": "d57376d41cb15b58b73c4ce228bc4e19cdaf6c56", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-Ews2u6p/GNMWYKkvFMVN5deYQQOnJ/UfADW8mDThKy7hncWhDK05B5Li2Qdko9YmZHzQFWxOSnV4y0/BjPhomw==", "signatures": [{"sig": "MEQCIEq6YdesSjTLigbgGgcDC3dIMs8XwhY2zzuB6Tthd+OTAiBEuoyI4jxs8WQ9OAnID9OHJi6T13rmbcv6Hg5uCh2qXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255918}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.0-rc.2": {"name": "@img/sharp-darwin-arm64", "version": "0.33.0-rc.2", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "e6fe3e7237edfae6083c7de5630c4f5b0bd8b748", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.0-rc.2.tgz", "fileCount": 4, "integrity": "sha512-udQHoL92wYbVKzrHR4wXVQyhsgzL8LiIN9UC9eskSnnghVyWBOp24Fr2g3SJROwsODRg8ll8OBtrdwZvYXQE4g==", "signatures": [{"sig": "MEYCIQCuURNDaaP1iendUlhuAXZE8TL3Nq7paghbwVEMR+RXoQIhAO23v14WrSwVdMOOvOtssklmzhphCCVakIeqdG5YTztE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "b3e4ed1887f53faca5e95e3fd9f25181432353ed", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.0.tgz", "fileCount": 4, "integrity": "sha512-070tEheekI1LJWTGPC9WlQEa5UoKTXzzlORBHMX4TbfUxMiL336YHR8vBEUNsjse0RJCX8dZ4ZXwT595aEF1ug==", "signatures": [{"sig": "MEUCIQCID5uCC3Aad/OEzLTlN7+cgCWM9eF0t3yk3w+THAo1PAIgLJB7Ua72CZAlLFkz2HnYOAH7mbAdS6g+uGyPuZy2sOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274085}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.1-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.1-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "c168ab01117e0bd20c6086665e57ca59919f6957", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.1-rc.0.tgz", "fileCount": 4, "integrity": "sha512-QrC6WMZCrkCQ4GA+iXkq46DLvEAGgeoQMrqmmtiBTh2RwQGxWi0+JabSICAn7MiI93s/iAu9Y17uA943Yf9X/Q==", "signatures": [{"sig": "MEQCIGS+RNsglz30p+lWfVIvDoxZ68ii3OKF4cbbOTKcFC/bAiA4RhvEDfNuxvlXGcnlvMBLyb4L0va6mlPIeIz/vT+4mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.1-rc.2": {"name": "@img/sharp-darwin-arm64", "version": "0.33.1-rc.2", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "837e8a25ae60f7a0998325b236637b1c5d95b8c1", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.1-rc.2.tgz", "fileCount": 4, "integrity": "sha512-sauqLF/KyhZ3GaVLgrNw2hxIp7wiPQrQUmW0nXrSiQJXuh+xjIpYbRbiaUs6dV9PEvgDTxX1GSraqCKFXhF/NQ==", "signatures": [{"sig": "MEUCIQCO4P2mkZ7PIqX8idWJiYEjwkX8uSb4/Un5OFtn769kKQIga045xggsfXy3DvCCGP849ldEdfA9GnXSVg7roIwbuMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.1-rc.3": {"name": "@img/sharp-darwin-arm64", "version": "0.33.1-rc.3", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "926bf11e9281c5c1e0d308112d91754d209792ed", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.1-rc.3.tgz", "fileCount": 4, "integrity": "sha512-Ke2wNzaXiOqVCwIclX+c4IXkNWSPsdAupg41GRFQ58WFFEYQ4Vue1vm8DueBAA+MC65bMBBUTFPa058e4PARpw==", "signatures": [{"sig": "MEUCIGNRrCQYm2P5N7ixKhd4lKUudB+MU2Pt+knBNz61ceTsAiEAgfvsvmqL/sQ5YqsCy3QBP0AP8Vd9eR5STUUE5KLJBSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.1": {"name": "@img/sharp-darwin-arm64", "version": "0.33.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.0"}, "dist": {"shasum": "9d3cb0e4899b10b003608a018877b45b6db02861", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.1.tgz", "fileCount": 4, "integrity": "sha512-esr2BZ1x0bo+wl7Gx2hjssYhjrhUsD88VQulI0FrG8/otRQUOxLWHMBd1Y1qo2Gfg2KUvXNpT0ASnV9BzJCexw==", "signatures": [{"sig": "MEQCIElo+IqgMacttJENWfqXJWiy3HVBOM6K7RvlZ2uyRrQIAiApvK15YKM1Fhs6SfBp5PUks8rS9r84Adz+VjPrAOOU0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274085}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.2-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.1"}, "dist": {"shasum": "7dfeff953d9119a8d828b873eabe583da3851261", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-6b+tO29SbP9bpvy3Qoqqh/BfaPTH7ChOxYux9l40iFWi1a0f3yddlqUVVgNRRJ6vvIScA4RCjz16J1E5OhgJPg==", "signatures": [{"sig": "MEUCICqdOpyrbFsuWCKfFiOJccxki7Q2qptPPF9H5dGb3F2zAiEA2TcTB/kd7kUCOZK+xttEJ+lGIRVJXk3i250uJq8USOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.2-rc.1": {"name": "@img/sharp-darwin-arm64", "version": "0.33.2-rc.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.1"}, "dist": {"shasum": "ef14140b2351c44842e023dfc37acf0b4877d309", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.2-rc.1.tgz", "fileCount": 4, "integrity": "sha512-Ttd/SFm8SrX7KasYMqgP8YXUDgaLzs+MBdlrfs7jtdw+t6gctCM4uFKQygUk5bBE4k3jH4+6XvNb14VDCYUjpA==", "signatures": [{"sig": "MEUCIHFF2R+1bWeO1qr6hsREA1IcC2NsEXHj5hEuCz5ktM7zAiEA9X07mKEsMG91fz4ggkWTY9Ppd1JrNxNYPi7XCJDscGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274090}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.2": {"name": "@img/sharp-darwin-arm64", "version": "0.33.2", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.1"}, "dist": {"shasum": "0a52a82c2169112794dac2c71bfba9e90f7c5bd1", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.2.tgz", "fileCount": 4, "integrity": "sha512-itHBs1rPmsmGF9p4qRe++CzCgd+kFYktnsoR1sbIAfsRMrJZau0Tt1AH9KVnufc2/tU02Gf6Ibujx+15qRE03w==", "signatures": [{"sig": "MEUCIQCWsMaKehDKJ0wiF1BJUOQh+yBmdO3rjOPsY+5Tt+xdEAIgD97O/GokFdobBzJ+wwxSLV53a0uTlVtzT/o2vLpTSmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274085}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.3-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.2"}, "dist": {"shasum": "3afc358c0c8e8ed8c5e4a53c8ac8a31cc12c004b", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-165hmLgTIZA60Cz7jjblbRtRz3RJ7QMSX5LpBIHRSmYWvulAYu789WekFaRWUb6vwcFD8eUvAX8Yu/RtmR8tNA==", "signatures": [{"sig": "MEUCIF7YttSN8u4jM8LqofbJUX3FoDPUdxIHTTLMYNyIdh8wAiEAmy5rCiwTedf+jY5/whfSBMbv5ZSyUc5aH5OYgfxMUX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273242}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.3": {"name": "@img/sharp-darwin-arm64", "version": "0.33.3", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.2"}, "dist": {"shasum": "2bbf676be830c5a9ae7d9294f201c9151535badd", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.3.tgz", "fileCount": 4, "integrity": "sha512-FaNiGX1MrOuJ3hxuNzWgsT/mg5OHG/Izh59WW2mk1UwYHUwtfbhk5QNKYZgxf0pLOhx9ctGiGa2OykD71vOnSw==", "signatures": [{"sig": "MEQCID67MiS3K95pSU3KHRDLayWzovmgIzo1PSjAHDvxOPw9AiAP6f+uI2nQepmlvxB8LO7Qd9ZZU0ZJtQFeyZ8x5NdVdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273237}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.4-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.4-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.2"}, "dist": {"shasum": "9498fd1254972cc6ed72b0fb3d40bbdda4767396", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.4-rc.0.tgz", "fileCount": 4, "integrity": "sha512-Y3ucbP1hR4DmUljljaG0rdNYahg+8uQlkIe+3Dxa+v5CKk9KuSX1Hvi/2ZwNvhT3o2WfMBOoGdSLmdS/AcQH3A==", "signatures": [{"sig": "MEQCIGEBSRqHamxkYar7qGHW1nCc80BYIn3JMNq3IDPiPyw9AiA+CRalag84dIXWlRwFQ9/cALHrJCGEKUZv2g/toRY+Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273322}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.4": {"name": "@img/sharp-darwin-arm64", "version": "0.33.4", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.2"}, "dist": {"shasum": "a1cf4a7febece334f16e0328b9689f05797d7aec", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.4.tgz", "fileCount": 4, "integrity": "sha512-p0suNqXufJs9t3RqLBO6vvrgr5OhgbWp76s5gTRvdmxmuv9E1rcaqGUsl3l4mKVmXPkTkTErXediAui4x+8PSA==", "signatures": [{"sig": "MEUCIQCjq1M7N2XxzSUeX/cN9KDxw4+NpHOo48wlj/a38NkHQAIgHVyKkEyRr/Bg95z5xuMARbqytzgjKF/eJ5qDu5utzMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273317}, "engines": {"npm": ">=9.6.5", "node": "^18.17.0 || ^20.3.0 || >=21.0.0", "pnpm": ">=7.1.0", "yarn": ">=3.2.0", "glibc": ">=2.26"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.5-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.33.5-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.4"}, "dist": {"shasum": "1a652c820a7c3fbbd7aabad29d0d069696acd026", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5-rc.0.tgz", "fileCount": 4, "integrity": "sha512-lq9sVkXYulsxy6pQdlSGMfiqMpHbp2hCcUM2TZSJm3w2lqXNPJpaYozlKNUm/GgEpDykvO5Xmz3yRs0xxHdbtg==", "signatures": [{"sig": "MEUCIQDBZ9EHjxrvzdDJWs5jogEj5YgRxO/g2vTPjjf3QucI0gIgD908CAEG2kLu1Kl6dvxqWc1zVi8hIsluxpmrdf6+TQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273983}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.5-rc.1": {"name": "@img/sharp-darwin-arm64", "version": "0.33.5-rc.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.4"}, "dist": {"shasum": "22174f672ba63d290d774c54a85f3fa78ede7593", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5-rc.1.tgz", "fileCount": 4, "integrity": "sha512-cL5TRIXmDVLcjeTsPS8DN68ZyC/4ul1wwYPZGS0IzvhmZRqCB4q55EeLv72QewqYCxCVcN9q+W1JTkU1kW4jIw==", "signatures": [{"sig": "MEUCIEnGkf8FCP55uAH7VtubrwpyMcoxBIBgnyne4FamDNKhAiEApsINOh8/GR3/PNHRkaToi8ciz1lYV3ezRF2osRnHeuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273983}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.33.5": {"name": "@img/sharp-darwin-arm64", "version": "0.33.5", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.4"}, "dist": {"shasum": "ef5b5a07862805f1e8145a377c8ba6e98813ca08", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5.tgz", "fileCount": 4, "integrity": "sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==", "signatures": [{"sig": "MEUCIHvP+mIqyHfE5/M1QB0fPhOXLgLuHiuyUbLIEgLxVrn8AiEAv8mJcsfRUgJIh/oV6kXxd1kZqmYHebK8LWoacCYYbG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273978}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.0-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.34.0-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0-rc4"}, "dist": {"shasum": "ec7ab9d2d6486b11a493333c561c7b395ede517b", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.0-rc.0.tgz", "fileCount": 4, "integrity": "sha512-qbkTsi4yItqb+bvi3JVbgIVPixk4sz7r1F5aw80vzK+q0yHj9lTFYkqnqWhCGNggp3MZfwx4Gqre5M+Y9ITwwQ==", "signatures": [{"sig": "MEQCIEr2ZH1zr1tHK8FyT2L4iL2Whs5djH2bwXv4IcbBNtD4AiB+KtBriaMlmMmG1TlDefELmCki4ubBjejHO+EBH90hXw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 290467}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.0-rc.1": {"name": "@img/sharp-darwin-arm64", "version": "0.34.0-rc.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0-rc5"}, "dist": {"shasum": "a87a9068df2fe04076402d5179eb623c65b45222", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-7iq5tYHKbkgCdIXlbx/45HmriG0ECtPLd24bCrz8igLKShHaafXhWhkJ2it33bEPjb1QQRIACCl961XfztZFZQ==", "signatures": [{"sig": "MEQCICn6QkJFBgdpEWmSGnXysKLinTaDeJ8VAlxEus/6gge5AiAFISmtHKRPhHMUCYD259aGFYlUPUvX02rIVmqDhOyJHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 290899}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.0": {"name": "@img/sharp-darwin-arm64", "version": "0.34.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}, "dist": {"shasum": "3aaf703d024843e95ce67b2c3752f1d639c20efd", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.0.tgz", "fileCount": 4, "integrity": "sha512-BLT8CQ234EOJFN4NCAkZUkJr2lyXavD+aQH/Jc2skPqAJTMjKeH2BUulaZNkd4MJ9hcCicTdupcbCRg4bto0Ow==", "signatures": [{"sig": "MEUCIHS/0Od1eKwdPLKXyrub7gMnzPfHoexNtbJs8SdXhrF0AiEAgpRF8ji0Mc0D6ux5HbUyxQxaGqJNYH66MmhgyW+W/nM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 274074}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.1": {"name": "@img/sharp-darwin-arm64", "version": "0.34.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}, "dist": {"shasum": "e79a4756bea9a06a7aadb4391ee53cb154a4968c", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.1.tgz", "fileCount": 4, "integrity": "sha512-pn44xgBtgpEbZsu+lWf2KNb6OAf70X68k+yk69Ic2Xz11zHR/w24/U49XT7AeRwJ0Px+mhALhU5LPci1Aymk7A==", "signatures": [{"sig": "MEUCIDMa36ZkmR1BM/QzO0HS4tpIZYb5NpHb9RdC44zl0euCAiEAwZFw1Hf7ySPcEoXXc6dP0W5MadcN17ZTLsAy/59Z9to=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 274074}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.2-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.34.2-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}, "dist": {"shasum": "f1b98bcbe45979fef5e6ad84b17bd8733fe80e57", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.2-rc.0.tgz", "fileCount": 4, "integrity": "sha512-cC+u1taHaFr/1PJs4RPwdUrmdKbd4N9JR3wVvcV5bVJSD0eXwtmRdZ5ovph7u70/78pPD+HbiUT+OOQtwjrTmQ==", "signatures": [{"sig": "MEUCIDNlOqbWalC9Z7HhKJ1/mlkHlpTOqxHiwgiKbMQKhw6UAiEA+Ie0FNLeX74zKDe0bNK/1W8Q60u9m6k7SbjLukUlvqg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 274079}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.2": {"name": "@img/sharp-darwin-arm64", "version": "0.34.2", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}, "dist": {"shasum": "65049ef7c6be7857da742cd028f97602ce209635", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.2.tgz", "fileCount": 4, "integrity": "sha512-OfXHZPppddivUJnqyKoi5YVeHRkkNE2zUFT2gbpKxp/JZCFYEYubnMg+gOp6lWfasPrTS+KPosKqdI+ELYVDtg==", "signatures": [{"sig": "MEQCIBNPE6YBXgg7v40TjjiUiiJQoKhOI6FmIUi9yTrUP0U+AiB3SlLAg42XeWUPdqSbV1s+PEQgVGbxNh1kXBI5qcb/DA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 273962}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.3-rc.0": {"name": "@img/sharp-darwin-arm64", "version": "0.34.3-rc.0", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.2.0-rc.2"}, "dist": {"shasum": "ae65371af3436145234721d6ab9587ab3ef44f99", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.3-rc.0.tgz", "fileCount": 4, "integrity": "sha512-z0ExJCezrSMyTWDzZJCv41NHV6JTuGLDlsbGznPxqsWylKsBsRa0uTX7/6TECXeKMOMtwEG16xrhvUB4H0LurQ==", "signatures": [{"sig": "MEYCIQCCHncYmZ5S9/vyREvEgkpvvLnq131NSWrDSsIhHOC5pwIhAOYEr8BANgtpy2H9IEs9iT7XBwiJefcBNQm2+iF06tty", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 273940}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.3-rc.1": {"name": "@img/sharp-darwin-arm64", "version": "0.34.3-rc.1", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.2.0"}, "dist": {"shasum": "849bb681d3b8aefef04fbd3c05d2646f17c99868", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.3-rc.1.tgz", "fileCount": 4, "integrity": "sha512-x4+RIWt1UEDtq4kCSBkOH7owKOATlnQ6PH89FBJlTgBLoi+ZNVAoN029vBw/VLdOxEnUaudhVg2A9w3Gtd+OWQ==", "signatures": [{"sig": "MEUCIEszc0W9WCaL1ZWMMEXzTwveTN7SYlTdINnEcePWaYjyAiEAxkYbexsaNZFfYcArrEJn97lsTihixGvp8kcleqtCtaY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 274175}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}, "0.34.3": {"name": "@img/sharp-darwin-arm64", "version": "0.34.3", "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.2.0"}, "dist": {"integrity": "sha512-ryFMfvxxpQRsgZJqBd4wsttYQbCxsJksrv9Lw/v798JcQ8+w84mBWuXwl+TT0WJ/WrYOLaYpwQXi3sA9nTIaIg==", "shasum": "4850c8ace3c1dc13607fa07d43377b1f9aa774da", "tarball": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.3.tgz", "fileCount": 4, "unpackedSize": 274170, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGd/N9RTeF8eOwkU6iyvQtvaAj49yy7KDGCbYZq3V7kgAiEAmm7U5fu3jE2ADFuZsDwh/RS7weFtEhKQ95+B2sXc9Y0="}]}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "os": ["darwin"], "cpu": ["arm64"]}}, "modified": "2025-07-10T07:57:17.891Z"}