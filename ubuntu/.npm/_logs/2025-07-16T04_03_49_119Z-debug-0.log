0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@9.2.0
2 info using node@v18.19.1
3 timing npm:load:whichnode Completed in 0ms
4 timing config:load:defaults Completed in 2ms
5 timing config:load:file:/usr/share/nodejs/npm/npmrc Completed in 8ms
6 timing config:load:builtin Completed in 9ms
7 timing config:load:cli Completed in 2ms
8 timing config:load:env Completed in 2ms
9 timing config:load:file:/home/<USER>/trading-platform-manager/frontend/.npmrc Completed in 1ms
10 timing config:load:project Completed in 11ms
11 timing config:load:file:/home/<USER>/.npmrc Completed in 0ms
12 timing config:load:user Completed in 0ms
13 timing config:load:file:/etc/npmrc Completed in 1ms
14 timing config:load:global Completed in 2ms
15 timing config:load:setEnvs Completed in 2ms
16 timing config:load Completed in 30ms
17 timing npm:load:configload Completed in 31ms
18 timing npm:load:mkdirpcache Completed in 1ms
19 timing npm:load:mkdirplogs Completed in 0ms
20 verbose title npm start
21 verbose argv "start"
22 timing npm:load:setTitle Completed in 3ms
23 timing config:load:flatten Completed in 11ms
24 timing npm:load:display Completed in 12ms
25 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-16T04_03_49_119Z-
26 verbose logfile /home/<USER>/.npm/_logs/2025-07-16T04_03_49_119Z-debug-0.log
27 timing npm:load:logFile Completed in 15ms
28 timing npm:load:timers Completed in 0ms
29 timing npm:load:configScope Completed in 0ms
30 timing npm:load Completed in 63ms
31 silly logfile start cleaning logs, removing 1 files
32 timing config:load:flatten Completed in 1ms
33 silly logfile done cleaning log files
34 timing command:run-script Completed in 2448ms
35 timing command:start Completed in 2462ms
36 verbose exit 1
37 timing npm Completed in 2549ms
38 verbose code 1
