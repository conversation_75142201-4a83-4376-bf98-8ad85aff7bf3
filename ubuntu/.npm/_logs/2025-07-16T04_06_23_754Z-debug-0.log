0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@9.2.0
2 info using node@v18.19.1
3 timing npm:load:whichnode Completed in 1ms
4 timing config:load:defaults Completed in 3ms
5 timing config:load:file:/usr/share/nodejs/npm/npmrc Completed in 7ms
6 timing config:load:builtin Completed in 7ms
7 timing config:load:cli Completed in 4ms
8 timing config:load:env Completed in 1ms
9 timing config:load:file:/home/<USER>/trading-platform-manager/frontend/.npmrc Completed in 1ms
10 timing config:load:project Completed in 12ms
11 timing config:load:file:/home/<USER>/.npmrc Completed in 0ms
12 timing config:load:user Completed in 1ms
13 timing config:load:file:/etc/npmrc Completed in 2ms
14 timing config:load:global Completed in 2ms
15 timing config:load:setEnvs Completed in 3ms
16 timing config:load Completed in 34ms
17 timing npm:load:configload Completed in 34ms
18 timing npm:load:mkdirpcache Completed in 1ms
19 timing npm:load:mkdirplogs Completed in 2ms
20 verbose title npm run dev
21 verbose argv "run" "dev"
22 timing npm:load:setTitle Completed in 2ms
23 timing config:load:flatten Completed in 11ms
24 timing npm:load:display Completed in 12ms
25 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-16T04_06_23_754Z-
26 verbose logfile /home/<USER>/.npm/_logs/2025-07-16T04_06_23_754Z-debug-0.log
27 timing npm:load:logFile Completed in 14ms
28 timing npm:load:timers Completed in 0ms
29 timing npm:load:configScope Completed in 0ms
30 timing npm:load Completed in 68ms
31 timing config:load:flatten Completed in 2ms
32 silly logfile start cleaning logs, removing 1 files
33 silly logfile done cleaning log files
