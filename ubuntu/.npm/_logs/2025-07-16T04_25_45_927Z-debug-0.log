0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@9.2.0
2 info using node@v18.19.1
3 timing npm:load:whichnode Completed in 1ms
4 timing config:load:defaults Completed in 10ms
5 timing config:load:file:/usr/share/nodejs/npm/npmrc Completed in 17ms
6 timing config:load:builtin Completed in 19ms
7 timing config:load:cli Completed in 12ms
8 timing config:load:env Completed in 1ms
9 timing config:load:file:/home/<USER>/.cache/typescript/5.8/.npmrc Completed in 1ms
10 timing config:load:project Completed in 26ms
11 timing config:load:file:/home/<USER>/.npmrc Completed in 2ms
12 timing config:load:user Completed in 2ms
13 timing config:load:file:/etc/npmrc Completed in 3ms
14 timing config:load:global Completed in 3ms
15 timing config:load:setEnvs Completed in 6ms
16 timing config:load Completed in 82ms
17 timing npm:load:configload Completed in 82ms
18 timing npm:load:mkdirpcache Completed in 6ms
19 timing npm:load:mkdirplogs Completed in 4ms
20 verbose title npm install types-registry@latest
21 verbose argv "install" "--ignore-scripts" "types-registry@latest"
22 timing npm:load:setTitle Completed in 8ms
23 timing config:load:flatten Completed in 22ms
24 timing npm:load:display Completed in 27ms
25 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-16T04_25_45_927Z-
26 verbose logfile /home/<USER>/.npm/_logs/2025-07-16T04_25_45_927Z-debug-0.log
27 timing npm:load:logFile Completed in 56ms
28 timing npm:load:timers Completed in 0ms
29 timing npm:load:configScope Completed in 0ms
30 timing npm:load Completed in 189ms
31 timing config:load:flatten Completed in 1ms
32 timing arborist:ctor Completed in 6ms
33 silly logfile start cleaning logs, removing 1 files
34 silly logfile done cleaning log files
35 timing idealTree:init Completed in 462ms
36 timing arborist:ctor Completed in 0ms
37 http fetch GET 200 https://registry.npmjs.org/types-registry 308ms (cache hit)
38 timing idealTree:userRequests Completed in 379ms
39 silly idealTree buildDeps
40 silly fetch manifest types-registry@0.1.729
41 silly placeDep ROOT types-registry@0.1.729 REPLACE for:  want: 0.1.729
42 timing idealTree:#root Completed in 29ms
43 timing idealTree:node_modules/types-registry Completed in 0ms
44 timing idealTree:buildDeps Completed in 37ms
45 timing idealTree:fixDepFlags Completed in 1ms
46 timing idealTree Completed in 887ms
47 timing reify:loadTrees Completed in 893ms
48 timing reify:diffTrees Completed in 12ms
49 silly reify moves {}
50 timing reify:retireShallow Completed in 5ms
51 timing reify:createSparse Completed in 0ms
52 timing reify:loadBundles Completed in 0ms
53 silly audit bulk request {
53 silly audit   '@types/bcrypt': [ '5.0.2' ],
53 silly audit   '@types/body-parser': [ '1.19.6' ],
53 silly audit   '@types/bytes': [ '3.1.5' ],
53 silly audit   '@types/compressible': [ '2.0.3' ],
53 silly audit   '@types/compression': [ '1.8.1' ],
53 silly audit   '@types/connect': [ '3.4.38' ],
53 silly audit   '@types/cors': [ '2.8.19' ],
53 silly audit   '@types/debug': [ '4.1.12' ],
53 silly audit   '@types/express': [ '5.0.3' ],
53 silly audit   '@types/express-serve-static-core': [ '5.0.7' ],
53 silly audit   '@types/http-errors': [ '2.0.5' ],
53 silly audit   '@types/jsonwebtoken': [ '9.0.10' ],
53 silly audit   '@types/merge-descriptors': [ '1.0.3' ],
53 silly audit   '@types/mime': [ '1.3.5' ],
53 silly audit   '@types/mime-db': [ '1.43.6' ],
53 silly audit   '@types/ms': [ '2.1.0' ],
53 silly audit   '@types/negotiator': [ '0.6.4' ],
53 silly audit   '@types/node': [ '24.0.13' ],
53 silly audit   '@types/object-assign': [ '4.0.33' ],
53 silly audit   '@types/on-headers': [ '1.0.4' ],
53 silly audit   '@types/qs': [ '6.14.0' ],
53 silly audit   '@types/range-parser': [ '1.2.7' ],
53 silly audit   '@types/react': [ '19.1.8' ],
53 silly audit   '@types/react-dom': [ '19.1.6' ],
53 silly audit   '@types/send': [ '0.17.5' ],
53 silly audit   '@types/serve-static': [ '1.15.8' ],
53 silly audit   '@types/vary': [ '1.1.3' ],
53 silly audit   csstype: [ '3.1.3' ],
53 silly audit   'undici-types': [ '7.8.0' ],
53 silly audit   'types-registry': [ '0.1.729' ]
53 silly audit }
54 timing reify:unpack Completed in 12ms
55 timing reify:unretire Completed in 0ms
56 timing build:queue Completed in 0ms
57 timing build:deps Completed in 3ms
58 timing build Completed in 3ms
59 timing reify:build Completed in 5ms
60 timing reify:trash Completed in 0ms
61 timing reify:save Completed in 146ms
62 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 770ms
63 timing auditReport:getReport Completed in 781ms
64 silly audit report {}
65 timing auditReport:init Completed in 0ms
66 timing reify:audit Completed in 789ms
67 timing reify Completed in 1761ms
68 timing command:install Completed in 1801ms
69 verbose exit 0
70 timing npm Completed in 2114ms
71 info ok
